import { requestClient, sleep } from '#/api/request';

/**
 * 入库作业 入库订单相关API
 */
export async function getBWInOrderList(obj: object) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/inbound/order/getList', {
    params: obj,
  });
}

export async function bwInOrderCreate(params: object) {
  await sleep(100);
  return requestClient.post('/bondedwarehouse/inbound/order/create', params);
}

export async function bwInOrderDelete(id: string) {
  await sleep(100);
  return requestClient.post('/bondedwarehouse/inbound/order/del', { id });
}

export async function getInOrderHead(id: string) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/inbound/order/head/getData', {
    params: { id },
  });
}

export async function bwInOrderHeadUpdate(params: object) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/inbound/order/head/update',
    params,
  );
}

export async function getInOrderItem(id: string) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/inbound/order/item/getList', {
    params: { id },
  });
}

export async function bwInOrderInboundImport(id: string, ids: Array<string>) {
  await sleep(100);
  return requestClient.post('/bondedwarehouse/inbound/order/ibd/import', {
    id,
    ids,
  });
}

export async function getInOrderInbound(id: string) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/inbound/order/ibd/getList', {
    params: { id },
  });
}

export async function getInOrderInboundItem(id: string) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/inbound/order/ibd/item/getList', {
    params: { id },
  });
}

export async function bwInOrderInboundRemove(id: string, ids: Array<string>) {
  await sleep(100);
  return requestClient.post('/bondedwarehouse/inbound/order/ibd/remove', {
    id,
    ids,
  });
}

export async function bwInOrderInStatusCheck(id: string) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/inbound/order/instatus/check', {
    params: { id },
  });
}

export async function bwInOrderInStatusConfirm(params: object) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/inbound/order/instatus/confirm',
    params,
  );
}

export async function bwInOrderItemGetData(id: string) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/inbound/order/item/getData', {
    params: { id },
  });
}

export async function bwInOrderItemReceiving(params: object) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/inbound/order/item/receiving',
    params,
  );
}

export async function bwInOrderItemInvoiceUpdate(
  id: string,
  ids: Array<string>,
  value: string,
) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/inbound/order/item/invoice/update',
    { id, ids, value },
  );
}

export async function bwInOrderItemStatusUpdate(
  id: string,
  ids: Array<string>,
  value: string,
) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/inbound/order/item/status/update',
    { id, ids, value },
  );
}

export async function bwInOrderItemUpdate(params: object) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/inbound/order/item/update',
    params,
  );
}

export async function getInOrderItemSplit(id: string) {
  await sleep(100);
  return requestClient.get(
    '/bondedwarehouse/inbound/order/item/split/getList',
    {
      params: { id },
    },
  );
}

export async function bwInOrderItemSplitRepeal(id: string) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/inbound/order/item/split/repeal',
    { id },
  );
}

export async function getInOrderCSARList(id: string) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/inbound/order/csar/getList', {
    params: { id },
  });
}

export async function getInOrderCSARData(id: string) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/inbound/order/csar/getData', {
    params: { id },
  });
}

export async function bwInOrderCSARUpdate(params: object) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/inbound/order/csar/update',
    params,
  );
}

export async function bwInOrderInboundGRCheck(params: object) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/inbound/order/ibd/gr/check',
    params,
  );
}

export async function bwInOrderInboundGR(params: object) {
  await sleep(100);
  return requestClient.post('/bondedwarehouse/inbound/order/ibd/gr', params);
}

export async function bwInOrderUnilateralGR(id: string, ids: Array<string>) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/inbound/order/ibd/unilateral/gr',
    { id, ids },
  );
}
/* export async function getInOrderInboundList(id: string) {
  return requestClient.get('/bondedwarehouse/inbound/order/ibd/getList', {
    params: { id },
  });
}
 */

export async function getInboundBatchReceivingItem(id: string) {
  await sleep(100);
  return requestClient.get(
    '/bondedwarehouse/inbound/order/batchReceiving/getItem',
    {
      params: { id },
    },
  );
}

export async function getInboundBatchStatusErrItem(id: string) {
  await sleep(100);
  return requestClient.get(
    '/bondedwarehouse/inbound/order/batchStatusErr/getItem',
    {
      params: { id },
    },
  );
}

export async function bwInOrderStockSetup(params: object) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/inbound/order/stockSetup',
    params,
  );
}

export async function bwInOrderClosing(id: string) {
  await sleep(100);
  return requestClient.post('/bondedwarehouse/inbound/order/closing', { id });
}

export async function bwInOrderItemSplit(params: object) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/inbound/order/item/split',
    params,
  );
}
