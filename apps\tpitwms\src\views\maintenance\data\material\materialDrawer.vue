<script lang="ts" setup>
import { ref } from 'vue';

import { Fallback, useVbenDrawer } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter';
import { getMaterialData, materialUpdate } from '#/api/maintenance/data';

const emit = defineEmits(['success']);

const data = ref();

const isNotFound = ref(false);
const [Form, baseFormApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'pb-1',
  },
  // 提交函数
  handleSubmit: onSubmit,
  // 垂直布局，label和input在不同行，值为vertical
  // 水平布局，label和input在同一行
  layout: 'horizontal',
  schema: [
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 16,
        placeholder: '请输入',
        disabled: true,
      },
      fieldName: 'MatCode',
      /* formItemClass: 'col-span-1 md:col-span-2', */
      help: '无法修改',
      label: '物料编号',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 16,
        placeholder: '请输入',
      },
      fieldName: 'BarCode',
      formItemClass: 'col-span-1 md:col-span-2',
      help: '仅供临时修改，会被产品主数据更新覆盖',
      label: '物料条码',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        placeholder: '请选择',
        getPopupContainer: () => document.body,
      },
      fieldName: 'MatType',
      help: '仅供临时修改，会被产品主数据更新覆盖',
      label: '物料类型',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        placeholder: '请选择',
        formItemClass: 'col-span-1 md:col-span-2',
        getPopupContainer: () => document.body,
      },
      fieldName: 'BrandCode',
      help: '仅供临时修改，会被产品主数据更新覆盖',
      label: '品牌',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        placeholder: '请选择',
        getPopupContainer: () => document.body,
      },
      fieldName: 'Origin',
      help: '仅供临时修改，会被产品主数据更新覆盖',
      label: '原产国',
    },
    {
      component: 'InputNumber',
      componentProps: {
        autocomplete: 'off',
        min: 0,
        placeholder: '请输入',
      },
      fieldName: 'GS1NetContentMetric',
      help: '仅供临时修改，会被产品主数据更新覆盖',
      label: '规格数值',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        placeholder: '请选择',
        getPopupContainer: () => document.body,
      },
      fieldName: 'GS1NetContentMetricUnit',
      help: '仅供临时修改，会被产品主数据更新覆盖',
      label: '规格单位',
    },
    {
      component: 'InputNumber',
      componentProps: {
        autocomplete: 'off',
        maxlength: 6,
        min: 0,
        placeholder: '请输入',
      },
      defaultValue: 0,
      fieldName: 'PCB',
      help: '仅供临时修改，会被产品主数据更新覆盖',
      label: 'PCB',
    },
    {
      component: 'InputNumber',
      componentProps: {
        autocomplete: 'off',
        maxlength: 6,
        min: 0,
        placeholder: '请输入',
      },
      defaultValue: 0,
      fieldName: 'SPCB',
      help: '仅供临时修改，会被产品主数据更新覆盖',
      label: 'SPCB',
    },
    {
      component: 'InputNumber',
      componentProps: {
        autocomplete: 'off',
        maxlength: 6,
        min: 0,
        placeholder: '请输入',
      },
      defaultValue: 0,
      fieldName: 'layerSupportQuantity',
      help: '仅供临时修改，会被产品主数据更新覆盖',
      label: '层规数量',
    },
    {
      component: 'InputNumber',
      componentProps: {
        autocomplete: 'off',
        maxlength: 6,
        min: 0,
        placeholder: '请输入',
      },
      defaultValue: 0,
      fieldName: 'palletSupportQuantity',
      help: '仅供临时修改，会被产品主数据更新覆盖',
      label: '托规数量',
    },
    {
      component: 'Textarea',
      componentProps: {
        autocomplete: 'off',
        autosize: { minRows: 1, maxRows: 2 },
        maxlength: 128,
        placeholder: '请输入',
      },
      fieldName: 'CName',
      formItemClass: 'col-span-1 md:col-span-3',
      help: '仅供临时修改，会被产品主数据更新覆盖',
      label: '中文品名',
    },
    {
      component: 'Textarea',
      componentProps: {
        autocomplete: 'off',
        autosize: { minRows: 1, maxRows: 2 },
        maxlength: 128,
        placeholder: '请输入',
      },
      fieldName: 'EName',
      formItemClass: 'col-span-1 md:col-span-3',
      help: '仅供临时修改，会被产品主数据更新覆盖',
      label: '英文品名',
    },
    {
      component: 'Divider',
      fieldName: 'D1',
      label: '物料扩展信息',
      formItemClass: 'col-span-1 md:col-span-3',
    },
    {
      component: 'Switch',
      componentProps: {
        class: 'w-auto',
        checkedChildren: '是',
        unCheckedChildren: '否',
      },
      fieldName: 'IsCareful',
      label: '是否精细理货',
    },
    {
      component: 'InputNumber',
      componentProps: {
        autocomplete: 'off',
        maxlength: 6,
        min: 0,
        placeholder: '请输入',
      },
      defaultValue: 0,
      fieldName: 'PCLInWMS',
      label: '实际层规',
    },
    {
      component: 'InputNumber',
      componentProps: {
        autocomplete: 'off',
        maxlength: 6,
        min: 0,
        placeholder: '请输入',
      },
      defaultValue: 0,
      fieldName: 'PCPInWMS',
      label: '实际托规',
    },
    {
      component: 'AutoComplete',
      componentProps: {
        filterOption: true,
        getPopupContainer: () => document.body,
      },
      fieldName: 'HsCode',
      label: 'Hs编码',
    },
    {
      component: 'Textarea',
      componentProps: {
        autocomplete: 'off',
        autosize: { minRows: 1, maxRows: 2 },
        maxlength: 255,
        placeholder: '请输入',
      },
      fieldName: 'GModel',
      formItemClass: 'col-span-1 md:col-span-2',
      label: '申报要素',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        placeholder: '请选择',
        getPopupContainer: () => document.body,
      },
      fieldName: 'MatUnit',
      label: '物料单位',
    },
    {
      component: 'InputNumber',
      componentProps: {
        autocomplete: 'off',
        maxlength: 6,
        min: 0,
        placeholder: '请输入',
      },
      defaultValue: 0,
      fieldName: 'Price',
      label: '单价',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        placeholder: '请选择',
        getPopupContainer: () => document.body,
      },
      fieldName: 'Curr',
      label: '币制',
    },
    {
      component: 'DatePicker',
      componentProps: {},
      fieldName: 'HPDeadline',
      label: 'HP限期',
    },
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 64,
        placeholder: '请输入',
      },
      fieldName: 'HPNo',
      formItemClass: 'col-span-1 md:col-span-2',
      label: '备案文号',
    },
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 128,
        placeholder: '请输入',
      },
      fieldName: 'Remark',
      formItemClass: 'col-span-1 md:col-span-3',
      label: '物料备注',
    },
    {
      component: 'Divider',
      fieldName: 'D1',
      label: '标签/加工信息',
      formItemClass: 'col-span-1 md:col-span-3',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        placeholder: '请选择',
        getPopupContainer: () => document.body,
      },
      fieldName: 'PackingMethod',
      label: '包装方式',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        placeholder: '请选择',
        getPopupContainer: () => document.body,
        mode: 'tags',
        maxTagCount: 2,
      },
      fieldName: 'PackageType',
      formItemClass: 'col-span-1 md:col-span-2',
      label: '包装大类',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        placeholder: '请选择',
        getPopupContainer: () => document.body,
      },
      fieldName: 'LabType',
      label: '标签类型',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        placeholder: '请选择',
        getPopupContainer: () => document.body,
      },
      fieldName: 'LabColor',
      label: '标签颜色',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        placeholder: '请选择',
        getPopupContainer: () => document.body,
      },
      fieldName: 'LabMaterial',
      label: '标签材质',
    },
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入',
        maxlength: 128,
      },
      fieldName: 'LabSize',
      formItemClass: 'col-span-1 md:col-span-3',
      label: '标签尺寸',
    },
    {
      component: 'InputNumber',
      componentProps: {
        autocomplete: 'off',
        max: 9,
        min: 0,
        placeholder: '请输入',
      },
      defaultValue: 0,
      fieldName: 'ExtLabNum',
      label: '外标数量',
    },
    {
      component: 'InputNumber',
      componentProps: {
        autocomplete: 'off',
        max: 9,
        min: 0,
        placeholder: '请输入',
      },
      defaultValue: 0,
      fieldName: 'InLabNum',
      label: '内标数量',
    },
    {
      component: 'InputNumber',
      componentProps: {
        autocomplete: 'off',
        max: 9,
        min: 0,
        placeholder: '请输入',
      },
      defaultValue: 0,
      fieldName: 'SealingStickerNum',
      label: '封口贴数量',
    },
    {
      component: 'InputNumber',
      componentProps: {
        autocomplete: 'off',
        max: 9,
        min: 0,
        placeholder: '请输入',
      },
      defaultValue: 0,
      fieldName: 'QRCodeNum',
      label: '二维码数量',
    },
    {
      component: 'InputNumber',
      componentProps: {
        autocomplete: 'off',
        max: 9,
        min: 0,
        placeholder: '请输入',
      },
      defaultValue: 0,
      fieldName: 'OutBoxLabelNum',
      label: '外箱贴数量',
    },
    {
      component: 'InputNumber',
      componentProps: {
        autocomplete: 'off',
        max: 9,
        min: 0,
        placeholder: '请输入',
      },
      defaultValue: 0,
      fieldName: 'PlasticPackagingNum',
      label: '塑封数量',
    },
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入',
        maxlength: 64,
      },
      fieldName: 'PlaSize',
      formItemClass: 'col-span-1 md:col-span-3',
      label: '塑封尺寸',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        placeholder: '请选择',
        getPopupContainer: () => document.body,
        mode: 'tags',
        maxTagCount: 4,
      },
      fieldName: 'Special',
      formItemClass: 'col-span-1 md:col-span-3',
      label: '特殊操作',
    },
  ],
  showDefaultActions: false,
  wrapperClass: 'grid-cols-1 md:grid-cols-3',
});
const [Drawer, drawerApi] = useVbenDrawer({
  class: 'w-[760px]',
  onCancel() {
    drawerApi.close();
  },
  onConfirm() {
    /* console.log(baseFormApi.validate()); */
    baseFormApi.submitForm();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      drawerApi.setState({ title: '编辑物料信息' });
      isNotFound.value = false;
      data.value = drawerApi.getData<Record<string, any>>();
      baseFormApi.updateSchema([
        {
          componentProps: {
            disabled: !(data.value.id === 'new'),
          },
          fieldName: 'BrandName',
        },
      ]);
      handleGetData(data.value.id);
    }
  },
});
function handleGetData(id: string) {
  drawerApi.setState({ loading: true });
  getMaterialData(id)
    .then((res) => {
      baseFormApi.setValues(res.info);

      const fieldList = [
        'BrandCode',
        'MatType',
        'PackageType',
        'MatUnit',
        'Origin',
        'HsCode',
        'Curr',
        'LabType',
        'LabColor',
        'LabMaterial',
        'PackingMethod',
        'Special',
        'GS1NetContentMetricUnit',
      ];
      fieldList.forEach((field) => {
        baseFormApi.updateSchema([
          {
            componentProps: {
              options: res.options?.filter((item: any) => {
                return item.type === field;
              }),
            },
            fieldName: field,
          },
        ]);
      });
    })
    .catch(() => {
      isNotFound.value = true;
    })
    .finally(() => {
      drawerApi.setState({ loading: false });
    });
}
async function onSubmit(values: Record<string, any>) {
  const check = await baseFormApi.validate();
  if (check.valid) {
    drawerApi.setState({ confirmLoading: true });
    drawerApi.setState({ loading: true });
    materialUpdate(Object.assign({ id: data.value.id }, values))
      .then(() => {
        message.success('操作成功');
        drawerApi.close();
        emit('success');
      })
      .catch(() => {})
      .finally(() => {
        drawerApi.setState({ loading: false });
        drawerApi.setState({ confirmLoading: false });
      });
  }
}
</script>
<template>
  <Drawer>
    <Fallback v-if="isNotFound" :show-back="false" status="404" />
    <Form v-else />
  </Drawer>
</template>

<style scoped lang="less">
::v-deep(.ant-input[disabled]) {
  color: rgb(0 0 0 / 70%);
}
::v-deep(input[disabled]) {
  color: rgb(0 0 0 / 70%) !important;
}
</style>
