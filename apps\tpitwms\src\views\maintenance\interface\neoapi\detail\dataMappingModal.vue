<script lang="ts" setup>
import type { VxeGridListeners, VxeGridProps } from '#/adapter';

import { ref } from 'vue';

import { Fallback, useVbenModal } from '@vben/common-ui';

import { message, Modal } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import {
  apiFieldsDelete,
  apiMappingFiledsUpdate,
  apiMappingNodesUpdate,
  apiNodesDelete,
  ckeckNEOAPIID,
  getNEOAPIMappingFields,
  getNEOAPIMappingNodes,
} from '#/api/maintenance/interface';

/* import nodesModal from './nodesModal.vue'; */

defineOptions({
  name: 'DataMappingModal',
});

const data = ref();
const isNotFound = ref(false);
const mappingNodesOptions = ref([]) as any;
const fieldTypeOptions = ref([]) as any;
/* const [NodesModal, nodesModalApi] = useVbenModal({
  connectedComponent: nodesModal,
}); */
const gridOptions: VxeGridProps = {
  columns: [
    { title: '#', type: 'seq', width: 48 },
    {
      editRender: { name: 'ASelect' },
      field: 'MappingNode',
      title: 'MappingNode.',
      width: 160,
      slots: { edit: 'mappingNode_edit' },
    },
    {
      editRender: { name: 'ASelect' },
      field: 'FieldType',
      title: 'FieldType',
      width: 160,
      slots: { edit: 'fieldType_edit' },
      formatter({ cellValue }) {
        if (!cellValue) {
          return undefined;
        }
        const { label } = fieldTypeOptions.value.find(
          (item: any) => item.value === cellValue,
        );
        return label || cellValue;
      },
    },
    {
      align: 'left',
      editRender: {
        name: 'AInput',
        props: { autocomplete: 'off', maxlength: 32 },
      },
      field: 'FieldName',
      title: 'FieldName',
      width: 200,
    },
    {
      align: 'left',
      editRender: {
        name: 'AInput',
        props: { autocomplete: 'off', maxlength: 1000 },
      },
      field: 'FieldXPath',
      title: 'FieldXPath',
      minWidth: 240,
    },
    { slots: { default: 'action' }, title: 'Action', width: 160 },
  ],
  customConfig: {
    storage: false,
  },
  editConfig: {
    mode: 'row',
    trigger: 'manual',
    autoClear: false,
    showStatus: true,
  },
  editRules: {
    MappingNode: [{ required: true, message: 'MappingNode is required' }],
    FieldType: [{ required: true, message: 'FieldType is required' }],
    FieldName: [{ required: true, message: 'FieldName is required' }],
    FieldXPath: [{ required: true, message: 'FieldXPath is required' }],
  },
  keepSource: true,
  layouts: ['Top', 'Toolbar', 'Table'],
  minHeight: 480,
  height: 'auto',
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    autoLoad: false,
    ajax: {
      query: async () => {
        const fieldsTableData = await getNEOAPIMappingFields(data.value.apiID);
        fieldTypeOptions.value = fieldsTableData.options;
        return fieldsTableData.items;
      },
    },
  },
  rowConfig: {
    useKey: true,
    keyField: 'Guid',
  },
  showOverflow: true,
  toolbarConfig: {
    custom: false,
    slots: {
      buttons: 'toolbar_left',
    },
  },
  menuConfig: {
    trigger: 'cell',
    body: {
      options: [
        [
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuCopy',
            disabled: false,
            name: '复制单元格',
            prefixConfig: {
              icon: 'vxe-icon-copy',
            },
            visible: true,
          },
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuRefresh',
            disabled: false,
            name: '刷新',
            prefixConfig: { icon: 'vxe-icon-refresh' },
            visible: true,
          },
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuExport',
            disabled: false,
            name: '导出明细',
            prefixConfig: { icon: 'vxe-icon-download' },
            visible: true,
          },
        ],
      ],
    },
    className: 'font-medium shadow rounded-md',
  },
};
const nodeGridOptions: VxeGridProps = {
  columns: [
    {
      field: 'NodeLevel',
      title: 'Level.',
      width: 80,
    },
    {
      field: 'ParentNode',
      title: 'ParentNode',
      width: 200,
      editRender: { name: 'ASelect' },
      slots: { edit: 'parentNode_edit' },
    },
    {
      editRender: {
        name: 'AInput',
        props: { autocomplete: 'off', maxlength: 48 },
      },
      field: 'MappingTable',
      title: 'MappingTable',
      width: 200,
    },
    {
      align: 'left',
      editRender: {
        name: 'AInput',
        props: { autocomplete: 'off', maxlength: 32 },
      },
      field: 'NodeName',
      title: 'NodeName',
      width: 200,
      treeNode: true,
    },
    {
      align: 'left',
      editRender: {
        name: 'AInput',
        props: { autocomplete: 'off', maxlength: 255 },
      },
      field: 'NodeXPath',
      title: 'NodeXPath',
      minWidth: 240,
    },
    { slots: { default: 'nodeAction' }, title: 'Action', width: 160 },
  ],
  customConfig: {
    storage: false,
  },
  editConfig: {
    mode: 'row',
    trigger: 'manual',
    autoClear: false,
    showStatus: true,
  },
  editRules: {
    NodeName: [{ required: true, message: 'NodeName is required' }],
    NodeXPath: [{ required: true, message: 'NodeXPath is required' }],
    MappingTable: [{ required: true, message: 'MappingTable is required' }],
  },
  keepSource: true,
  minHeight: 220,
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    autoLoad: false,
    ajax: {
      query: async () => {
        const nodesTableData = await getNEOAPIMappingNodes(data.value.apiID);
        mappingNodesOptions.value = nodesTableData.map((item: any) => {
          return { label: item.NodeName, value: item.Guid, key: item.NodeKey };
        });
        return nodesTableData;
      },
    },
  },
  rowConfig: {
    useKey: true,
    keyField: 'Guid',
  },
  stripe: false,
  showOverflow: true,
  toolbarConfig: {
    custom: false,
    slots: {
      buttons: 'toolbar_left',
    },
  },
  treeConfig: {
    transform: true,
    rowField: 'Guid',
    parentField: 'ParentID',
    showLine: true,
    expandAll: true,
  },
};
const gridEvents: VxeGridListeners = {
  menuClick({ menu }) {
    switch (menu.code) {
      case 'menuExport': {
        handleExport();
        break;
      }
      default: {
        break;
      }
    }
  },
};
function handleExport() {
  gridApi.grid.exportData({
    filename: `FieldMapping_${data.value.apiName}`,
    original: false,
    sheetName: 'Sheet1',
    type: 'xlsx',
    isFooter: false,
    columnFilterMethod: ({ column }) => {
      return column.field !== undefined;
    },
  });
}
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions, gridEvents });
const [NodeGrid, nodeGridApi] = useVbenVxeGrid({
  gridOptions: nodeGridOptions,
  gridClass: 'px-0 py-0',
});
const [BasicModal, modalApi] = useVbenModal({
  class: 'w-full h-full',
  fullscreenButton: true,
  closeOnClickModal: false,
  /* fullscreen: true, */
  /* showConfirmButton: false, */
  footer: false,
  zIndex: 900,
  onCancel() {
    modalApi.close();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      isNotFound.value = false;
      data.value = modalApi.getData<Record<string, any>>();
      modalApi.setState({ title: `DataMapping - ${data.value.apiName}` });
      handleGetData();
    }
  },
  title: `DataMapping`,
});
async function handleGetData() {
  modalApi.setState({ confirmLoading: true });
  modalApi.setState({ loading: true });
  ckeckNEOAPIID(data.value.apiID)
    .then(() => {})
    .catch(() => {
      isNotFound.value = true;
    })
    .finally(() => {
      if (!isNotFound.value) {
        handleSuccess();
      }
      modalApi.setState({ confirmLoading: false });
      modalApi.setState({ loading: false });
    });
}
/* function openNodesModal(id: string) {
  nodesModalApi.setData({
    id,
    apiID: data.value.apiID,
    nodesOptions: mappingNodesOptions.value,
  });
  nodesModalApi.open();
} */
function handleSuccess() {
  gridApi.grid.commitProxy('query');
  nodeGridApi.grid.commitProxy('query');
}
function hasEditStatus(isNodeRow: boolean, row: any) {
  return isNodeRow
    ? nodeGridApi.grid?.isEditByRow(row)
    : gridApi.grid?.isEditByRow(row);
}
function editRowEvent(isNodeRow: boolean, row: any) {
  if (isNodeRow) {
    nodeGridApi.grid?.setEditRow(row);
  } else {
    gridApi.grid?.setEditRow(row);
  }
}
const cancelRowEvent = (isNodeRow: boolean, row: any) => {
  if (isNodeRow) {
    if (row.Guid === 'New') {
      nodeGridApi.grid?.remove(row);
    } else {
      nodeGridApi.grid?.clearEdit();
      nodeGridApi.grid?.revertData(row);
    }
  } else {
    if (row.Guid === 'New') {
      gridApi.grid?.remove(row);
    } else {
      gridApi.grid?.clearEdit();
      gridApi.grid?.revertData(row);
    }
  }
};
function showDeleteModal(row: any) {
  Modal.confirm({
    content: `请确认是否删除字段：${row.FieldName}?`,
    onCancel() {},
    onOk() {
      apiFieldsDelete(row.Guid)
        .then(() => {
          message.success('操作成功');
          handleSuccess();
        })
        .catch(() => {})
        .finally(() => {});
    },
    title: '操作确认',
  });
}
function showDeleteModalNode(row: any) {
  Modal.confirm({
    content: `该节点下的所有字段将同步删除！`,
    onCancel() {},
    onOk() {
      handleDeleteNode(row.Guid);
    },
    title: `请确认是否删除节点：${row.NodeName}？`,
  });
}
function handleDeleteNode(id: string) {
  apiNodesDelete(id)
    .then(() => {
      message.success('操作成功');
      handleSuccess();
    })
    .catch(() => {})
    .finally(() => {});
}

/* function editRowEvent(row: any) {
  gridApi.grid?.setEditRow(row);
} */
async function saveRowEvent(row: any) {
  const errMap = await gridApi.grid?.validate();
  if (errMap) {
    return;
  }
  gridApi.setLoading(true);
  apiMappingFiledsUpdate(
    Object.assign({ id: row.Guid, APIID: data.value.apiID }, row),
  )
    .then(() => {
      gridApi.grid?.clearEdit();
      handleSuccess();
      message.success('操作成功');
    })
    .catch(() => {})
    .finally(() => {
      gridApi.setLoading(false);
    });
}
async function saveRowEventNode(row: any) {
  const errMap = await nodeGridApi.grid?.validate();
  if (errMap) {
    return;
  }
  nodeGridApi.setLoading(true);
  apiMappingNodesUpdate(
    Object.assign({ id: row.Guid, APIID: data.value.apiID }, row),
  )
    .then(() => {
      nodeGridApi.grid?.clearEdit();
      handleSuccess();
      message.success('操作成功');
    })
    .catch(() => {})
    .finally(() => {
      nodeGridApi.setLoading(false);
    });
}
async function handleAddFieldRow() {
  if (gridApi.grid && !gridApi.grid.getEditRecord()) {
    const { row: newRow } = await gridApi.grid.insertAt({ Guid: 'New' }, -1);
    gridApi.grid?.setEditRow(newRow);
    setTimeout(() => {
      gridApi.grid.scrollToRow(newRow);
    }, 100);
  }
}
async function handleAddNodeRow() {
  if (nodeGridApi.grid && !nodeGridApi.grid.getEditRecord()) {
    const { row: newRow } = await nodeGridApi.grid.insertAt(
      { Guid: 'New' },
      -1,
    );
    nodeGridApi.grid?.setEditRow(newRow);
  }
}
</script>
<template>
  <BasicModal>
    <Fallback v-if="isNotFound" :show-back="false" status="404" />
    <Grid v-else>
      <template #top>
        <NodeGrid>
          <template #toolbar_left>
            <span class="text-lg font-medium">Nodes</span>
          </template>
          <template #toolbar-tools>
            <a-button class="mr-3" type="primary" @click="handleAddNodeRow()">
              Add Node
            </a-button>
          </template>
          <template #parentNode_edit="{ row }">
            <a-select
              v-model:value="row.ParentID"
              :options="
                mappingNodesOptions.map((item: any) => {
                  return {
                    label: item.label,
                    value: item.value,
                    disabled:
                      item.value === row.Guid ||
                      item.key.split('/').includes(row.NodeName),
                  };
                })
              "
              allow-clear
            />
          </template>
          <template #nodeAction="{ row }">
            <template v-if="hasEditStatus(true, row)">
              <a-button type="link" @click="saveRowEventNode(row)">
                Save
              </a-button>
              <a-button type="link" @click="cancelRowEvent(true, row)">
                Cancel
              </a-button>
            </template>
            <template v-else>
              <a-button type="link" @click="editRowEvent(true, row)">
                Edit
              </a-button>
              <a-button danger type="link" @click="showDeleteModalNode(row)">
                Delete
              </a-button>
            </template>
          </template>
        </NodeGrid>
      </template>
      <template #toolbar_left>
        <span class="text-lg font-medium">Fields</span>
      </template>
      <template #toolbar-tools>
        <a-button class="mr-3" type="primary" @click="handleAddFieldRow()">
          Add Field
        </a-button>
      </template>
      <template #action="{ row }">
        <template v-if="hasEditStatus(false, row)">
          <a-button type="link" @click="saveRowEvent(row)"> Save </a-button>
          <a-button type="link" @click="cancelRowEvent(false, row)">
            Cancel
          </a-button>
        </template>
        <template v-else>
          <a-button type="link" @click="editRowEvent(false, row)">
            Edit
          </a-button>
          <a-button danger type="link" @click="showDeleteModal(row)">
            Delete
          </a-button>
        </template>
      </template>
      <template #mappingNode_edit="{ row }">
        <a-select
          v-model:value="row.NodeID"
          :options="mappingNodesOptions"
          allow-clear
          @change="
            (_e: any, option: any) => {
              if (option) {
                row.MappingNode = option.label;
              } else {
                row.MappingNode = undefined;
              }
            }
          "
        />
      </template>
      <template #fieldType_edit="{ row }">
        <a-select
          v-model:value="row.FieldType"
          :options="fieldTypeOptions"
          allow-clear
        />
      </template>
    </Grid>
  </BasicModal>
</template>
