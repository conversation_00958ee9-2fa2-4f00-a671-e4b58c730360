import { requestClient, sleep } from '#/api/request';

/**
 * 留样装箱 相关API
 */

export async function bwOutSamplePackingGetDoc() {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/outbound/sample/packing/getDoc');
}

export async function bwOutSamplePackingCreate(params: object) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/outbound/sample/packing/create',
    params,
  );
}

export async function bwOutSamplePackingDelete(id: string) {
  await sleep(100);
  return requestClient.post('/bondedwarehouse/outbound/sample/packing/del', {
    id,
  });
}

export async function getOutSamplePackingList(obj: object) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/outbound/sample/packing/getList', {
    params: obj,
  });
}

export async function getOutSamplePackingHead(id: string) {
  await sleep(100);
  return requestClient.get(
    '/bondedwarehouse/outbound/sample/packing/head/getData',
    {
      params: { id },
    },
  );
}

export async function checkSamplePacking(id: string) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/sample/packing/check', {
    params: { id },
  });
}

export async function bwOutSamplePackingHeadUpdate(params: object) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/outbound/sample/packing/head/update',
    params,
  );
}

export async function getOutSamplePackingItem(id: string) {
  await sleep(100);
  return requestClient.get(
    '/bondedwarehouse/outbound/sample/packing/item/getList',
    {
      params: { id },
    },
  );
}

export async function getOutSampleUnPacking(obj: object) {
  await sleep(100);
  return requestClient.get(
    '/bondedwarehouse/outbound/sample/packing/unPacking',
    {
      params: obj,
    },
  );
}

export async function bwOutSampleItemLoading(
  id: string,
  ids: Array<string>,
  values: Array<object>,
) {
  await sleep(100);
  return requestClient.post('/bondedwarehouse/outbound/sample/item/loading', {
    id,
    ids,
    values,
  });
}

export async function bwOutSampleIteRemove(id: string, ids: Array<string>) {
  await sleep(100);
  return requestClient.post('/bondedwarehouse/outbound/sample/item/remove', {
    id,
    ids,
  });
}
