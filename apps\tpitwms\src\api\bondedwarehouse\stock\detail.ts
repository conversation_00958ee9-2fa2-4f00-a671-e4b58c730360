import { requestClient, sleep } from '#/api/request';

/**
 * 库存管理 库存明细相关API
 */
export async function getBWStockDetailList(obj: object) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/stock/detail/getList', {
    params: obj,
  });
}

export async function getBWStockFlowByDoc(id: string) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/stock/flow/doc/getList', {
    params: { id },
  });
}
