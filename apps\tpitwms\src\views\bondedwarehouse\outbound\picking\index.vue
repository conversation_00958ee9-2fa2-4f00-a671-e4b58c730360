<script setup lang="ts">
import type { VxeGridListeners, VxeGridProps } from '#/adapter';

import { onMounted, reactive } from 'vue';
import { useRouter } from 'vue-router';

import { Page, useVbenModal } from '@vben/common-ui';
import { AntClear, AntDelete } from '@vben/icons';

import { message, Modal, Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import {
  bwOutPickingDelete,
  getBWOutPickingList,
} from '#/api/bondedwarehouse/outbound';
import { getOptions } from '#/api/common';

import createModal from './createModal.vue';

const router = useRouter();
const [CreateModal, createModalApi] = useVbenModal({
  connectedComponent: createModal,
});
const searchField: any = reactive({
  PickingDocNo: undefined,
});
const gridOptions = reactive<VxeGridProps>({
  id: 'BWOutPicking',
  columns: [
    {
      fixed: 'left',
      title: '#',
      type: 'seq',
      width: 60,
    },
    {
      field: 'PickingDocNo',
      title: '拣货单号',
      minWidth: 150,
      fixed: 'left',
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'PickingType',
      title: '拣货类型',
      minWidth: 108,
      fixed: 'left',
      formatter({ cellValue }) {
        return cellValue === 'Normal' ? '正常拣货 By BL' : '留样拣货';
      },
      filters: [
        { label: '正常拣货 By BL', value: 'Normal' },
        { label: '留样拣货', value: 'Sample' },
      ],
      filterMultiple: false,
    },
    {
      field: 'IsClosed',
      title: '单据状态',
      minWidth: 88,
      filters: [
        { label: '未关闭', value: false },
        { label: '已关闭', value: true },
      ],
      filterMultiple: false,
      slots: { default: 'docStatus' },
    },
    {
      field: 'BLNo',
      title: '出库提单BL',
      minWidth: 150,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      visible: false,
    },
    /* {
      field: 'OutDeliveryType',
      title: '订单类型',
      minWidth: 100,
      formatter({ row }) {
        return row.OutDeliveryTypeName;
      },
      filters: [],
    }, */
    { field: 'OwnerShortName', title: '货主', minWidth: 108, filters: [] },
    { field: 'WarehouseName', title: '仓库', minWidth: 108, filters: [] },
    {
      field: 'YUB',
      title: 'SAP销售订单',
      minWidth: 120,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'PackagingStatus',
      title: '打包状态',
      minWidth: 88,
      formatter({ cellValue }) {
        return cellValue === 1 ? '已完成' : '未完成';
      },
      filters: [
        { label: '已完成', value: 1 },
        { label: '未完成', value: 0 },
      ],
      filterMultiple: false,
      slots: { default: 'packagingStatus' },
    },
    {
      field: 'LoadingStatus',
      title: '装车状态',
      minWidth: 88,
      formatter({ cellValue }) {
        return cellValue === 1 ? '已完成' : '未完成';
      },
      filters: [
        { label: '已完成', value: 1 },
        { label: '未完成', value: 0 },
      ],
      filterMultiple: false,
      slots: { default: 'loadingStatus' },
    },
    {
      field: 'DeliveryStatus',
      title: '发货状态',
      minWidth: 88,
      formatter({ cellValue }) {
        return cellValue === 1 ? '已完成' : '未完成';
      },
      filters: [
        { label: '已完成', value: 1 },
        { label: '未完成', value: 0 },
      ],
      filterMultiple: false,
      slots: { default: 'deliveryStatus' },
    },
    {
      field: 'GoodsNum',
      title: '产品总数',
      minWidth: 108,
      filters: [{ data: { type: '0', num1: undefined, num2: undefined } }],
      filterRender: { name: 'FilterNumberRange' },
    },
    { field: 'PalletCount', title: '托盘数', minWidth: 88 },
    {
      field: 'PreOutDate',
      title: '预计出库日',
      minWidth: 100,
      formatter: 'formatDate',
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'Remark',
      title: '备注',
      minWidth: 150,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'CreateBy',
      title: '创建人',
      width: 100,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      visible: false,
    },
    {
      field: 'CreateDate',
      filterRender: { name: 'FilterDateRange' },
      filters: [{ data: { date: [], type: '0' } }],
      formatter: 'formatDateTime',
      title: '创建日期',
      width: 136,
    },
    {
      field: 'UpdateBy',
      title: '更新人',
      width: 100,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      visible: false,
    },
    {
      field: 'UpdateDate',
      filterRender: { name: 'FilterDateRange' },
      filters: [{ data: { date: [], type: '0' } }],
      formatter: 'formatDateTime',
      title: '更新日期',
      width: 136,
      visible: false,
    },
    {
      field: 'CloseBy',
      title: '关单人',
      width: 100,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      visible: false,
    },
    {
      field: 'CloseDate',
      title: '关闭日期',
      width: 136,
      formatter: 'formatDateTime',
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'Delete',
      align: 'center',
      slots: { default: 'action' },
      title: '删除',
      width: 60,
    },
  ],
  filterConfig: {
    remote: true,
  },
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: ({ filters, page, sorts }) => {
        const queryParams: any = Object.assign({}, page);
        // 处理排序
        if (sorts.length === 0) {
          queryParams.sort = '_Identify desc';
        } else {
          const sortItem = sorts.map((item) => {
            const newItem = `${item.field} ${item.order}`;
            return newItem;
          });
          queryParams.sort = sortItem.join(',');
        }
        // 处理筛选
        Object.getOwnPropertyNames(searchField).forEach((key) => {
          searchField[key] = undefined;
        });
        filters.forEach(({ field, values }) => {
          queryParams[field] = values.join(',');
          if (Object.prototype.hasOwnProperty.call(searchField, field)) {
            const jsonObject = JSON.parse(values.toString());
            searchField[field] = jsonObject.text;
          }
        });
        return getBWOutPickingList(queryParams);
      },
    },
    seq: true,
  },
  size: 'mini',
  sortConfig: {
    remote: true,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
});

const gridEvents: VxeGridListeners = {
  cellDblclick({ row }) {
    router.push({
      name: 'BWOutPickingDetail',
      params: { id: row.Guid },
    });
  },
};
const [Grid, gridApi] = useVbenVxeGrid({ gridEvents, gridOptions });

function searchEvent(field: string) {
  const column = gridApi.grid.getColumnByField(field);
  if (column) {
    // 修改第一个选项为勾选状态
    const option = column.filters[0];
    if (!option) {
      return;
    }

    if (searchField[field]) {
      option.data = { type: '1', text: searchField[field] };
      option.value = JSON.stringify(option.data);
      option.checked = true;
    } else {
      option.data = { type: '0', text: undefined };
      option.checked = false;
    }

    // 修改条件之后，需要手动调用 updateData 处理表格数据
    gridApi.grid.updateData();
    gridApi.grid.commitProxy('query');
  }
}
function openCreateModal() {
  createModalApi.open();
}
function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
  gridApi.grid.commitProxy('query');
}
function handleSuccess() {
  gridApi.grid.commitProxy('query');
}
function handleDelete(id: string) {
  bwOutPickingDelete(id)
    .then(() => {
      message.success('操作成功');
      handleSuccess();
    })
    .catch(() => {})
    .finally(() => {});
}

function showDeleteModal(id: string, name: string) {
  Modal.confirm({
    content: `请确认是否删除？`,
    /* icon: createVNode(ExclamationCircleOutlined), */
    onCancel() {},
    onOk() {
      handleDelete(id);
    },
    title: `拣货单: ${name}`,
    okButtonProps: { danger: true },
  });
}
async function fetch() {
  const options = await getOptions('OutOrderList');
  const fieldList = ['OutDeliveryType', 'OwnerShortName', 'WarehouseName'];
  fieldList.forEach((field) => {
    gridApi.grid.setFilter(
      field,
      options.filter((item: any) => {
        return item.type === field;
      }),
    );
  });
}
onMounted(() => {
  setTimeout(() => {
    fetch();
  }, 300);
});
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar_left>
        <a-button class="mr-1" @click="clearFilterAndSort()">
          <AntClear class="size-5" />
        </a-button>
        <div class="hidden pl-1 md:block">
          <a-input-search
            v-model:value="searchField.PickingDocNo"
            allow-clear
            class="mr-2 w-60"
            placeholder="拣货单号"
            @search="searchEvent('PickingDocNo')"
          />
        </div>
      </template>
      <template #toolbar-tools>
        <a-button class="mr-3" type="primary" @click="openCreateModal()">
          创建拣货单
        </a-button>
      </template>
      <template #docStatus="{ row }">
        <Tag :color="row.IsClosed ? 'green' : 'red'">
          {{ row.IsClosed ? '已关闭' : '未关闭' }}
        </Tag>
      </template>
      <template #packagingStatus="{ row }">
        <Tag :color="row.PackagingStatus === 1 ? 'green' : 'blue'">
          {{ row.PackagingStatus === 1 ? '已完成' : '未完成' }}
        </Tag>
      </template>
      <template #loadingStatus="{ row }">
        <Tag :color="row.LoadingStatus === 1 ? 'green' : 'blue'">
          {{ row.LoadingStatus === 1 ? '已完成' : '未完成' }}
        </Tag>
      </template>
      <template #deliveryStatus="{ row }">
        <Tag :color="row.DeliveryStatus === 1 ? 'green' : 'blue'">
          {{ row.DeliveryStatus === 1 ? '已完成' : '未完成' }}
        </Tag>
      </template>
      <template #action="{ row }">
        <a-button
          danger
          size="small"
          type="link"
          @click="showDeleteModal(row.Guid, row.PickingDocNo)"
        >
          <AntDelete class="size-5" />
        </a-button>
      </template>
    </Grid>
    <CreateModal @success="handleSuccess" />
  </Page>
</template>
