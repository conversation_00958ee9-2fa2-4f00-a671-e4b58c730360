<script lang="ts" setup>
import type { VxeGridProps } from '#/adapter';

import { reactive, ref } from 'vue';

import { /* useVbenDrawer, */ useVbenModal } from '@vben/common-ui';

import { Col, Descriptions, message, Modal, Row, Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import {
  bwOutPickingItemSampleAdd,
  bwOutPickingItemSampleRemove,
  getOutPickingSampleDetailByIds,
  getOutPickingSampleOPList,
} from '#/api/bondedwarehouse/outbound';

const emit = defineEmits(['success']);

const data = ref();
const isUpdate = ref(false);

const palletTypeOption = ref([]);
const packTypeOption = ref([]);
const handlingUnit: any = reactive({
  palletType: '',
  packagingMaterial: '',
});

const [TrayModal, trayModalApi] = useVbenModal({
  class: 'w-[960px]',
  fullscreenButton: false,
  title: '创建出库托盘',
  onConfirm: async () => {
    await handleCreateTray();
  },
});

const [BaseModal, modalApi] = useVbenModal({
  class: 'w-full h-full',
  destroyOnClose: true,
  fullscreenButton: true,
  closeOnClickModal: false,
  fullscreen: true,
  zIndex: 900,
  /* showConfirmButton: false, */
  footer: false,
  /* onCancel() {
    modalApi.close();
  }, */
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      isUpdate.value = false;
      data.value = modalApi.getData<Record<string, any>>();
      fetch();
    } else {
      emit('success', isUpdate.value);
    }
  },
  title: '留样拣货作业',
});
const packGridOptions = reactive<VxeGridProps>({
  id: 'OutPickingModalPack',
  columns: [
    {
      fixed: 'left',
      title: '#',
      type: 'seq',
      width: 60,
    },
    {
      field: 'SampleBoxNo',
      title: '装箱流水号',
      minWidth: 88,
    },
    {
      field: 'BLNo',
      title: '出库提单BL',
      minWidth: 136,
    },
    {
      field: 'YUB',
      title: 'SAP销售订单',
      minWidth: 88,
    },
    {
      field: 'MatCode',
      title: '物料编号',
      minWidth: 88,
    },
    {
      field: 'BatchNo',
      title: '实物批号',
      minWidth: 88,
    },
    { field: 'SampleQty', title: '留样数量', minWidth: 88 },
  ],
  keepSource: true,
  size: 'mini',
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    enabled: false,
  },
  toolbarConfig: {
    enabled: false,
  },
});
const [PackGrid, packGridApi] = useVbenVxeGrid({
  gridOptions: packGridOptions,
});

const gridOptions = reactive<VxeGridProps>({
  id: 'OutPickingModalSample',
  checkboxConfig: {
    highlight: true,
    range: true,
    checkMethod: ({ row }: any) => {
      return !row.PickingStatus;
    },
  },
  columns: [
    { type: 'checkbox', width: 40 },
    {
      field: 'SampleBoxNo',
      title: '装箱流水号',
      minWidth: 160,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'PackingDate',
      title: '装箱日期',
      minWidth: 150,
      formatter: 'formatDateTime',
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'SampleQty',
      title: '留样总数',
      minWidth: 100,
      filters: [{ data: { type: '0', num1: undefined, num2: undefined } }],
      filterRender: { name: 'FilterNumberRange' },
    },
    {
      field: 'Remark',
      title: '装箱备注',
      minWidth: 200,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
  ],
  filterConfig: {
    remote: false,
  },
  height: 'auto',
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    enabled: false,
  },
  size: 'mini',
  sortConfig: {
    remote: false,
  },
  toolbarConfig: {
    custom: false,
    refresh: false,
    slots: {
      buttons: 'toolbar_left',
    },
  },
  menuConfig: {
    trigger: 'cell',
    body: {
      options: [
        [
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuCopy',
            disabled: false,
            name: '复制单元格',
            prefixConfig: {
              icon: 'vxe-icon-copy',
            },
            visible: true,
          },
        ],
      ],
    },
    className: 'font-medium shadow rounded-md',
  },
});
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });
const itemGridOptions = reactive<VxeGridProps>({
  id: 'OutPickingModalItem',
  checkboxConfig: {
    highlight: true,
    range: true,
    checkMethod: ({ row }) => {
      return !row.PackagingStatus;
    },
  },
  columns: [
    { type: 'checkbox', width: 40 },
    {
      field: 'PackagingStatus',
      title: '打包状态',
      minWidth: 88,
      slots: { default: 'packstatus' },
    },
    {
      field: 'NewPalletNo',
      title: '出库托盘号',
      minWidth: 144,
    },
    {
      field: 'NewSSCC',
      title: '出库SSCC',
      minWidth: 144,
    },
    {
      field: 'PalletType',
      title: '托盘规格',
      minWidth: 88,
    },
    {
      field: 'PackagingMaterial',
      title: '包装材料',
      minWidth: 88,
    },
    {
      field: 'GoodsNum',
      title: '产品数量',
      minWidth: 88,
    },
    {
      field: 'MissingQty',
      title: '贴标少',
      minWidth: 72,
    },
    {
      field: 'PickingQty',
      title: '拣货数量',
      minWidth: 88,
    },
    {
      field: 'PickingBy',
      title: '拣货人',
      minWidth: 80,
    },
    {
      field: 'PickingDate',
      title: '拣货日期',
      minWidth: 136,
    },
  ],

  filterConfig: {
    remote: false,
  },
  height: 'auto',
  keepSource: true,
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    enabled: false,
  },
  /* scrollY: { enabled: true, gt: 0 }, */
  size: 'mini',
  sortConfig: {
    remote: false,
  },
  toolbarConfig: {
    custom: false,
    refresh: false,
    slots: {
      buttons: 'toolbar_left',
    },
  },
  menuConfig: {
    trigger: 'cell',
    body: {
      options: [
        [
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuCopy',
            disabled: false,
            name: '复制单元格',
            prefixConfig: {
              icon: 'vxe-icon-copy',
            },
            visible: true,
          },
        ],
      ],
    },
    className: 'font-medium shadow rounded-md',
  },
});

const [ItemGrid, itemGridApi] = useVbenVxeGrid({
  gridOptions: itemGridOptions,
});

async function handleAdd() {
  const checkedRecords = gridApi.grid
    .getCheckboxRecords()
    .map((item: any) => item.Guid);
  if (checkedRecords.length === 0) {
    message.info(`请勾选需要创托盘的留样外箱`);
    return;
  }
  modalApi.setState({ confirmLoading: true });
  modalApi.setState({ loading: true });
  getOutPickingSampleDetailByIds(data.value.id, checkedRecords)
    .then((res) => {
      packGridOptions.data = res;
      packGridApi.setGridOptions(packGridOptions);
      trayModalApi.open();
    })
    .catch(() => {})
    .finally(() => {
      modalApi.setState({ loading: false });
      modalApi.setState({ confirmLoading: false });
    });
}

function handleCreateTray() {
  const checkedRecords = gridApi.grid
    .getCheckboxRecords()
    .map((item: any) => item.Guid);
  if (checkedRecords.length === 0) {
    message.info(`请勾选需要创托盘的留样外箱`);
    return;
  }

  trayModalApi.setState({ confirmLoading: true });
  trayModalApi.setState({ loading: true });
  bwOutPickingItemSampleAdd(data.value.id, checkedRecords, handlingUnit)
    .then(() => {
      fetch();
      message.success('操作成功');
      trayModalApi.close();
    })
    .catch(() => {})
    .finally(() => {
      trayModalApi.setState({ loading: false });
      trayModalApi.setState({ confirmLoading: false });
    });
}
function showRemoveModal() {
  const checkedRecords = itemGridApi.grid
    .getCheckboxRecords()
    .map((item: any) => item.NewPalletID);
  if (checkedRecords.length === 0) {
    message.info(`请勾选需要撤销的拣货明细`);
    return;
  }
  Modal.confirm({
    content: `是否撤销勾选托盘的拣货操作？`,
    /* icon: createVNode(ExclamationCircleOutlined), */
    onCancel() {},
    onOk() {
      handleRemove(checkedRecords);
    },
    title: `操作确认`,
    okButtonProps: { danger: true },
  });
}
function handleRemove(checkedRecords: Array<string>) {
  modalApi.setState({ loading: true });
  bwOutPickingItemSampleRemove(data.value.id, checkedRecords)
    .then(() => {
      fetch();
      message.success('操作成功');
    })
    .catch(() => {
      modalApi.setState({ loading: false });
    })
    .finally(() => {});
}

async function fetch() {
  modalApi.setState({ loading: true });
  getOutPickingSampleOPList(data.value.id)
    .then((res) => {
      gridOptions.data = res.sample;
      itemGridOptions.data = res.picking;

      gridApi.setGridOptions(gridOptions);
      itemGridApi.setGridOptions(itemGridOptions);
      palletTypeOption.value = res.pallet;
      packTypeOption.value = res.pack;
      palletTypeOption.value.forEach((item: any) => {
        if (item?.d) {
          handlingUnit.palletType = item.value;
        }
      });
      packTypeOption.value.forEach((item: any) => {
        if (item?.d) {
          handlingUnit.packagingMaterial = item.value;
        }
      });
    })
    .catch(() => {})
    .finally(() => {
      modalApi.setState({ loading: false });
    });
}
</script>
<template>
  <BaseModal>
    <TrayModal>
      <Descriptions
        size="small"
        layout="vertical"
        bordered
        :column="2"
        class="p-2"
      >
        <Descriptions.Item label="包装材料">
          <a-select
            style="width: 100%"
            v-model:value="handlingUnit.packagingMaterial"
            :options="packTypeOption"
          />
        </Descriptions.Item>
        <Descriptions.Item label="托盘规格">
          <a-select
            style="width: 100%"
            v-model:value="handlingUnit.palletType"
            :options="palletTypeOption"
          />
        </Descriptions.Item>
      </Descriptions>
      <PackGrid />
    </TrayModal>
    <Row :gutter="12" class="h-1/2">
      <Col :span="24" class="h-full">
        <Grid>
          <template #toolbar_left>
            <div class="hidden pl-1 text-base font-medium md:block">
              <span class="mr-2">留样装箱清单</span>
            </div>
          </template>
          <template #toolbar-tools>
            <a-button size="small" type="primary" @click="handleAdd()">
              创建出库托盘
            </a-button>
          </template>
        </Grid>
      </Col>
    </Row>
    <Row :gutter="12" class="h-1/2">
      <Col :span="24" class="h-full">
        <ItemGrid>
          <template #toolbar_left>
            <div class="hidden pl-1 text-base font-medium md:block">
              <span class="mr-2">已拣货明细</span>
            </div>
          </template>
          <template #toolbar-tools>
            <a-button
              size="small"
              danger
              type="primary"
              @click="showRemoveModal()"
            >
              撤销拣货
            </a-button>
          </template>
          <template #packstatus="{ row }">
            <Tag :color="row.PackagingStatus ? 'green' : 'red'">
              {{ row.PackagingStatus ? '已打包' : '待打包' }}
            </Tag>
          </template>
        </ItemGrid>
      </Col>
    </Row>
  </BaseModal>
</template>
