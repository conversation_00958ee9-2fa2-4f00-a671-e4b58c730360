import { defineOverridesPreferences } from '@vben/preferences';

import header from '#/assets/images/header.jpg';
import logo from '#/assets/images/logo.png';
/**
 * @description 项目配置文件
 * 只需要覆盖项目中的一部分配置，不需要的配置不用覆盖，会自动使用默认配置
 */
export const overridesPreferences = defineOverridesPreferences({
  // overrides
  app: {
    accessMode: 'backend',
    authPageLayout: 'panel-center',
    enablePreferences: false,
    enableRefreshToken: true,
    defaultAvatar: header,
    defaultHomePath: '/workspace',
    layout: 'header-nav',
    loginExpiredMode: 'modal',
    name: import.meta.env.VITE_APP_TITLE,
  },
  footer: {
    enable: false,
  },
  logo: {
    enable: true,
    source: logo,
  },
  tabbar: {
    height: 34,
    keepAlive: true,
    persist: true,
    styleType: 'chrome',
  },
  theme: {
    builtinType: 'deep-blue',
    colorPrimary: 'hsl(211 91% 39%)',
    mode: 'light',
  },
  transition: {
    /* name: 'fade', */
    /* loading: false,
    progress: false, */
  },
  widget: {
    languageToggle: false,
    themeToggle: false,
    refresh: true,
    notification: false,
  },
  copyright: {
    enable: true,
    companyName: 'TPIT',
    companySiteLink: 'https://www.tpittc.com',
    date: '2025',
    icp: '',
    icpLink: '',
    settingShow: true,
  },
});
