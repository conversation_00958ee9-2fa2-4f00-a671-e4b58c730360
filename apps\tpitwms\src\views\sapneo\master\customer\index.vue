<script setup lang="ts">
import type { VxeGridProps } from '#/adapter';

import { reactive } from 'vue';

import { Page, useVbenDrawer } from '@vben/common-ui';
import { AntClear } from '@vben/icons';

import { useVbenVxeGrid } from '#/adapter';
import { getCustomerList } from '#/api/sapneo';

import customerDrawer from './customerDrawer.vue';

const [CustomerDrawer, customerDrawerApi] = useVbenDrawer({
  connectedComponent: customerDrawer,
});
function openCustomerDrawer(id: string) {
  customerDrawerApi.setData({ id });
  customerDrawerApi.open();
}

const searchField: any = reactive({
  bpGoldenIdCentralMdm: undefined,
  legalName1OfOrganization: undefined,
});

const gridOptions = reactive<VxeGridProps>({
  id: 'NEOCustomerMaster',
  columns: [
    {
      title: '#',
      type: 'seq',
      width: 60,
      fixed: 'left',
    },
    {
      field: 'bpGoldenIdCentralMdm',
      title: '客户代码',
      fixed: 'left',
      width: 136,
      sortable: true,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      slots: { default: 'bpGoldenIdCentralMdm' },
    },
    {
      field: 'bpNumberInExternalSystem',
      title: '外部代码',
      width: 100,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'legalName1OfOrganization',
      title: '客户名称',
      minWidth: 200,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'customerType',
      title: '类型',
      width: 64,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'customerAccountGroup',
      title: '账户组',
      width: 80,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'bpGroupingCode',
      title: '分组',
      width: 64,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'addressTimeZone',
      title: '时区',
      width: 72,
      visible: false,
    },
    {
      field: 'bpCorrespondenceLanguage',
      title: '语言',
      width: 48,
    },
    {
      field: 'countryCode',
      title: '国家',
      width: 64,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'city',
      title: '城市',
      width: 96,
    },
    {
      field: 'cityPostalCode',
      title: '邮编',
      width: 80,
    },
    {
      field: 'street',
      title: '地址街道',
      width: 200,
      formatter({ row }) {
        return `${row.street}  ${row.street2}  ${row.street3}  ${row.street4}  ${row.street5}`;
      },
    },
    {
      field: 'completeTelNumber',
      title: '地址电话',
      width: 128,
    },
    {
      field: 'bpContactPerson',
      title: '联系人代码',
      width: 128,
      visible: false,
    },
    {
      field: 'CreateDate',
      title: '创建日期',
      width: 150,
      formatter: 'formatDateTime',
      sortable: true,
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'UpdateDate',
      title: '更新日期',
      width: 150,
      formatter: 'formatDateTime',
      sortable: true,
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
  ],
  filterConfig: {
    remote: true,
  },
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: ({ filters, page, sorts }) => {
        const queryParams: any = Object.assign({}, page);
        // 处理排序条件
        if (sorts.length === 0) {
          queryParams.sort = '_Identify desc';
        } else {
          const sortItem = sorts.map((item) => {
            const newItem = `${item.field} ${item.order}`;
            return newItem;
          });
          queryParams.sort = sortItem.join(',');
        }
        // 处理筛选
        Object.getOwnPropertyNames(searchField).forEach((key) => {
          searchField[key] = undefined;
        });
        filters.forEach(({ field, values }) => {
          queryParams[field] = values.join(',');
          if (Object.prototype.hasOwnProperty.call(searchField, field)) {
            const jsonObject = JSON.parse(values.toString());
            searchField[field] = jsonObject.text;
          }
        });
        return getCustomerList(queryParams);
      },
    },
    seq: true,
  },
  scrollX: { enabled: true, gt: 0 },
  scrollY: { enabled: true, gt: 0 },
  size: 'mini',
  sortConfig: {
    remote: true,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
});
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });
function searchEvent(field: string) {
  const column = gridApi.grid.getColumnByField(field);
  if (column) {
    // 修改第一个选项为勾选状态
    const option = column.filters[0];
    if (!option) {
      return;
    }

    if (searchField[field]) {
      option.data = { type: '1', text: searchField[field] };
      option.value = JSON.stringify(option.data);
      option.checked = true;
    } else {
      option.data = { type: '0', text: undefined };
      option.checked = false;
    }

    // 修改条件之后，需要手动调用 updateData 处理表格数据
    gridApi.grid.updateData();
    gridApi.grid.commitProxy('query');
  }
}
function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
  gridApi.grid.commitProxy('query');
}
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar_left>
        <a-button class="mr-1" @click="clearFilterAndSort()">
          <AntClear class="size-5" />
        </a-button>
        <div class="hidden pl-1 md:block">
          <a-input-search
            v-model:value="searchField.bpGoldenIdCentralMdm"
            allow-clear
            class="mr-2 w-60"
            placeholder="客户代码"
            @search="searchEvent('bpGoldenIdCentralMdm')"
          />
        </div>
      </template>
      <template #toolbar-tools> </template>
      <template #bpGoldenIdCentralMdm="{ row }">
        <a-button
          size="small"
          type="link"
          @click="openCustomerDrawer(row.bpGoldenIdCentralMdm)"
        >
          {{ row.bpGoldenIdCentralMdm }}
        </a-button>
      </template>
    </Grid>
    <CustomerDrawer />
  </Page>
</template>
