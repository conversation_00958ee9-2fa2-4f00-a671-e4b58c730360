<script lang="ts" setup>
import type { VxeGridListeners, VxeGridProps } from '#/adapter';

import { reactive, ref } from 'vue';

import { Fallback, useVbenDrawer } from '@vben/common-ui';

import { Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import { getTaskListByHBL, getTaskRecord } from '#/api/lis';

const data = ref();
const isNotFound = ref(false);
const [Drawer, drawerApi] = useVbenDrawer({
  class: 'w-[560px]',
  showConfirmButton: false,
  cancelText: '关闭',
  zIndex: 900,
  onCancel() {
    drawerApi.close();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      isNotFound.value = false;
      data.value = drawerApi.getData<Record<string, any>>();
      handleGetData(data.value.hbl);
    }
  },
});
const itemGridOptions = reactive<VxeGridProps>({
  columns: [
    { type: 'seq', width: 48 },
    /* { field: 'HBL', title: 'HBL', width: 136 }, */
    { field: 'Inbound', title: 'Inbound', minWidth: 88 },
    {
      field: 'Item',
      title: 'Item',
      minWidth: 64,
    },
    {
      field: 'Invoice',
      title: 'Invoice',
      minWidth: 80,
    },
    {
      field: 'Material',
      title: 'Material',
      minWidth: 80,
    },
  ],
  customConfig: {
    storage: false,
  },
  menuConfig: {
    body: {
      options: [
        [
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuCopy',
            disabled: false,
            name: '复制单元格',
            prefixConfig: {
              icon: 'vxe-icon-copy',
            },
            visible: true,
          },
        ],
      ],
    },
    className: 'font-medium shadow rounded-md',
  },
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    enabled: false,
  },
  size: 'mini',
  stripe: false,
  toolbarConfig: {
    enabled: false,
  },
});
const [ItemGrid, itemGridApi] = useVbenVxeGrid({
  gridOptions: itemGridOptions,
});
const gridOptions = reactive<VxeGridProps>({
  id: 'Record4HBLInvoice',
  columns: [
    { type: 'expand', width: 48, slots: { content: 'expand_content' } },
    {
      field: 'TaskNo',
      title: '任务单据',
      minWidth: 136,
      treeNode: true,
    },
    {
      field: 'TaskStatus',
      title: 'API请求状态',
      width: 96,
      slots: { default: 'taskStatus' },
    },
    /* {
      field: 'AnalysisStatus',
      title: '解析状态',
      width: 96,
      slots: { default: 'analysisStatus' },
    }, */
    {
      field: 'CreateDate',
      title: '创建日期',
      width: 136,
      formatter: 'formatDateTime',
    },
  ],
  customConfig: {
    storage: false,
  },
  expandConfig: {
    accordion: true,
  },
  menuConfig: {
    body: {
      options: [
        [
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuCopy',
            disabled: false,
            name: '复制单元格',
            prefixConfig: {
              icon: 'vxe-icon-copy',
            },
            visible: true,
          },
        ],
      ],
    },
    className: 'font-medium shadow rounded-md',
  },
  rowConfig: {
    useKey: true,
  },
  size: 'mini',
  stripe: false,
  showOverflow: true,
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    enabled: false,
  },
  toolbarConfig: {
    enabled: false,
  },
});
const gridEvents: VxeGridListeners = {
  async toggleRowExpand({ expanded, row }) {
    await handleItemData([]);
    if (expanded) {
      handleItemLoading(true);
      const itemData = await getTaskRecord(row.Guid);
      await handleHeadRow(row);
      await handleItemData(itemData);
      handleItemLoading(false);
    }
  },
};
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions, gridEvents });
function handleHeadRow(row: any) {
  gridApi.grid.setCurrentRow(row);
}
function handleItemData(data: any) {
  itemGridApi.setGridOptions({
    data,
  });
}
function handleItemLoading(isLoading: boolean) {
  itemGridApi.setLoading(isLoading);
}
function handleGetData(hbl: string) {
  drawerApi.setState({ loading: true });
  getTaskListByHBL(hbl)
    .then((res) => {
      gridApi.grid.loadData(res);
      isNotFound.value = false;
    })
    .catch(() => {
      isNotFound.value = true;
    })
    .finally(() => {
      drawerApi.setState({ loading: false });
    });
}
</script>
<template>
  <Drawer title="发票关系 ">
    <Fallback v-if="isNotFound" :show-back="false" status="404" />
    <div v-else>
      <Grid>
        <template #taskStatus="{ row }">
          <Tag :color="row.TaskStatus ? 'green' : 'orange'">
            {{ row.TaskStatus ? '已完成' : '未完成' }}
          </Tag>
        </template>
        <!-- <template #analysisStatus="{ row }">
          <Tag
            :color="
              row.AnalysisStatus === 1
                ? 'green'
                : row.AnalysisStatus === 0
                  ? 'orange'
                  : 'red'
            "
          >
            {{
              row.AnalysisStatus === 1
                ? '已完成'
                : row.AnalysisStatus === 0
                  ? '未完成'
                  : '解析失败'
            }}
          </Tag>
        </template> -->
        <template #expand_content>
          <div class="p0 m0">
            <ItemGrid class="p0 m0" />
          </div>
        </template>
      </Grid>
    </div>
  </Drawer>
</template>
