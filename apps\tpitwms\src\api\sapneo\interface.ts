import { requestClient, sleep } from '#/api/request';

/**
 * SAP NEO 业务接口相关API
 */
export async function getInboundList(obj: object) {
  await sleep(100);
  return requestClient.get('/sapneo/interface/inbound/getList', {
    params: obj,
  });
}

export async function getInboundItem(id: string) {
  /* await sleep(100); */
  return requestClient.get('/sapneo/interface/inbound/getItem', {
    params: { id },
  });
}

export async function getOutboundList(obj: object) {
  await sleep(100);
  return requestClient.get('/sapneo/interface/outbound/getList', {
    params: obj,
  });
}

export async function getOutboundItem(id: string) {
  await sleep(100);
  return requestClient.get('/sapneo/interface/outbound/getItem', {
    params: { id },
  });
}

export async function outboundDelete(id: string) {
  await sleep(100);
  return requestClient.post('/sapneo/interface/outbound/del', { id });
}

export async function outboundRestore(id: string) {
  await sleep(100);
  return requestClient.post('/sapneo/interface/outbound/restore', { id });
}

export async function getOutboundRecycle(obj: object) {
  await sleep(100);
  return requestClient.get('/sapneo/interface/outbound/getRecycle', {
    params: obj,
  });
}

export async function getReturnsOutboundList(obj: object) {
  await sleep(100);
  return requestClient.get('/sapneo/interface/returns/outbound/getList', {
    params: obj,
  });
}

export async function getReturnsOutboundItem(id: string) {
  await sleep(100);
  return requestClient.get('/sapneo/interface/returns/outbound/getItem', {
    params: { id },
  });
}

export async function returnsOBDGRtest(id: string) {
  await sleep(100);
  return requestClient.post('/sapneo/interface/returns/outbound/grTest', {
    id,
  });
}

export async function getStockMovementList(obj: object) {
  await sleep(100);
  return requestClient.get('/sapneo/interface/stockmovement/getList', {
    params: obj,
  });
}

export async function getStockMovementItem(id: string) {
  await sleep(100);
  return requestClient.get('/sapneo/interface/stockmovement/getItem', {
    params: { id },
  });
}
