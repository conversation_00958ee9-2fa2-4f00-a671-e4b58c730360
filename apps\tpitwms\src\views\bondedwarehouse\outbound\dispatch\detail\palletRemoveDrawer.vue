<script lang="ts" setup>
import type { VxeGridProps } from '#/adapter';

import { reactive, ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import {
  bwOutDispatchItemRemove,
  getOutDispatchLoadingPallet,
} from '#/api/bondedwarehouse/outbound';

const emit = defineEmits(['success']);
const data = ref();

const [Drawer, drawerApi] = useVbenDrawer({
  confirmText: '确认卸载',
  onCancel() {
    drawerApi.close();
  },
  onConfirm: async () => {
    await handleRemove();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      data.value = drawerApi.getData<Record<string, any>>();
    }
  },
});
const gridOptions = reactive<VxeGridProps>({
  id: 'BWOutDispatchPalletRemove',
  checkboxConfig: { highlight: true, range: true },
  columns: [
    { type: 'checkbox', width: 48, fixed: 'left' },
    { field: 'NewPalletNo', title: '出库托盘号', width: 100 },
    { field: 'NewSSCC', title: '出库SSCC', minWidth: 150 },
    { field: 'GoodsNum', title: '产品数量', width: 100 },
  ],
  filterConfig: {
    remote: false,
  },
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    ajax: {
      query: () => {
        return getOutDispatchLoadingPallet(data.value.id);
      },
    },
    seq: true,
  },
  size: 'mini',
  sortConfig: {
    remote: false,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
});
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });
function handleRemove() {
  const checkedRecords = gridApi.grid
    .getCheckboxRecords()
    .map((item: any) => item.PalletID);
  if (checkedRecords.length === 0) {
    message.info(`请勾选卸载的托盘`);
    return;
  }
  drawerApi.setState({ confirmLoading: true });
  drawerApi.setState({ loading: true });
  bwOutDispatchItemRemove(data.value.id, checkedRecords)
    .then(() => {
      handleClose();
    })
    .catch(() => {})
    .finally(() => {
      drawerApi.setState({ loading: false });
      drawerApi.setState({ confirmLoading: false });
    });
}
function handleClose() {
  drawerApi.close();
  emit('success');
}
</script>
<template>
  <Drawer title="托盘卸载">
    <Grid />
  </Drawer>
</template>
