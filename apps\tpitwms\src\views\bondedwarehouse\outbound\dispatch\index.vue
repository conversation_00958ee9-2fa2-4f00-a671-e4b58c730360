<script setup lang="ts">
import type { VxeGridListeners, VxeGridProps } from '#/adapter';

import { onMounted, reactive } from 'vue';
import { useRouter } from 'vue-router';

import { Page, useVbenModal } from '@vben/common-ui';
import { AntClear, AntDelete } from '@vben/icons';

import { message, Modal, Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import {
  bwOutDispatchDelete,
  getOutDispatchList,
} from '#/api/bondedwarehouse/outbound/dispatch';
import { getOptions } from '#/api/common';

import createModal from './createModal.vue';

const router = useRouter();
const [CreateModal, createModalApi] = useVbenModal({
  connectedComponent: createModal,
});
const searchField: any = reactive({
  DispatchNo: undefined,
  /* DeliveryStatus: undefined, */
});
const gridOptions = reactive<VxeGridProps>({
  id: 'BWOutDispatch',
  columns: [
    {
      fixed: 'left',
      title: '#',
      type: 'seq',
      width: 60,
    },
    {
      field: 'DispatchNo',
      title: '派车单号',
      width: 150,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'DeliveryStatus',
      title: '发货状态',
      width: 88,
      formatter({ cellValue }) {
        return cellValue ? '已发货' : '待发货';
      },
      filters: [
        { label: '已发货', value: true },
        { label: '待发货', value: false },
      ],
      filterMultiple: false,
      slots: { default: 'isClosed' },
    },
    { field: 'AddressTo', title: '目的地仓库', minWidth: 120, filters: [] },
    { field: 'TruckModel', title: '车型', minWidth: 100, filters: [] },
    {
      field: 'LicensePlate',
      title: '车牌号',
      minWidth: 100,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'PalletCount',
      title: '托盘数量',
      minWidth: 100,
      filters: [{ data: { type: '0', num1: undefined, num2: undefined } }],
      filterRender: { name: 'FilterNumberRange' },
    },
    {
      field: 'GoodsNum',
      title: '产品总数',
      minWidth: 100,
      filters: [{ data: { type: '0', num1: undefined, num2: undefined } }],
      filterRender: { name: 'FilterNumberRange' },
    },
    {
      field: 'DeliveryDate',
      title: '发货日期',
      minWidth: 150,
      formatter: 'formatDateTime',
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'Remark',
      title: '备注',
      minWidth: 150,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'CreateBy',
      title: '创建人',
      width: 100,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      visible: false,
    },
    {
      field: 'CreateDate',
      title: '创建日期',
      width: 136,
      formatter: 'formatDateTime',
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'UpdateBy',
      title: '更新人',
      width: 100,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      visible: false,
    },
    {
      field: 'UpdateDate',
      filterRender: { name: 'FilterDateRange' },
      filters: [{ data: { date: [], type: '0' } }],
      formatter: 'formatDateTime',
      title: '更新日期',
      width: 136,
      visible: false,
    },
    {
      field: 'CloseBy',
      title: '关单人',
      width: 100,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      visible: false,
    },
    {
      field: 'CloseDate',
      title: '关闭日期',
      width: 136,
      formatter: 'formatDateTime',
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      align: 'center',
      slots: { default: 'action' },
      title: '删除',
      width: 60,
    },
  ],
  filterConfig: {
    remote: true,
  },
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: ({ filters, page, sorts }) => {
        const queryParams: any = Object.assign({}, page);
        // 处理排序
        if (sorts.length === 0) {
          queryParams.sort = '_Identify desc';
        } else {
          const sortItem = sorts.map((item) => {
            const newItem = `${item.field} ${item.order}`;
            return newItem;
          });
          queryParams.sort = sortItem.join(',');
        }
        // 处理筛选
        Object.getOwnPropertyNames(searchField).forEach((key) => {
          searchField[key] = undefined;
        });
        filters.forEach(({ field, values }) => {
          queryParams[field] = values.join(',');
          if (Object.prototype.hasOwnProperty.call(searchField, field)) {
            const jsonObject = JSON.parse(values.toString());
            searchField[field] = jsonObject.text;
          }
        });
        return getOutDispatchList(queryParams);
      },
    },
    seq: true,
  },
  size: 'mini',
  sortConfig: {
    remote: true,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
});

const gridEvents: VxeGridListeners = {
  cellDblclick({ row }) {
    router.push({
      name: 'BWOutDispatchDetail',
      params: { id: row.Guid },
    });
  },
};
const [Grid, gridApi] = useVbenVxeGrid({ gridEvents, gridOptions });

function searchEvent(field: string) {
  const column = gridApi.grid.getColumnByField(field);
  if (column) {
    // 修改第一个选项为勾选状态
    const option = column.filters[0];
    if (!option) {
      return;
    }

    if (searchField[field]) {
      option.data = { type: '1', text: searchField[field] };
      option.value = JSON.stringify(option.data);
      option.checked = true;
    } else {
      option.data = { type: '0', text: undefined };
      option.checked = false;
    }

    // 修改条件之后，需要手动调用 updateData 处理表格数据
    gridApi.grid.updateData();
    gridApi.grid.commitProxy('query');
  }
}
function openCreateModal() {
  createModalApi.open();
}
function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
  gridApi.grid.commitProxy('query');
}
function handleSuccess() {
  gridApi.grid.commitProxy('query');
}
function handleDelete(id: string) {
  bwOutDispatchDelete(id)
    .then(() => {
      message.success('操作成功');
      handleSuccess();
    })
    .catch(() => {})
    .finally(() => {});
}

function showDeleteModal(id: string, name: string) {
  Modal.confirm({
    content: `请确认是否删除？`,
    /* icon: createVNode(ExclamationCircleOutlined), */
    onCancel() {},
    onOk() {
      handleDelete(id);
    },
    title: `派车单号: ${name}`,
    okButtonProps: { danger: true },
  });
}
async function fetch() {
  const options = await getOptions('OutDispatch');
  const fieldList = ['AddressTo', 'TruckModel'];
  fieldList.forEach((field) => {
    gridApi.grid.setFilter(
      field,
      options.filter((item: any) => {
        return item.type === field;
      }),
    );
  });
}
onMounted(() => {
  setTimeout(() => {
    fetch();
  }, 300);
});
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar_left>
        <a-button class="mr-1" @click="clearFilterAndSort()">
          <AntClear class="size-5" />
        </a-button>
        <div class="hidden pl-1 md:block">
          <!-- <a-select
            v-model:value="searchField.DeliveryStatus"
            allow-clear
            class="mr-2 w-32"
            placeholder="发货状态"
            @change="searchEvent('IsClosed', 'Select')"
          >
            <a-select-option :value="true">已发货</a-select-option>
            <a-select-option :value="false">待发货</a-select-option>
          </a-select> -->
          <a-input-search
            v-model:value="searchField.DispatchNo"
            allow-clear
            class="mr-2 w-60"
            placeholder="派车单号"
            @search="searchEvent('DispatchNo')"
          />
        </div>
      </template>
      <template #isClosed="{ row }">
        <Tag :color="row.DeliveryStatus ? 'green' : 'red'">
          {{ row.DeliveryStatus ? '已发货' : '待发货' }}
        </Tag>
      </template>
      <template #toolbar-tools>
        <a-button class="mr-3" type="primary" @click="openCreateModal()">
          创建派车单
        </a-button>
      </template>
      <template #action="{ row }">
        <a-button
          :disabled="row.CreateBy === 'System'"
          danger
          size="small"
          type="link"
          @click="showDeleteModal(row.Guid, row.DispatchNo)"
        >
          <AntDelete class="size-5" />
        </a-button>
      </template>
    </Grid>
    <CreateModal @success="handleSuccess" />
  </Page>
</template>
