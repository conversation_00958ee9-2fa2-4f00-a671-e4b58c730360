import type {
  VxeTableDefines,
  VxeTableGridOptions,
} from '@vben/plugins/vxe-table';

import { h } from 'vue';

import { setupVbenVxeTable, useVbenVxeGrid } from '@vben/plugins/vxe-table';
import { useUserStore } from '@vben/stores';

import VxeUIPluginExportXLSX from '@vxe-ui/plugin-export-xlsx';
import VxeUIPluginRenderAntd from '@vxe-ui/plugin-render-antd';
import { Button, Image, message } from 'ant-design-vue';
import ExcelJS from 'exceljs';

import { setWebColumn } from '#/api/common';

import { useVbenForm } from './form';
import FilterContent from './vxeRender/FilterContent.vue';
import FilterDateRange from './vxeRender/FilterDateRange.vue';
import FilterInput from './vxeRender/FilterInput.vue';
import FilterInputText from './vxeRender/FilterInputText.vue';
import FilterNumberRange from './vxeRender/FilterNumberRange.vue';

import '@vxe-ui/plugin-render-antd/dist/style.css';

const userStore = useUserStore();

async function findCustomSetting(id: string) {
  const colSettings = userStore.userInfo?.colSettings;
  /* console.log(colSettings); */
  const col = colSettings.find((item: any) => item.TableName === id);
  /* await getWebColumn(id); */
  return new Promise<VxeTableDefines.CustomStoreData>((resolve) => {
    setTimeout(() => {
      try {
        if (col) {
          resolve(col.ColumnOption as VxeTableDefines.CustomStoreData);
        } else {
          resolve({});
        }
      } catch {
        resolve({});
      }
    }, 50);
  });
}
function saveCustomSetting(
  id: string,
  storeData: VxeTableDefines.CustomStoreData,
) {
  return new Promise<void>((resolve) => {
    setTimeout(() => {
      const userInfo = userStore.userInfo;
      const targetItem = userInfo?.colSettings.find(
        (item: any) => item.TableName === id,
      );
      if (targetItem) {
        targetItem.ColumnOption = storeData;
        userStore.setUserInfo(userInfo);
      }
      setWebColumn({ id, option: storeData });
      resolve();
    }, 50);
  });
}
setupVbenVxeTable({
  configVxeTable: (vxeUI) => {
    vxeUI.use(VxeUIPluginRenderAntd);
    vxeUI.use(VxeUIPluginExportXLSX, {
      ExcelJS,
    });
    vxeUI.setConfig({
      grid: {
        align: 'center',
        border: true,
        minHeight: 180,
        id: '',
        formConfig: { enabled: false },
        /* pagerConfig: {
          autoHidden: false,
          background: true,
          enabled: true,
          layouts: ['Total', 'Sizes', 'JumpNumber', 'FullJump', 'PageCount'],
          pageSize: 20,
          pageSizes: [20, 50, 100, 500],
        }, */
        proxyConfig: {
          autoLoad: true,
          filter: true,
          response: {
            result: 'items',
            total: 'total',
            /* list: 'items', */
          },
          sort: true,
          showActiveMsg: true,
          showResponseMsg: false,
        },
        round: true,
        size: 'small',
        toolbarConfig: {
          custom: true,
          enabled: true,
          export: false,
          print: false,
          refresh: true,
          refreshOptions: {
            code: 'query',
          },
          zoom: false,
        },
        customConfig: {
          storage: true,
          restoreStore({ id }) {
            return findCustomSetting(id);
          },
          updateStore({ id, storeData }) {
            return saveCustomSetting(id, storeData);
          },
        },
        menuConfig: {
          trigger: 'cell',
          body: {
            options: [
              [
                {
                  className: 'rounded-md text-black text-sm pt-1 pb-1',
                  code: 'menuCopy',
                  disabled: false,
                  name: '复制单元格',
                  prefixConfig: {
                    icon: 'vxe-icon-copy',
                  },
                  visible: true,
                },
                {
                  className: 'rounded-md text-black text-sm pt-1 pb-1',
                  code: 'menuRefresh',
                  disabled: false,
                  name: '刷新',
                  prefixConfig: { icon: 'vxe-icon-refresh' },
                  visible: true,
                },
              ],
            ],
          },
          className: 'font-medium shadow rounded-md',
        },
      } as VxeTableGridOptions,
      table: {
        autoResize: true,
        border: true,
        columnConfig: {
          isCurrent: false,
          isHover: false,
          resizable: true,
        },
        loading: false,
        rowConfig: {
          isCurrent: true,
          /* isHover: true, */
        },
        showFooterOverflow: true,
        showHeaderOverflow: true,
        showOverflow: true,
        sortConfig: {
          chronological: true,
          multiple: true,
        },
        stripe: true,
      },
    });

    // 表格配置项可以用 cellRender: { name: 'CellImage' },
    vxeUI.renderer.add('CellImage', {
      renderDefault(_renderOpts, params) {
        const { column, row } = params;
        return h(Image, { src: row[column.field] });
      },
    });

    // 表格配置项可以用 cellRender: { name: 'CellLink' },
    vxeUI.renderer.add('CellLink', {
      renderDefault(renderOpts) {
        const { props } = renderOpts;
        return h(
          Button,
          { size: 'small', type: 'link' },
          { default: () => props?.text },
        );
      },
    });

    vxeUI.renderer.add('FilterInput', {
      // 自定义筛选模板
      renderTableFilter(_renderOpts, params) {
        return h(FilterInput, { params });
      },
      // 自定义筛选方法
      tableFilterMethod(params) {
        const { column, option, row } = params;
        const { data } = option;
        const cellValue = row[column.field];
        if (cellValue) {
          return cellValue.includes(data);
        }
        return false;
      },
      // 自定义重置筛选复原方法（当未点击确认时，该选项将被恢复为默认值）
      tableFilterRecoverMethod({ option }) {
        option.data = '';
      },
      // 自定义重置数据方法
      tableFilterResetMethod(params) {
        const { options } = params;
        options.forEach((option) => {
          option.data = '';
        });
      },
    });
    vxeUI.renderer.add('FilterDateRange', {
      /* showFilterFooter: false, */
      // 筛选模板
      renderTableFilter(_renderOpts, params) {
        return h(FilterDateRange, { params });
      },
      // 重置数据方法
      tableFilterResetMethod(params) {
        const { options } = params;
        options.forEach((option) => {
          option.data = { date: [], type: '0' };
        });
      },
      // 重置筛选复原方法（当未点击确认时，该选项将被恢复为默认值）
      tableFilterRecoverMethod({ option }) {
        option.data = { date: [], type: '0' };
      },
    });
    vxeUI.renderer.add('FilterNumberRange', {
      // 筛选模板
      renderTableFilter(_renderOpts, params) {
        return h(FilterNumberRange, { params });
      },
      // 重置数据方法
      tableFilterResetMethod(params) {
        const { options } = params;
        options.forEach((option) => {
          option.data = { num1: undefined, num2: undefined, type: '0' };
        });
      },
      // 重置筛选复原方法（当未点击确认时，该选项将被恢复为默认值）
      tableFilterRecoverMethod({ option }) {
        option.data = { num1: undefined, num2: undefined, type: '0' };
      },
    });
    vxeUI.renderer.add('FilterInputText', {
      // 筛选模板
      renderTableFilter(_renderOpts, params) {
        return h(FilterInputText, { params });
      },
      // 重置数据方法
      tableFilterResetMethod(params) {
        const { options } = params;
        options.forEach((option) => {
          option.data = { text: undefined, type: '0' };
        });
      },
      // 重置筛选复原方法（当未点击确认时，该选项将被恢复为默认值）
      tableFilterRecoverMethod({ option }) {
        option.data = { text: undefined, type: '0' };
      },
    });
    // 创建一个显示列表的筛选的筛选渲染器
    vxeUI.renderer.add('FilterContent', {
      // 不显示底部按钮，使用自定义的按钮
      showTableFilterFooter: false,
      // 自定义筛选模板
      renderTableFilter(_renderOpts, renderParams) {
        return h(FilterContent, { renderParams });
      },
      // 自定义重置数据方法
      tableFilterResetMethod(params) {
        const { options } = params;
        options.forEach((option) => {
          option.data = { vals: [], sVal: '' };
        });
      },
      // 自定义筛选数据方法
      tableFilterMethod(params) {
        const { option, row, column } = params;
        const { vals } = option.data;
        const cellValue = row[column.field];
        return vals.includes(cellValue);
      },
    });
    // 这里可以自行扩展 vxe-table 的全局配置，比如自定义格式化
    // vxeUI.formats.add
    // 格式化VXE表格日期
    /* vxeUI.formats.add('YMD', {
      cellFormatMethod({ cellValue }, t) {
        return t
          ? XEUtils.toDateString(cellValue, 'yyyy-MM-dd HH:mm:ss')
          : XEUtils.toDateString(cellValue, 'yyyy-MM-dd');
      },
    }); */
    // 注册菜单
    vxeUI.menus.add('menuCopy', {
      menuMethod({ column, row }) {
        if (row && column && vxeUI.clipboard.copy(row[column.field])) {
          message.success('已复制到剪贴板！');
        }
      },
    });
    vxeUI.menus.add('menuRefresh', {
      menuMethod({ $grid }) {
        $grid?.commitProxy('query');
      },
    });
    /* vxeUI.menus.add('menuExport', {
      menuMethod({ $grid }) {
        $grid?.commitProxy('query');
      },
    }); */
  },
  useVbenForm,
});

export { useVbenVxeGrid };

export type * from '@vben/plugins/vxe-table';
