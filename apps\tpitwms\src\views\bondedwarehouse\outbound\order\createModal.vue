<script lang="ts" setup>
import { reactive, ref } from 'vue';
import { useRouter } from 'vue-router';

import { useVbenModal } from '@vben/common-ui';

import { Result } from 'ant-design-vue';

import { useVbenForm, z } from '#/adapter';
import { bwOutOrderCreate } from '#/api/bondedwarehouse/outbound';
import { getOptions } from '#/api/common';

const emit = defineEmits(['success']);

const orderId = ref(undefined);
const cName = reactive({
  OwnerShortName: undefined,
  WarehouseName: undefined,
});
const router = useRouter();
const filterOption = (input: string, option: any) => {
  return option.label.toLowerCase().includes(input.toLowerCase());
};
const [Form, formApi] = useVbenForm({
  handleSubmit: onSubmit,
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  schema: [
    {
      component: 'Select',
      componentProps: {
        getPopupContainer: () => document.body,
        onChange: async (e: any) => {
          formApi.setFieldValue('HBL', undefined, false);
          formApi.setFieldValue('BLNo', undefined, false);
          if (['N', 'R', 'T'].includes(e)) {
            formApi.updateSchema([
              {
                componentProps: {
                  getPopupContainer: () => document.body,
                  filterOption,
                  onChange: (value: any, option: any) => {
                    if (value && e === 'N') {
                      formApi.setFieldValue('BLNo', `${option.label}-`);
                    } else {
                      formApi.setFieldValue('BLNo', undefined, false);
                    }
                  },
                  showSearch: true,
                  allowClear: true,
                },
                fieldName: 'HBL',
              },
            ]);
          }
        },
      },
      fieldName: 'OutDeliveryType',
      label: '订单类型',
      rules: 'required',
    },
    {
      component: 'Select',
      componentProps: {},
      fieldName: 'HBL',
      label: '入库提单(HBL)',
      dependencies: {
        show(values) {
          return ['N', 'R', 'T'].includes(values.OutDeliveryType);
        },
        rules(values) {
          if (['N', 'R', 'T'].includes(values.OutDeliveryType)) {
            return 'required';
          }
          return null;
        },
        triggerFields: ['OutDeliveryType'],
      },
    },
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 48,
      },
      fieldName: 'BLNo',
      label: '出库提单(BL)',
      /* rules: z
        .string({ message: '请输入出库提单(BL)' })
        .min(8, { message: '至少需要8个字符' })
        .regex(/^[A-Z0-9-]+$/, {
          message: `只能包含大写字母、数字或字符'-'`,
        }), */
      dependencies: {
        rules(values) {
          if (values.OutDeliveryType === 'N') {
            return z
              .string({ message: '请输入出库提单(BL)' })
              .min(8, { message: '至少需要8个字符' })
              .regex(/^[A-Z0-9]+-\d{1,2}$/, {
                message: `调拨By HBL请以"HBL-数字"的方式命名（未拆分-99）`,
              });
          }
          return z
            .string({ message: '请输入出库提单(BL)' })
            .min(8, { message: '至少需要8个字符' })
            .regex(/^[A-Z0-9-]+$/, {
              message: `只能包含大写字母、数字或字符'-'`,
            });
        },
        triggerFields: ['OutDeliveryType'],
      },
    },
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 10,
      },
      fieldName: 'YUB',
      label: 'SAP销售订单',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        getPopupContainer: () => document.body,
        onSelect: (_value: string, option: any) => {
          cName.OwnerShortName = option.label;
        },
      },
      fieldName: 'OwnerCode',
      label: '货主',
      dependencies: {
        show(values) {
          return ['D', 'Z'].includes(values.OutDeliveryType);
        },
        rules(values) {
          if (['D', 'Z'].includes(values.OutDeliveryType)) {
            return 'required';
          }
          return null;
        },
        triggerFields: ['OutDeliveryType'],
      },
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        getPopupContainer: () => document.body,
        onSelect: (_value: string, option: any) => {
          cName.WarehouseName = option.label;
        },
      },
      fieldName: 'WarehouseCode',
      label: '仓库',
      dependencies: {
        show(values) {
          return ['D', 'Z'].includes(values.OutDeliveryType);
        },
        rules(values) {
          if (['D', 'Z'].includes(values.OutDeliveryType)) {
            return 'required';
          }
          return null;
        },
        triggerFields: ['OutDeliveryType'],
      },
    },
  ],
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  fullscreenButton: false,
  closeOnClickModal: false,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await formApi.submitForm();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      modalApi.setState({ showCancelButton: true });
      modalApi.setState({ showConfirmButton: true });
      orderId.value = undefined;
      formApi.resetForm();
      fetch();
    }
  },
  title: '创建出库订单',
});

async function onSubmit(values: Record<string, any>) {
  const check = await formApi.validate();
  if (check.valid) {
    modalApi.setState({ confirmLoading: true });
    modalApi.setState({ loading: true });
    bwOutOrderCreate(Object.assign({}, values, cName))
      .then((res) => {
        modalApi.setState({ showCancelButton: false });
        modalApi.setState({ showConfirmButton: false });
        orderId.value = res;
      })
      .catch(() => {})
      .finally(() => {
        modalApi.setState({ loading: false });
        modalApi.setState({ confirmLoading: false });
      });
  }
}
function handleClose() {
  modalApi.close();
  emit('success');
}
function handleGoDetail() {
  modalApi.close();
  router.push({
    name: 'BWOutOrderDetail',
    params: { id: orderId.value },
  });
  emit('success');
}
async function fetch() {
  const options = await getOptions('BWOutOrderCreate');
  const fieldList = ['OutDeliveryType', 'HBL', 'OwnerCode', 'WarehouseCode'];
  fieldList.forEach((field) => {
    formApi.updateSchema([
      {
        componentProps: {
          options: options.filter((item: any) => {
            return item.type === field;
          }),
        },
        fieldName: field,
      },
    ]);
  });
}
</script>
<template>
  <Modal>
    <Result v-if="orderId" status="success" title="创建成功">
      <template #extra>
        <a-button type="primary" @click="handleGoDetail()">
          查看出库订单
        </a-button>
        <a-button @click="handleClose()">关闭</a-button>
      </template>
    </Result>
    <Form v-else />
  </Modal>
</template>
