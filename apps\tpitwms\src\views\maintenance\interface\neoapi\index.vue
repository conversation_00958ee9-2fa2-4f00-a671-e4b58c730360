<script setup lang="ts">
import type { VxeGridProps } from '#/adapter';

import { reactive, ref } from 'vue';
import { useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';
import { AntSetting } from '@vben/icons';

import { RadioButton, RadioGroup, Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import { getNEOAPIList } from '#/api/maintenance/interface';

const apiType = ref('NEO2WMS');
const router = useRouter();
const gridOptions = reactive<VxeGridProps>({
  id: 'Settings4NEOAPI',
  columns: [
    { title: '#', type: 'seq', width: 60, fixed: 'left' },
    /* {
      field: 'APIType',
      title: 'API Type',
      width: 100,
    }, */
    { field: 'APIName', title: 'API Name', width: 160 },
    {
      field: 'IsActived',
      title: 'IsActived',
      width: 96,
      slots: { default: 'isActived' },
    },
    { field: 'APIMethod', title: 'Method', width: 96 },
    {
      align: 'left',
      field: 'APIURL',
      title: 'URL',
      minWidth: 300,
      showOverflow: 'ellipsis',
    },
    {
      title: 'Action',
      slots: { default: 'action' },
      width: 64,
      align: 'center',
    },
  ],
  height: 'auto',
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    ajax: {
      query: () => {
        return getNEOAPIList(apiType.value);
      },
    },
    seq: true,
  },
  size: 'mini',
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
});

const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });

/* function handleSuccess() {
  gridApi.grid.commitProxy('query');
} */
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar_left>
        <RadioGroup
          v-model:value="apiType"
          button-style="solid"
          @change="gridApi.grid.commitProxy('query')"
        >
          <RadioButton value="NEO2WMS">NEO2WMS</RadioButton>
          <RadioButton value="WMS2NEO">WMS2NEO</RadioButton>
        </RadioGroup>
      </template>
      <template #isActived="{ row }">
        <Tag :color="row.IsActived ? 'green' : 'red'">
          {{ row.IsActived }}
        </Tag>
      </template>
      <template #toolbar-tools> </template>
      <template #action="{ row }">
        <a-button
          size="small"
          type="link"
          @click="
            router.push({
              name: 'NEOAPISettingsDetail',
              params: { id: row.Guid },
            })
          "
        >
          <AntSetting class="size-6" />
        </a-button>
      </template>
    </Grid>
  </Page>
</template>
