<script lang="ts" setup>
import type { EchartsUIType } from '@vben/plugins/echarts';

import { onMounted, ref } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

import { getTrends4Event } from '#/api/common/web';

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);

onMounted(() => {
  getTrends4Event()
    .then((res) => {
      renderEcharts({
        animationDuration: 1000,
        dataset: res.dataset,
        grid: {
          bottom: 0,
          containLabel: true,
          left: '1%',
          right: '300',
          top: '2%',
        },
        legend: {
          right: 0,
          top: '5%',
          orient: 'vertical',
          itemGap: 20, // 图例间距
          itemWidth: 16, // 图例标记宽度
          itemHeight: 16, // 图例标记高度
        },
        series: res.series,
        tooltip: {
          order: 'valueDesc',
          trigger: 'axis',
          confine: true,
          axisPointer: {
            type: 'line',
          },
        },
        // xAxis: {
        //   axisTick: {
        //     show: false,
        //   },
        //   boundaryGap: false,
        //   data: Array.from({ length: 18 }).map((_item, index) => `${index + 6}:00`),
        //   type: 'category',
        // },
        xAxis: {
          nameLocation: 'middle',
          type: 'category',
        },
        yAxis: { name: 'Event' },
        // 添加响应式配置，在小屏幕下隐藏图例
        media: [
          {
            query: {
              maxWidth: 768, // 小屏幕断点，可根据需要调整
            },
            option: {
              legend: {
                show: false, // 小屏幕下隐藏图例
              },
              grid: {
                right: '10', // 小屏幕下调整网格右边距
              },
            },
          },
        ],
      });
    })
    .catch(() => {})
    .finally(() => {});
});
</script>

<template>
  <EchartsUI ref="chartRef" />
</template>
