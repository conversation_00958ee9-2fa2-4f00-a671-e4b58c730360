<script lang="ts" setup>
import { onMounted, ref } from 'vue';

import {
  EllipsisText,
  Page,
  useVbenDrawer,
  VbenButton,
  VbenLoading,
} from '@vben/common-ui';
import { AntDelete, AntEdit, AntPlus, AntReload } from '@vben/icons';

import {
  Badge,
  Card,
  Col,
  List,
  ListItem,
  message,
  Modal,
  Row,
  Tag,
} from 'ant-design-vue';

import {
  areaDelete,
  getWarehouseList,
  warehouseDelete,
} from '#/api/maintenance/data';

import areaDrawer from './areaDrawer.vue';
import warehouseDrawer from './warehouseDrawer.vue';

const warehouseJo = ref({}) as any;
const loadingRef = ref(false);
const [WarehouseDrawer, warehouseDrawerApi] = useVbenDrawer({
  // 连接抽离的组件
  connectedComponent: warehouseDrawer,
});
const [AreaDrawer, areaDrawerApi] = useVbenDrawer({
  // 连接抽离的组件
  connectedComponent: areaDrawer,
});
function openWarehouseDrawer(id: string) {
  warehouseDrawerApi.setData({ id });
  warehouseDrawerApi.open();
}
function openAreaDrawer(id: string, info: any) {
  areaDrawerApi.setData({ id, info });
  areaDrawerApi.open();
}
function handleSuccess() {
  loadingRef.value = true;
  fetch();
}
function handleDelete(id: string) {
  warehouseDelete(id)
    .then(() => {
      message.success('操作成功');
      handleSuccess();
    })
    .catch(() => {})
    .finally(() => {});
}
function handleAreaDelete(id: string) {
  areaDelete(id)
    .then(() => {
      message.success('操作成功');
      handleSuccess();
    })
    .catch(() => {})
    .finally(() => {});
}
function showDeleteModal(id: string, name: string) {
  Modal.confirm({
    content: `请确认是否删除？`,
    /* icon: createVNode(ExclamationCircleOutlined), */
    onCancel() {},
    onOk() {
      handleDelete(id);
    },
    title: `仓库: ${name}`,
    okButtonProps: { danger: true },
  });
}
function showAreaDeleteModal(id: string, name: string) {
  Modal.confirm({
    content: `请确认是否删除？`,
    /* icon: createVNode(ExclamationCircleOutlined), */
    onCancel() {},
    onOk() {
      handleAreaDelete(id);
    },
    title: `库区: ${name}`,
    okButtonProps: { danger: true },
  });
}
async function fetch() {
  warehouseJo.value = await getWarehouseList();
  loadingRef.value = false;
}
onMounted(() => {
  fetch();
});
</script>

<template>
  <Page title="仓库列表">
    <template #extra>
      <VbenButton class="mr-5" variant="link" @click="handleSuccess()">
        <AntReload class="size-4" />
      </VbenButton>
      <VbenButton
        class="mr-5"
        variant="default"
        @click="openWarehouseDrawer('new')"
      >
        <AntPlus class="mr-2 size-4" />
        新增仓库
      </VbenButton>
    </template>
    <template v-for="item in warehouseJo.warehouse" :key="item.Guid">
      <Card :hoverable="true" class="w-full">
        <template #title>
          <div>
            <span>{{ item.WarehouseName }}</span>
            <Tag :color="item.IsActived ? 'green' : 'red'" class="ml-2">
              {{ item.IsActived ? '启用' : '停用' }}
            </Tag>
          </div>
        </template>
        <template #extra>
          <a-button type="link" @click="openWarehouseDrawer(item.Guid)">
            <AntEdit class="size-5" />
          </a-button>
          <a-button
            danger
            type="link"
            @click="showDeleteModal(item.Guid, item.WarehouseName)"
          >
            <AntDelete class="size-5" />
          </a-button>
        </template>

        <div>
          <EllipsisText
            v-if="item.Address"
            :line="2"
            :tooltip="false"
            class="mb-4"
          >
            {{ item.Address }}
          </EllipsisText>
          <Row :gutter="[16, 16]">
            <template
              v-for="areaTypeItem in warehouseJo.areaType"
              :key="areaTypeItem.value"
            >
              <Col :lg="12" :md="12" :sm="12" :xl="6" :xs="24" :xxl="6">
                <Card
                  :hoverable="true"
                  :title="areaTypeItem.label"
                  size="small"
                >
                  <template #extra>
                    <a-button
                      type="link"
                      @click="
                        openAreaDrawer('new', {
                          WarehouseCode: item.WarehouseCode,
                          WarehouseName: item.WarehouseName,
                          AreaType: areaTypeItem.value,
                          AreaTypeName: areaTypeItem.label,
                        })
                      "
                    >
                      新增
                    </a-button>
                  </template>
                  <List size="small">
                    <ListItem
                      v-for="areaItem in warehouseJo.area.filter(
                        (aItem: any) =>
                          aItem.WarehouseCode === item.WarehouseCode &&
                          aItem.AreaType === areaTypeItem.value,
                      )"
                      :key="areaItem.AreaCode"
                    >
                      <Badge :color="areaItem.IsActived ? 'green' : 'red'" dot>
                        {{ areaItem.AreaName }}
                      </Badge>
                      <span v-if="areaItem.IsDefault">默认</span>
                      <template #actions>
                        <div>
                          <a-button
                            type="link"
                            @click="
                              openAreaDrawer(areaItem.Guid, {
                                WarehouseCode: item.WarehouseCode,
                                WarehouseName: item.WarehouseName,
                                AreaType: areaTypeItem.value,
                                AreaTypeName: areaTypeItem.label,
                              })
                            "
                          >
                            <AntEdit class="size-5" />
                          </a-button>
                          <a-button
                            :disabled="areaItem.IsProvisional"
                            danger
                            type="link"
                            @click="
                              showAreaDeleteModal(
                                areaItem.Guid,
                                areaItem.AreaName,
                              )
                            "
                          >
                            <AntDelete class="size-5" />
                          </a-button>
                        </div>
                      </template>
                    </ListItem>
                  </List>
                </Card>
              </Col>
            </template>
          </Row>
        </div>
      </Card>
    </template>
    <WarehouseDrawer @success="handleSuccess()" />
    <AreaDrawer @success="handleSuccess()" />
    <VbenLoading :spinning="loadingRef" />
  </Page>
</template>
