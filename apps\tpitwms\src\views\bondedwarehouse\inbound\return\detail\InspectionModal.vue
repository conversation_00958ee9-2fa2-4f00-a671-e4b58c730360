<script lang="ts" setup>
import { ref } from 'vue';

import { useVbenDrawer, useVbenModal } from '@vben/common-ui';
import { AntSearch } from '@vben/icons';

import { Descriptions, message, Modal, Tag } from 'ant-design-vue';

import { useVbenForm, z } from '#/adapter';
import {
  bwInReturnInspectionGetData,
  bwInReturnItemInspection,
} from '#/api/bondedwarehouse/inbound';

import batchSelectDrawer from './batchSelectDrawer.vue';

const emit = defineEmits(['success']);

const data = ref();
const itemData = ref({}) as any;
const submitData = ref({}) as any;
const [BatchSelectDrawer, BatchSelectDrawerApi] = useVbenDrawer({
  connectedComponent: batchSelectDrawer,
});

const [Form, formApi] = useVbenForm({
  handleSubmit: onSubmit,
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
    labelClass: 'w-24',
  },
  schema: [
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 16,
      },
      fieldName: 'ReturnBatch',
      label: '实物批次',
    },
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 10,
      },
      fieldName: 'ReturnExpirationDate',
      label: '实物效期',
    },
    {
      component: 'InputNumber',
      componentProps: {
        autocomplete: 'off',
        min: 0,
      },
      fieldName: 'InspectionQty',
      label: '质检数量',
      rules: 'required',
    },
    {
      component: 'Switch',
      componentProps: {
        class: 'w-auto',
        checkedChildren: '正常',
        unCheckedChildren: '异常',
      },
      defaultValue: true,
      fieldName: 'IsNormal',
      label: '产品状态',
      rules: 'required',
    },
    /* {
      component: 'Select',
      componentProps: {},
      fieldName: 'TargetLocation',
      label: '目标SAP位置',
      rules: 'required',
    }, */
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 128,
      },
      fieldName: 'Remark',
      formItemClass: 'col-span-1 md:col-span-2',
      label: '质检备注',
    },
  ],
  showDefaultActions: false,
  wrapperClass: 'grid-cols-1 md:grid-cols-2',
});

const [BaseModal, modalApi] = useVbenModal({
  class: 'w-[640px]',
  draggable: true,
  fullscreenButton: false,
  closeOnClickModal: false,
  destroyOnClose: true,
  zIndex: 900,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await formApi.submitForm();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      modalApi.setState({ showConfirmButton: false });
      itemData.value = {};
      submitData.value = {};
      data.value = modalApi.getData<Record<string, any>>();
      formApi.resetForm();
      fetch();
    }
  },
  title: '退货质检',
});

async function onSubmit(values: Record<string, any>) {
  const check = await formApi.validate();
  if (check.valid) {
    submitData.value = values;
    checkQty();
  }
}
function handleSubmit() {
  Modal.destroyAll();
  modalApi.setState({ confirmLoading: true });
  modalApi.setState({ loading: true });
  bwInReturnItemInspection(
    Object.assign({ id: data.value.id }, submitData.value),
  )
    .then(() => {
      handleClose();
    })
    .catch(() => {})
    .finally(() => {
      modalApi.setState({ loading: false });
      modalApi.setState({ confirmLoading: false });
    });
}

function checkQty() {
  if (submitData.value.InspectionQty === 0) {
    message.error('质检数量应大于0');
    return;
  }
  if (itemData.value.ReceivedQty > submitData.value.InspectionQty) {
    Modal.confirm({
      closable: true,
      content: `质检产品数量小于收货数量，是否拆分？`,
      onOk() {
        handleSubmit();
      },
      title: `数量差异提醒`,
    });
  } else {
    handleSubmit();
  }
}

function handleBatchSearch() {
  BatchSelectDrawerApi.setData({
    material: itemData.value.MatCode,
  });
  BatchSelectDrawerApi.open();
}
function handleBatchSelected(row: any) {
  formApi.setFieldValue('ReturnBatch', row.SubBatch);
  if (row.ShelfLifeExpirationDate) {
    formApi.setFieldValue(
      'ReturnExpirationDate',
      row.ShelfLifeExpirationDate.slice(0, 10),
    );
  }
}
function handleClose() {
  modalApi.close();
  emit('success');
}
async function fetch() {
  await bwInReturnInspectionGetData(data.value.id)
    .then((res) => {
      modalApi.setState({ showConfirmButton: true });
      itemData.value = res;
      formApi.setValues(itemData.value);
      formApi.updateSchema([
        {
          componentProps: {
            options: res.locationOptions,
          },
          fieldName: 'TargetLocation',
        },
        {
          rules: res.isBatchManagementRequired ? 'required' : null,
          fieldName: 'ReturnBatch',
          disabled: !res.isBatchManagementRequired,
        },
        {
          rules: res.isBatchManagementRequired
            ? z
                .string()
                .regex(
                  /^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])$|^\d{4}-(0[1-9]|1[0-2])$/,
                  { message: '格式：yyyy-MM-dd或yyyy-MM' },
                )
            : null,
          fieldName: 'ReturnExpirationDate',
          disabled: !res.isBatchManagementRequired,
        },
        {
          rules: z
            .number()
            .max(res.ReceivedQty, '质检产品数量不能大于收货数量')
            .min(0, { message: '请输入质检产品数量' })
            .nullable()
            .refine((value) => value !== null && value !== undefined, {
              message: '请输入质检产品数量',
            }),
          fieldName: 'InspectionQty',
        },
      ]);
    })
    .catch(() => {})
    .finally(() => {});
}
</script>
<template>
  <BaseModal>
    <Descriptions :column="{ xs: 1, sm: 2, md: 3 }" bordered size="small">
      <Descriptions.Item :span="2" label="SSCC">
        {{ itemData.SSCC }}
      </Descriptions.Item>
      <Descriptions.Item label="入库托盘">
        {{ itemData.PalletNo }}
      </Descriptions.Item>
      <Descriptions.Item label="产品编号">
        {{ itemData.MatCode }}
      </Descriptions.Item>
      <Descriptions.Item label="产品条码">
        {{ itemData.iBarCode }}
      </Descriptions.Item>
      <Descriptions.Item label="产品类型">
        {{ itemData.iMatType }}
      </Descriptions.Item>

      <Descriptions.Item label="箱规数量">
        {{ itemData.iPCB }}
      </Descriptions.Item>
      <Descriptions.Item label="层规数量">
        {{ itemData.iPCL }}
      </Descriptions.Item>
      <Descriptions.Item label="托规数量">
        {{ itemData.iPCP }}
      </Descriptions.Item>
      <Descriptions.Item label="收货数量">
        {{ itemData.ReceivedQty }}
      </Descriptions.Item>
      <Descriptions.Item label="批次管理">
        <Tag :color="itemData.isBatchManagementRequired ? 'green' : 'blue'">
          {{ itemData.isBatchManagementRequired ? '是' : '否' }}
        </Tag>
      </Descriptions.Item>
    </Descriptions>
    <Form class="mt-4">
      <template #ReturnBatch="slotProps">
        <a-input-search v-bind="slotProps" @search="handleBatchSearch">
          <template #enterButton>
            <a-button class="!pl-1 !pr-1">
              <AntSearch class="size-5" />
            </a-button>
          </template>
        </a-input-search>
      </template>
    </Form>
    <BatchSelectDrawer @success="handleBatchSelected" />
  </BaseModal>
</template>
