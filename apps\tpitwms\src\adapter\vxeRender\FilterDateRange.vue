<script lang="ts" setup>
import type { PropType } from 'vue';

import type {
  VxeGlobalRendererHandles,
  VxeTableDefines,
} from '@vben/plugins/vxe-table';

import { ref, watch } from 'vue';

import { RadioButton, RadioGroup, RangePicker } from 'ant-design-vue';

const props = defineProps({
  params: {
    default: () => ({}),
    type: Object as PropType<VxeGlobalRendererHandles.RenderTableFilterParams>,
  },
});
const currOption = ref<VxeTableDefines.FilterOption>();

const load = () => {
  const { params } = props;
  if (params) {
    const { column } = params;
    const option = column.filters[0];
    currOption.value = option;
  }
};

const changeOptionEvent = () => {
  const { params } = props;
  const option = currOption.value;
  if (params && option) {
    option.value = JSON.stringify(option.data);
    const { $panel } = params;
    const checked = !(
      option?.data.type === '0' &&
      (option?.data.date === undefined || option?.data.date === null)
    );
    $panel.changeOption(null, checked, option);
  }
};

const onRangeChange = () => {
  changeOptionEvent();
};

/* const confirmFilterEvent = () => {
  const { params } = props;
  if (params) {
    const { $panel } = params;
    $panel.confirmFilter();
  }
};
const resetFilterEvent = () => {
  const { params } = props;
  if (params) {
    const { $panel } = params;
    $panel.resetFilter();
  }
}; */

watch(() => {
  const { column } = props.params || {};
  return column;
}, load);

load();
</script>

<template>
  <div v-if="currOption" class="my-filter-date">
    <div style="padding-bottom: 8px">
      <RadioGroup
        v-model:value="currOption.data.type"
        button-style="solid"
        size="small"
        @change="changeOptionEvent"
      >
        <RadioButton value="0">日期范围</RadioButton>
        <RadioButton value="1">空值</RadioButton>
      </RadioGroup>
    </div>
    <RangePicker
      v-model:value="currOption.data.date"
      :disabled="currOption.data.type === '1'"
      :get-popup-container="(triggerNode: any) => triggerNode.parentNode"
      value-format="YYYY-MM-DD"
      @change="onRangeChange"
    />
    <!-- <div class="my-fc-footer">
      <a-button
        :disabled="
          currOption?.data.type === '0' && currOption?.data.date.length === 0
        "
        size="mini"
        type="text"
        @click="confirmFilterEvent"
      >
        筛选
      </a-button>
      <a-button size="mini" type="text" @click="resetFilterEvent">
        重置
      </a-button>
    </div> -->
  </div>
</template>

<style scoped>
.my-filter-date {
  padding: 10px;
}

.my-fc-footer {
  padding-top: 8px;
  text-align: left;
}

.my-fc-footer button {
  padding: 0 8px 0 0;
  margin-left: 2px;
}
</style>
