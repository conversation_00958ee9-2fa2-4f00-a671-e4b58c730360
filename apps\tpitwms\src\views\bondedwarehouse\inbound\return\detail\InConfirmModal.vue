<script lang="ts" setup>
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter';
import {
  bwInReturnInStatusCheck,
  bwInReturnInStatusConfirm,
} from '#/api/bondedwarehouse/inbound';

const emit = defineEmits(['success']);

const data = ref();

const [Form, formApi] = useVbenForm({
  handleSubmit: onSubmit,
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  schema: [
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 16,
      },
      fieldName: 'WarehouseName',
      label: '仓库',
      disabled: true,
    },
    /* {
      component: 'DatePicker',
      componentProps: {
        style: { width: '100%' },
        valueFormat: 'YYYY-MM-DD',
      },
      fieldName: 'PreInDate',
      label: '预计进仓日期',
      rules: 'required',
    }, */
    {
      component: 'DatePicker',
      componentProps: {
        style: { width: '100%' },
        valueFormat: 'YYYY-MM-DD',
      },
      fieldName: 'InDate',
      label: '进仓日期',
      rules: 'required',
    },
    {
      component: 'Select',
      componentProps: {
        getPopupContainer: () => document.body,
      },
      fieldName: 'InArea',
      label: '收货库区',
      rules: 'required',
    },
  ],
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  fullscreenButton: false,
  closeOnClickModal: false,
  draggable: true,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await formApi.submitForm();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      data.value = modalApi.getData<Record<string, any>>();
      modalApi.setState({ title: `进仓确认 - ${data.value.doc}` });
      formApi.resetForm();
      fetch();
    }
  },
  title: '进仓确认',
});

async function onSubmit(values: Record<string, any>) {
  const check = await formApi.validate();
  if (check.valid) {
    modalApi.setState({ confirmLoading: true });
    modalApi.setState({ loading: true });
    bwInReturnInStatusConfirm(Object.assign({ id: data.value.id }, values))
      .then(() => {
        handleClose();
        message.success('操作成功！');
      })
      .catch(() => {})
      .finally(() => {
        modalApi.setState({ loading: false });
        modalApi.setState({ confirmLoading: false });
      });
  }
}
function handleClose() {
  modalApi.close();
  emit('success');
}
async function fetch() {
  const res = await bwInReturnInStatusCheck(data.value.id);
  formApi.updateSchema([
    {
      componentProps: {
        options: res.areaOptions,
      },
      fieldName: 'InArea',
    },
  ]);
  formApi.setValues(res.info);
}
</script>
<template>
  <Modal>
    <Form />
  </Modal>
</template>
