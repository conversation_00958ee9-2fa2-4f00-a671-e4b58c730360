<script lang="ts" setup>
import type { VxeGridProps } from '#/adapter';

import { reactive, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { AntClear } from '@vben/icons';

import { message, Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import {
  getBWStockDetailList,
  stockTakingItemImport,
} from '#/api/bondedwarehouse/stock';
import { getOptions } from '#/api/common';

const emit = defineEmits(['success']);

const data = ref();
const searchField: any = reactive({
  MatCode: undefined,
  BatchNo: undefined,
});

const [Modal, modalApi] = useVbenModal({
  class: 'w-full h-full',
  destroyOnClose: true,
  fullscreenButton: true,
  closeOnClickModal: false,
  /* footer: false, */
  zIndex: 900,
  confirmText: '添加明细',
  onCancel() {
    handleClose();
  },
  onConfirm: async () => {
    await handleImport();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      data.value = modalApi.getData<Record<string, any>>();
      fetch();
    }
  },
  onBeforeClose() {
    emit('success');
    return true;
  },
  title: '库存盘点 - 选择项目',
});
const gridOptions = reactive<VxeGridProps>({
  id: 'StockTakingItemAdd',
  checkboxConfig: {
    highlight: true,
    range: true,
    checkMethod: ({ row }) => {
      return !(row.LockedByTaking || row.LockedByMovement);
    },
  },
  columns: [
    { type: 'checkbox', width: 40 },
    {
      field: 'Division',
      title: 'Division',
      width: 88,
      filters: [],
    },
    {
      field: 'BrandName',
      title: '品牌',
      width: 100,
      filters: [],
    },
    {
      field: 'AreaName',
      title: '库区',
      width: 100,
      filters: [],
    },
    {
      field: 'StockStatus',
      title: '库存状态',
      width: 88,
      filters: [],
    },
    {
      field: 'StorageLocation',
      title: 'SAP位置',
      width: 96,
      filters: [],
    },
    {
      field: 'MatCode',
      title: '物料编号',
      width: 96,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'MaterialType',
      title: '物料类型',
      width: 88,
      filters: [],
    },
    /* {
      field: 'isBatchManagementRequired',
      title: '批次管理',
      width: 88,
      align: 'center',
      slots: { default: 'batchmanage' },
      filters: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      filterMultiple: false,
    }, */
    {
      field: 'BatchNo',
      title: '批号',
      width: 80,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    /* {
      field: 'BatchStatus',
      title: '批次状态',
      width: 88,
      slots: { default: 'batchstatus' },
      filters: [
        { label: '正常', value: 0 },
        { label: '限制', value: 1 },
        { label: '不存在', value: -1 },
      ],
    }, */
    /* {
      field: 'ShelfLifeExpirationDate',
      title: '批次效期',
      width: 108,
      formatter: 'formatDate',
      sortable: true,
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'ActualExpiration',
      title: '实物效期',
      width: 88,
      formatter: 'formatDate',
    }, */
    { field: 'StockQty', title: '库存数量', width: 108 },
    { field: 'UnrestrictedQty', title: '非限制库存', width: 108 },
    { field: 'BlockedQty', title: '冻结库存', width: 100 },
    {
      field: 'HBL',
      title: 'HBL',
      width: 136,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'PalletNo',
      title: '入库托盘',
      width: 100,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },

    {
      field: 'BIN',
      title: '货位',
      width: 88,
    },
    {
      field: 'IsWorking',
      title: '正操作',
      width: 80,
      align: 'center',
      filters: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      filterMultiple: false,
      slots: { default: 'isworking' },
    },
    {
      field: 'LockedByMovement',
      title: '调整锁定',
      width: 88,
      filters: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      filterMultiple: false,
      formatter({ cellValue }) {
        return cellValue ? '是' : '否';
      },
    },
    {
      field: 'LockedByTaking',
      title: '盘点锁定',
      width: 88,
      filters: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      filterMultiple: false,
      formatter({ cellValue }) {
        return cellValue ? '是' : '否';
      },
    },
    {
      field: 'LockedByAPI',
      title: 'API锁定',
      width: 88,
      filters: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      filterMultiple: false,
      formatter({ cellValue }) {
        return cellValue ? '是' : '否';
      },
    },
    /*  {
      title: '操作',
      slots: { default: 'action' },
      width: 72,
      align: 'center',
      fixed: 'right',
    }, */
  ],
  expandConfig: {
    accordion: true,
  },
  filterConfig: {
    remote: true,
  },
  height: 'auto',
  pagerConfig: {
    pageSize: 50,
  },
  proxyConfig: {
    ajax: {
      query: ({ filters, page, sorts }) => {
        const queryParams: any = Object.assign(
          {
            WarehouseCode: data.value.warehouse,
            OwnerCode: data.value.owner,
            LockedByTaking: false,
            /* IsWorking: false,
            StockStatus: 'Inbound', */
          },
          page,
        );
        // 处理排序条件
        if (sorts.length === 0) {
          queryParams.sort = 'AreaName desc';
        } else {
          const sortItem = sorts.map((item) => {
            const newItem = `${item.field} ${item.order}`;
            return newItem;
          });
          queryParams.sort = sortItem.join(',');
        }
        // 处理筛选
        Object.getOwnPropertyNames(searchField).forEach((key) => {
          searchField[key] = undefined;
        });
        filters.forEach(({ field, values }) => {
          queryParams[field] = values.join(',');
          if (Object.prototype.hasOwnProperty.call(searchField, field)) {
            const jsonObject = JSON.parse(values.toString());
            searchField[field] = jsonObject.text;
          }
        });
        return getBWStockDetailList(queryParams);
      },
    },
    seq: true,
  },
  /* scrollY: { enabled: true, gt: 0 }, */
  size: 'mini',
  sortConfig: {
    remote: true,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
});

const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });

function handleImport() {
  const checkedRecords = gridApi.grid
    .getCheckboxRecords()
    .map((item: any) => item.Guid);
  if (checkedRecords.length === 0) {
    message.info(`请勾选需要添加的库存明细`);
    return;
  }
  modalApi.setState({ confirmLoading: true });
  modalApi.setState({ loading: true });
  stockTakingItemImport(data.value.id, checkedRecords)
    .then(() => {
      handleClose();
    })
    .catch(() => {})
    .finally(() => {
      modalApi.setState({ loading: false });
      modalApi.setState({ confirmLoading: false });
    });
}
function handleClose() {
  modalApi.close();
  emit('success');
}
function searchEvent(field: string) {
  const column = gridApi.grid.getColumnByField(field);
  if (column) {
    // 修改第一个选项为勾选状态
    const option = column.filters[0];
    if (!option) {
      return;
    }

    if (searchField[field]) {
      option.data = { type: '1', text: searchField[field] };
      option.value = JSON.stringify(option.data);
      option.checked = true;
    } else {
      option.data = { type: '0', text: undefined };
      option.checked = false;
    }

    // 修改条件之后，需要手动调用 updateData 处理表格数据
    gridApi.grid.updateData();
    gridApi.grid.commitProxy('query');
  }
}
/* function handleSuccess() {
  gridApi.grid.commitProxy('query');
} */
function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
  gridApi.grid.commitProxy('query');
}
async function fetch() {
  const options = await getOptions(`StockTaking_${data.value.warehouse}`);
  const fieldList = [
    'AreaName',
    'StockStatus',
    'StorageLocation',
    'MaterialType',
    'BrandName',
    'Division',
  ];
  fieldList.forEach((field) => {
    gridApi.grid.setFilter(
      field,
      options.filter((item: any) => {
        return item.type === field;
      }),
    );
  });
}
</script>
<template>
  <Modal>
    <Grid>
      <template #toolbar_left>
        <a-button class="mr-1" @click="clearFilterAndSort()">
          <AntClear class="size-5" />
        </a-button>
        <div class="hidden pl-1 md:block">
          <a-input-search
            v-model:value="searchField.MatCode"
            allow-clear
            class="mr-2 w-60"
            placeholder="物料编号"
            @search="searchEvent('MatCode')"
          />
          <!-- <a-input-search
            v-model:value="searchField.BatchNo"
            allow-clear
            class="mr-2 w-44"
            placeholder="批号"
            @search="searchEvent('BatchNo', 'Input')"
          /> -->
        </div>
      </template>
      <template #toolbar-tools> </template>
      <template #batchmanage="{ row }">
        <Tag :color="row.isBatchManagementRequired ? 'green' : 'red'">
          {{ row.isBatchManagementRequired ? '是' : '否' }}
        </Tag>
      </template>

      <template #isworking="{ row }">
        <Tag :color="row.IsWorking ? 'blue' : 'green'">
          {{ row.IsWorking ? '是' : '否' }}
        </Tag>
      </template>
    </Grid>
  </Modal>
</template>
