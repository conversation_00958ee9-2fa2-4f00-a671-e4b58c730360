<script setup lang="ts">
import type { VxeGridProps } from '#/adapter';

import { onMounted, reactive } from 'vue';

import { Page } from '@vben/common-ui';
import { AntClear } from '@vben/icons';

import { useVbenVxeGrid } from '#/adapter';
import { getRecord4BWOutDispatch } from '#/api/bondedwarehouse/outbound';

const searchField: any = reactive({
  DispatchNo: undefined,
  MatCode: undefined,
});
const qty: any = reactive({
  GoodsNum: 0,
});
const gridOptions = reactive<VxeGridProps>({
  id: 'Record4BWOutboundPicking',
  columns: [
    {
      fixed: 'left',
      title: '#',
      type: 'seq',
      width: 60,
    },
    {
      field: 'DispatchNo',
      title: '派车单号',
      width: 112,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'AddressTo',
      title: '目的地仓库',
      width: 112,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'DeliveryDate',
      title: '发货日期',
      width: 136,
      formatter: 'formatDateTime',
      filterRender: { name: 'FilterDateRange' },
      filters: [{ data: { date: [], type: '0' } }],
    },
    {
      field: 'LicensePlate',
      title: '车牌号',
      width: 88,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'TruckModel',
      title: '车型',
      width: 72,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'MatCode',
      title: '物料编号',
      width: 88,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'BatchNo',
      title: '批号',
      width: 80,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'GoodsNum',
      title: '产品数量',
      width: 88,
      filters: [{ data: { type: '0', num1: undefined, num2: undefined } }],
      filterRender: { name: 'FilterNumberRange' },
    },
    {
      field: 'DeliveryGoodsType',
      title: '产品分类',
      width: 88,
      filters: [
        { label: '正常', value: '正常' },
        { label: '留样', value: '留样' },
        { label: 'CIQ', value: 'CIQ' },
        { label: '搁置', value: '搁置' },
      ],
    },
    {
      field: 'NewPalletNo',
      title: '出库托盘',
      width: 100,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'NewSSCC',
      title: '出库SSCC',
      width: 144,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'SSCC',
      title: '分类SSCC',
      width: 144,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'YUB',
      title: 'SAP销售订单',
      width: 112,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'Outbound',
      title: 'Outbound',
      width: 100,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'CreateDate',
      title: '装载日期',
      width: 136,
      formatter: 'formatDateTime',
      filterRender: { name: 'FilterDateRange' },
      filters: [{ data: { date: [], type: '0' } }],
    },
    {
      field: 'PickingDocNo',
      title: '拣货单',
      width: 160,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    { field: 'oBrandName', title: '品牌', width: 100, visible: false },
    { field: 'oCName', title: '中文品名', minWidth: 220, visible: false },
  ],
  filterConfig: {
    remote: true,
  },
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: ({ filters, page, sorts }) => {
        const queryParams: any = Object.assign({}, page);
        // 处理排序条件
        if (sorts.length === 0) {
          queryParams.sort = '_Identify desc';
        } else {
          const sortItem = sorts.map((item) => {
            const newItem = `${item.field} ${item.order}`;
            return newItem;
          });
          queryParams.sort = sortItem.join(',');
        }
        // 处理筛选
        Object.getOwnPropertyNames(searchField).forEach((key) => {
          searchField[key] = undefined;
        });
        filters.forEach(({ field, values }) => {
          queryParams[field] = values.join(',');
          if (Object.prototype.hasOwnProperty.call(searchField, field)) {
            const jsonObject = JSON.parse(values.toString());
            searchField[field] = jsonObject.text;
          }
        });
        return getRecord4BWOutDispatch(queryParams)
          .then((res) => {
            Object.assign(qty, res.qty);
            return res;
          })
          .catch(() => {})
          .finally(() => {});
      },
    },
    seq: true,
  },
  showFooter: true,
  scrollX: { enabled: true, gt: 0 },
  scrollY: { enabled: true, gt: 0 },
  size: 'mini',
  sortConfig: {
    remote: true,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
  footerRowStyle: { 'font-weight': 'bold ', backgroundColor: '#ebfbee' },
  footerMethod({ columns }) {
    return [
      columns.map((column, columnIndex) => {
        if (columnIndex === 0) {
          return `合计`;
        }
        if (
          [
            'CIQQty',
            'GoodsNum',
            'MissingQty',
            'PickingQty',
            'ShelveQty',
          ].includes(column.field)
        ) {
          return qty[column.field];
        }

        return null;
      }),
    ];
  },
});
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });
function searchEvent(field: string) {
  const column = gridApi.grid.getColumnByField(field);
  if (column) {
    // 修改第一个选项为勾选状态
    const option = column.filters[0];
    if (!option) {
      return;
    }

    if (searchField[field]) {
      option.data = { type: '1', text: searchField[field] };
      option.value = JSON.stringify(option.data);
      option.checked = true;
    } else {
      option.data = { type: '0', text: undefined };
      option.checked = false;
    }

    // 修改条件之后，需要手动调用 updateData 处理表格数据
    gridApi.grid.updateData();
    gridApi.grid.commitProxy('query');
  }
}
function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
  gridApi.grid.commitProxy('query');
}
async function fetch() {}
onMounted(() => {
  setTimeout(() => {
    fetch();
  }, 300);
});
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar_left>
        <a-button class="mr-1" @click="clearFilterAndSort()">
          <AntClear class="size-5" />
        </a-button>
        <div class="hidden pl-1 md:block">
          <a-input-search
            v-model:value="searchField.BLNo"
            allow-clear
            class="mr-2 w-60"
            placeholder="出库提单BL"
            @search="searchEvent('BLNo')"
          />
        </div>
      </template>
      <!-- <template #isBatchDiff="{ row }">
        <Tag v-if="row.TallyStatus" :color="row.IsBatchDiff ? 'red' : 'green'">
          {{ row.IsBatchDiff ? '有差异' : '无差异' }}
        </Tag>
      </template> -->
      <template #toolbar-tools> </template>
    </Grid>
  </Page>
</template>
