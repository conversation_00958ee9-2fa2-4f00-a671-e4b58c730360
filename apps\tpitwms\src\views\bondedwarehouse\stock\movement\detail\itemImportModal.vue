<script lang="ts" setup>
import type { VxeGridProps } from '#/adapter';

import { reactive, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { AntClear } from '@vben/icons';

import { Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import { getBWStockDetailList } from '#/api/bondedwarehouse/stock';
import { getOptions } from '#/api/common';

import singleAddModal from './singleAddModal.vue';

const emit = defineEmits(['success']);

const data = ref();
const searchField: any = reactive({
  MatCode: undefined,
  BatchNo: undefined,
});

const [SingleAddModal, SingleAddModalApi] = useVbenModal({
  connectedComponent: singleAddModal,
});
function openSingleAddModal(stockId: string) {
  SingleAddModalApi.setData({ headId: data.value.id, stockId });
  SingleAddModalApi.open();
}

const [Modal, modalApi] = useVbenModal({
  class: 'w-full h-full',
  destroyOnClose: true,
  fullscreenButton: true,
  closeOnClickModal: false,
  footer: false,
  zIndex: 900,
  /* confirmText: '确认导入', */
  onCancel() {
    handleClose();
  },
  onConfirm: async () => {},
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      data.value = modalApi.getData<Record<string, any>>();
      fetch();
    }
  },
  onBeforeClose() {
    emit('success');
    return true;
  },
  title: '库存调整 - 选择项目',
});
const gridOptions = reactive<VxeGridProps>({
  id: 'StockMovementItemAdd',
  /* checkboxConfig: { highlight: true, range: true }, */
  columns: [
    /* { type: 'checkbox', width: 40 }, */
    {
      field: 'StockStatus',
      title: '库存状态',
      width: 88,
      filters: [
        { label: '入库收货', value: 'Inbound' },
        { label: '出库理货', value: 'OutTally' },
      ],
    },
    {
      field: 'StorageLocation',
      title: 'SAP位置',
      width: 96,
      filters: [],
    },
    {
      field: 'MatCode',
      title: '物料编号',
      width: 96,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      /* slots: { default: 'material' }, */
    },
    {
      field: 'MaterialType',
      title: '物料类型',
      width: 88,
      filters: [],
    },
    {
      field: 'isBatchManagementRequired',
      title: '批次管理',
      width: 88,
      align: 'center',
      slots: { default: 'batchmanage' },
      filters: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      filterMultiple: false,
    },
    {
      field: 'BatchNo',
      title: '批次',
      width: 80,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'BatchStatus',
      title: '批次状态',
      width: 88,
      slots: { default: 'batchstatus' },
      filters: [
        { label: '正常', value: 0 },
        { label: '限制', value: 1 },
        { label: '不存在', value: -1 },
      ],
    },
    /* {
      field: 'ShelfLifeExpirationDate',
      title: '批次效期',
      width: 108,
      formatter: 'formatDate',
      sortable: true,
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'ActualExpiration',
      title: '实物效期',
      width: 88,
      formatter: 'formatDate',
    }, */
    {
      field: 'StockQty',
      title: '库存数量',
      width: 108,
      filters: [{ data: { type: '0', num1: undefined, num2: undefined } }],
      filterRender: { name: 'FilterNumberRange' },
    },
    {
      field: 'UnrestrictedQty',
      title: '非限制库存',
      width: 108,
      filters: [{ data: { type: '0', num1: undefined, num2: undefined } }],
      filterRender: { name: 'FilterNumberRange' },
    },
    {
      field: 'BlockedQty',
      title: '冻结库存',
      width: 100,
      filters: [{ data: { type: '0', num1: undefined, num2: undefined } }],
      filterRender: { name: 'FilterNumberRange' },
    },
    {
      field: 'DocNo',
      title: '单据',
      width: 136,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'PalletNo',
      title: '入库托盘',
      width: 100,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'OwnerShortName',
      title: '货主',
      width: 120,
      filters: [],
    },
    {
      field: 'WarehouseName',
      title: '仓库',
      width: 88,
      filters: [],
    },
    /* {
      field: 'IsWorking',
      title: '正操作',
      width: 80,
      align: 'center',
      filters: [
        { label: '是', value: 1 },
        { label: '否', value: 0 },
      ],
      filterMultiple: false,
      slots: { default: 'isworking' },
    }, */
    {
      field: 'LockedByMovement',
      title: '调整锁定',
      width: 88,
      filters: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      filterMultiple: false,
      formatter({ cellValue }) {
        return cellValue ? '是' : '否';
      },
    },
    {
      field: 'LockedByTaking',
      title: '盘点锁定',
      width: 88,
      filters: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      filterMultiple: false,
      formatter({ cellValue }) {
        return cellValue ? '是' : '否';
      },
    },
    {
      field: 'LockedByAPI',
      title: 'API锁定',
      width: 88,
      filters: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      filterMultiple: false,
      formatter({ cellValue }) {
        return cellValue ? '是' : '否';
      },
    },
    {
      title: '操作',
      slots: { default: 'action' },
      width: 72,
      align: 'center',
      fixed: 'right',
    },
  ],
  expandConfig: {
    accordion: true,
  },
  filterConfig: {
    remote: true,
  },
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: ({ filters, page, sorts }) => {
        const queryParams: any = Object.assign(
          {
            IsWorking: false,
            StockStatus: 'Inbound,OutTally',
          },
          page,
        );
        // 处理排序条件
        if (sorts.length === 0) {
          queryParams.sort = '_Identify desc';
        } else {
          const sortItem = sorts.map((item) => {
            const newItem = `${item.field} ${item.order}`;
            return newItem;
          });
          queryParams.sort = sortItem.join(',');
        }
        // 处理筛选
        Object.getOwnPropertyNames(searchField).forEach((key) => {
          searchField[key] = undefined;
        });
        filters.forEach(({ field, values }) => {
          queryParams[field] = values.join(',');
          if (Object.prototype.hasOwnProperty.call(searchField, field)) {
            const jsonObject = JSON.parse(values.toString());
            searchField[field] = jsonObject.text;
          }
        });
        return getBWStockDetailList(queryParams);
      },
    },
    seq: true,
  },
  /* scrollY: { enabled: true, gt: 0 }, */
  size: 'mini',
  sortConfig: {
    remote: true,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
});

const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });

function handleClose() {
  modalApi.close();
  emit('success');
}
function searchEvent(field: string) {
  const column = gridApi.grid.getColumnByField(field);
  if (column) {
    // 修改第一个选项为勾选状态
    const option = column.filters[0];
    if (!option) {
      return;
    }

    if (searchField[field]) {
      option.data = { type: '1', text: searchField[field] };
      option.value = JSON.stringify(option.data);
      option.checked = true;
    } else {
      option.data = { type: '0', text: undefined };
      option.checked = false;
    }

    // 修改条件之后，需要手动调用 updateData 处理表格数据
    gridApi.grid.updateData();
    gridApi.grid.commitProxy('query');
  }
}
function handleSuccess() {
  gridApi.grid.commitProxy('query');
}
function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
  gridApi.grid.commitProxy('query');
}
async function fetch() {
  const options = await getOptions('StockDetail');
  const fieldList = [
    /* 'StockStatus', */
    'StorageLocation',
    'MaterialType',
    'BrandName',
    'OwnerShortName',
    'WarehouseName',
    'Division',
    'OriginName',
  ];
  fieldList.forEach((field) => {
    gridApi.grid.setFilter(
      field,
      options.filter((item: any) => {
        return item.type === field;
      }),
    );
  });
}
</script>
<template>
  <Modal>
    <Grid>
      <template #toolbar_left>
        <a-button class="mr-1" @click="clearFilterAndSort()">
          <AntClear class="size-5" />
        </a-button>
        <div class="hidden pl-1 md:block">
          <a-input-search
            v-model:value="searchField.MatCode"
            allow-clear
            class="mr-2 w-60"
            placeholder="物料编号"
            @search="searchEvent('MatCode')"
          />
          <!-- <a-input-search
            v-model:value="searchField.BatchNo"
            allow-clear
            class="mr-2 w-60"
            placeholder="批号"
            @search="searchEvent('BatchNo')"
          /> -->
        </div>
      </template>
      <template #toolbar-tools> </template>
      <template #batchmanage="{ row }">
        <Tag :color="row.isBatchManagementRequired ? 'green' : 'red'">
          {{ row.isBatchManagementRequired ? '是' : '否' }}
        </Tag>
      </template>
      <template #batchstatus="{ row }">
        <Tag
          :color="
            row.BatchStatus === 0
              ? 'green'
              : row.BatchStatus === 1
                ? 'orange'
                : 'red'
          "
        >
          {{
            row.BatchStatus === 0
              ? '正常'
              : row.BatchStatus === 1
                ? '限制'
                : '不存在'
          }}
        </Tag>
      </template>
      <!-- <template #isworking="{ row }">
        <Tag :color="row.IsWorking ? 'blue' : 'green'">
          {{ row.IsWorking ? '是' : '否' }}
        </Tag>
      </template> -->
      <template #action="{ row }">
        <a-button
          :disabled="
            row.LockedByMovement || row.LockedByTaking || row.LockedByAPI
          "
          size="small"
          type="primary"
          @click="openSingleAddModal(row.Guid)"
        >
          调整
        </a-button>
      </template>
    </Grid>
    <SingleAddModal @success="handleSuccess()" />
  </Modal>
</template>
