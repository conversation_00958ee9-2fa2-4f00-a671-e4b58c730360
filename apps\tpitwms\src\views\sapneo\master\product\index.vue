<script setup lang="ts">
import type { VxeGridProps } from '#/adapter';

import { reactive } from 'vue';

import { Page, useVbenDrawer } from '@vben/common-ui';
import { AntClear } from '@vben/icons';

import { Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import { getProductList } from '#/api/sapneo';

import skuDrawer from './skuDrawer.vue';

const [SkuDrawer, skuDrawerApi] = useVbenDrawer({
  // 连接抽离的组件
  connectedComponent: skuDrawer,
});
function openSkuDrawer(id: string) {
  skuDrawerApi.setData({ id });
  skuDrawerApi.open();
}

const searchField: any = reactive({
  skuCode: undefined,
  /* productStandardId: undefined, */
});

const gridOptions = reactive<VxeGridProps>({
  id: 'NEOProductMaster',
  columns: [
    {
      title: '#',
      type: 'seq',
      width: 60,
      fixed: 'left',
    },
    {
      field: 'skuCode',
      title: '产品编号',
      fixed: 'left',
      width: 108,
      /* sortable: true, */
      slots: { default: 'skucode' },
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'productStandardId',
      title: '产品条码',
      minWidth: 112,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'materialType',
      title: '产品类型',
      width: 88,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      visible: false,
    },
    {
      field: 'MatType',
      title: '物料类型',
      width: 88,
      filters: [
        { label: 'YFG', value: 'YFG' },
        { label: 'YSM2', value: 'YSM2' },
        { label: 'YPL2', value: 'YPL2' },
      ],
    },
    {
      field: 'productHierarchy',
      title: '产品结构代码',
      width: 152,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      visible: false,
    },
    {
      field: 'logisticDescription',
      title: '中文品名',
      minWidth: 180,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'rootDescription',
      title: '英文品名',
      minWidth: 160,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'materialGroup',
      title: '分组',
      width: 88,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      visible: false,
    },
    {
      field: 'signatureSapS4Code',
      title: '品牌',
      width: 80,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      visible: false,
    },
    {
      field: 'isBatchManagementRequired',
      title: '批次管理',
      width: 88,
      align: 'center',
      slots: { default: 'batchmanage' },
      filters: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      filterMultiple: false,
    },
    {
      field: 'productCarriesAntidiversionMark',
      title: '防串货标识',
      width: 100,
      align: 'center',
      slots: { default: 'antidiversion' },
      filters: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      filterMultiple: false,
      visible: false,
    },
    {
      field: 'lifespanInDays',
      title: '效期-天',
      width: 100,
      filters: [{ data: { type: '0', num1: undefined, num2: undefined } }],
      filterRender: { name: 'FilterNumberRange' },
      sortable: true,
    },
    {
      field: 'GS1NetContentMetric',
      title: '产品规格',
      width: 88,
      formatter({ row }) {
        return row.GS1NetContentMetric + row.GS1NetContentMetricUnit;
      },
    },
    {
      field: 'cuWidthInMm',
      title: '长宽高-mm',
      width: 120,
      formatter({ row }) {
        return `${row.cuDepthInMm} * ${row.cuWidthInMm} * ${row.cuHeightInMm}`;
      },
    },
    {
      field: 'cuVolumeInCm3',
      title: '体积-cm3',
      width: 88,
      visible: false,
    },
    {
      field: 'cuGrossWeightInG',
      title: '毛重-g',
      width: 88,
      visible: false,
    },
    {
      field: 'PCB',
      title: 'PCB',
      width: 72,
      filters: [{ data: { type: '0', num1: undefined, num2: undefined } }],
      filterRender: { name: 'FilterNumberRange' },
    },
    {
      field: 'SPCB',
      title: 'SPCB',
      width: 80,
      filters: [{ data: { type: '0', num1: undefined, num2: undefined } }],
      filterRender: { name: 'FilterNumberRange' },
    },
    {
      field: 'layerSupportQuantity',
      title: '层规件数',
      width: 88,
      filters: [{ data: { type: '0', num1: undefined, num2: undefined } }],
      filterRender: { name: 'FilterNumberRange' },
    },
    {
      field: 'palletSupportQuantity',
      title: '托规件数',
      width: 88,
      filters: [{ data: { type: '0', num1: undefined, num2: undefined } }],
      filterRender: { name: 'FilterNumberRange' },
    },

    {
      field: 'hazardousMaterialNumber',
      title: '危险品编号',
      width: 100,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      visible: false,
    },
    {
      field: 'crossPlantStatus',
      title: 'crossPlantStatus',
      width: 120,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      visible: false,
    },
    /* {
      field: 'mmStatus',
      title: 'mmStatus',
      width: 88,
    }, */
    {
      field: 'CreateDate',
      title: '创建日期',
      width: 136,
      formatter: 'formatDateTime',
      sortable: true,
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'UpdateDate',
      title: '更新日期',
      width: 136,
      formatter: 'formatDateTime',
      sortable: true,
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
  ],
  filterConfig: {
    remote: true,
  },
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: ({ filters, page, sorts }) => {
        const queryParams: any = Object.assign({}, page);
        // 处理排序条件
        if (sorts.length === 0) {
          queryParams.sort = 'CreateDate desc';
        } else {
          const sortItem = sorts.map((item) => {
            const newItem = `${item.field} ${item.order}`;
            return newItem;
          });
          queryParams.sort = sortItem.join(',');
        }
        // 处理筛选
        Object.getOwnPropertyNames(searchField).forEach((key) => {
          searchField[key] = undefined;
        });
        filters.forEach(({ field, values }) => {
          queryParams[field] = values.join(',');
          if (Object.prototype.hasOwnProperty.call(searchField, field)) {
            const jsonObject = JSON.parse(values.toString());
            searchField[field] = jsonObject.text;
          }
        });
        return getProductList(queryParams);
      },
    },
    seq: true,
  },
  scrollX: { enabled: true, gt: 0 },
  scrollY: { enabled: true, gt: 0 },
  size: 'mini',
  sortConfig: {
    remote: true,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
});
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });
function searchEvent(field: string) {
  const column = gridApi.grid.getColumnByField(field);
  if (column) {
    // 修改第一个选项为勾选状态
    const option = column.filters[0];
    if (!option) {
      return;
    }

    if (searchField[field]) {
      option.data = { type: '1', text: searchField[field] };
      option.value = JSON.stringify(option.data);
      option.checked = true;
    } else {
      option.data = { type: '0', text: undefined };
      option.checked = false;
    }

    // 修改条件之后，需要手动调用 updateData 处理表格数据
    gridApi.grid.updateData();
    gridApi.grid.commitProxy('query');
  }
}
function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
  gridApi.grid.commitProxy('query');
}
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar_left>
        <a-button class="mr-1" @click="clearFilterAndSort()">
          <AntClear class="size-5" />
        </a-button>
        <div class="hidden pl-1 md:block">
          <a-input-search
            v-model:value="searchField.skuCode"
            allow-clear
            class="mr-2 w-60"
            placeholder="产品编号"
            @search="searchEvent('skuCode')"
          />
        </div>
      </template>
      <template #toolbar-tools> </template>
      <template #skucode="{ row }">
        <a-button size="small" type="link" @click="openSkuDrawer(row.skuCode)">
          {{ row.skuCode }}
        </a-button>
      </template>
      <template #antidiversion="{ row }">
        <Tag :color="row.productCarriesAntidiversionMark ? 'green' : 'red'">
          {{ row.productCarriesAntidiversionMark ? '是' : '否' }}
        </Tag>
      </template>
      <template #batchmanage="{ row }">
        <Tag :color="row.isBatchManagementRequired ? 'green' : 'red'">
          {{ row.isBatchManagementRequired ? '是' : '否' }}
        </Tag>
      </template>
    </Grid>
    <SkuDrawer />
  </Page>
</template>
