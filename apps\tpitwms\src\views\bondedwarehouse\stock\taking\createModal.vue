<script lang="ts" setup>
import { reactive, ref } from 'vue';
import { useRouter } from 'vue-router';

import { useVbenModal } from '@vben/common-ui';

import { Result } from 'ant-design-vue';

import { useVbenForm, z } from '#/adapter';
import {
  stockTakingCreate,
  stockTakingGetDoc,
} from '#/api/bondedwarehouse/stock';
import { getOptions } from '#/api/common';

const emit = defineEmits(['success']);
const cName = reactive({
  OwnerShortName: undefined,
  WarehouseName: undefined,
});
const docId = ref(undefined);
const docNo = ref(undefined);
const router = useRouter();
const [Form, formApi] = useVbenForm({
  handleSubmit: onSubmit,
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  schema: [
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 24,
      },
      fieldName: 'TakingNo',
      label: '调整单号',
      rules: z
        .string()
        .min(8, { message: '至少需要8个字符' })
        .regex(/^[A-Z0-9]+$/, { message: '只能包含大写字母或数字' }),
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        getPopupContainer: () => document.body,
        onSelect: (_value: string, option: any) => {
          cName.OwnerShortName = option.label;
        },
      },
      fieldName: 'OwnerCode',
      label: '货主名称',
      rules: 'required',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        getPopupContainer: () => document.body,
        onSelect: (_value: string, option: any) => {
          cName.WarehouseName = option.label;
        },
      },
      fieldName: 'WarehouseCode',
      label: '仓库',
      rules: 'required',
    },
    {
      component: 'DatePicker',
      componentProps: {
        valueFormat: 'YYYY-MM-DD',
        format: 'YYYY-MM-DD',
        style: { width: '100%' },
        allowClear: true,
      },
      fieldName: 'PreTakingDate',
      label: '计划盘点日期',
      rules: 'required',
    },
    {
      component: 'Switch',
      componentProps: {
        class: 'w-auto',
      },
      fieldName: 'IsBright',
      label: '是否明盘',
      defaultValue: true,
      rules: 'required',
    },
  ],
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  fullscreenButton: false,
  closeOnClickModal: false,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await formApi.submitForm();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      modalApi.setState({ showCancelButton: true });
      modalApi.setState({ showConfirmButton: true });
      docId.value = undefined;
      docNo.value = undefined;
      formApi.resetForm();
      fetch();
    }
  },
  title: '创建盘点作业单',
});

async function onSubmit(values: Record<string, any>) {
  const check = await formApi.validate();
  if (check.valid) {
    modalApi.setState({ confirmLoading: true });
    modalApi.setState({ loading: true });
    stockTakingCreate(Object.assign({}, values, cName))
      .then((res) => {
        modalApi.setState({ showCancelButton: false });
        modalApi.setState({ showConfirmButton: false });
        docId.value = res;
      })
      .catch(() => {})
      .finally(() => {
        modalApi.setState({ loading: false });
        modalApi.setState({ confirmLoading: false });
      });
  }
}
function handleClose() {
  modalApi.close();
  emit('success');
}
function handleGoDetail() {
  modalApi.close();
  router.push({
    name: 'BWStockTakingDetail',
    params: { id: docId.value },
  });
  emit('success');
}
async function fetch() {
  docNo.value = await stockTakingGetDoc();
  formApi.setFieldValue('TakingNo', docNo.value);
  const options = await getOptions('BWInOrderCreate');
  const fieldList = ['OwnerCode', 'WarehouseCode'];
  fieldList.forEach((field) => {
    formApi.updateSchema([
      {
        componentProps: {
          options: options.filter((item: any) => {
            return item.type === field;
          }),
        },
        fieldName: field,
      },
    ]);
  });
}
</script>
<template>
  <Modal>
    <Result v-if="docId" status="success" title="创建成功">
      <template #extra>
        <a-button type="primary" @click="handleGoDetail()">
          查看盘点作业
        </a-button>
        <a-button @click="handleClose()">关闭</a-button>
      </template>
    </Result>
    <Form v-else class="mt-4" />
  </Modal>
</template>
