<script lang="ts" setup>
import type { VxeGridProps } from '#/adapter';

import { reactive, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { AntDelete } from '@vben/icons';

import { message, Modal, Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import {
  bwInOrderItemSplitRepeal,
  getInOrderItemSplit,
} from '#/api/bondedwarehouse/inbound';

const emit = defineEmits(['success']);

const data = ref();
const [SplitModal, modalApi] = useVbenModal({
  class: 'w-full h-full',
  fullscreenButton: true,
  closeOnClickModal: false,
  showConfirmButton: false,
  zIndex: 900,
  onCancel() {
    modalApi.close();
  },
  /* onConfirm: async () => {
    await handleImport();
  }, */
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      data.value = modalApi.getData<Record<string, any>>();
      /* fetch(); */
    }
  },
  title: '订单明细拆分记录',
});
const gridOptions = reactive<VxeGridProps>({
  id: 'BWInOrderItemSplit',
  columns: [
    {
      field: 'MatCode',
      title: '物料编号',
      minWidth: 136,
      treeNode: true,
    },
    {
      field: 'InboundBatch',
      title: '产品批次',
      minWidth: 80,
    },
    {
      field: 'OriginQty',
      title: '原始数量',
      minWidth: 80,
    },
    {
      field: 'CreateDate',
      title: '拆分日期',
      minWidth: 136,
      formatter: 'formatDateTime',
      sortable: true,
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'NewQty',
      title: '拆分项1数量',
      minWidth: 80,
    },
    {
      field: 'NewStatus',
      title: '拆分项1状态',
      minWidth: 80,
      slots: { default: 'newstatus' },
    },
    {
      field: 'SplitQty',
      title: '拆分项2数量',
      minWidth: 80,
    },
    {
      field: 'SplitStatus',
      title: '拆分项2状态',
      minWidth: 80,
      slots: { default: 'splitstatus' },
    },
    {
      field: 'CreateBy',
      title: '操作人员',
      minWidth: 80,
    },
    {
      title: '撤销拆分',
      slots: { default: 'action' },
      width: 72,
      align: 'center',
    },
  ],
  filterConfig: {
    remote: false,
  },
  height: 'auto',
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    ajax: {
      query: async () => {
        return await getInOrderItemSplit(data.value.id);
      },
    },
    seq: true,
  },
  size: 'mini',
  sortConfig: {
    remote: false,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
  treeConfig: {
    transform: true,
    showLine: true,
    expandAll: true,
    rowField: 'Guid',
    parentField: 'ParentID',
  },
});

const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });

function showDeleteModal(id: string, matCode: string, originQty: number) {
  Modal.confirm({
    content: `请确认是否撤销拆分？`,
    /* icon: createVNode(ExclamationCircleOutlined), */
    onCancel() {},
    onOk() {
      handleDelete(id);
    },
    title: `物料编号: ${matCode}，原始数量：${originQty}`,
    okButtonProps: { danger: true },
  });
}

function handleDelete(id: string) {
  bwInOrderItemSplitRepeal(id)
    .then(() => {
      message.success('操作成功');
      handleSuccess();
      emit('success');
    })
    .catch(() => {})
    .finally(() => {});
}

function handleSuccess() {
  gridApi.grid.commitProxy('query');
}
</script>
<template>
  <SplitModal>
    <Grid>
      <template #action="{ row }">
        <a-button
          danger
          size="small"
          type="link"
          @click="showDeleteModal(row.Guid, row.MatCode, row.OriginQty)"
        >
          <AntDelete class="size-5" />
        </a-button>
      </template>
      <template #newstatus="{ row }">
        <Tag
          :color="
            row.NewStatus === null ? 'blue' : row.NewStatus ? 'green' : 'red'
          "
        >
          {{
            row.NewStatus === null
              ? '拆分'
              : row.NewStatus
                ? '已收货'
                : '待收货'
          }}
        </Tag>
      </template>
      <template #splitstatus="{ row }">
        <Tag
          :color="
            row.SplitStatus === null
              ? 'blue'
              : row.SplitStatus
                ? 'green'
                : 'red'
          "
        >
          {{
            row.SplitStatus === null
              ? '拆分'
              : row.SplitStatus
                ? '已收货'
                : '待收货'
          }}
        </Tag>
      </template>
    </Grid>
  </SplitModal>
</template>
