<script lang="ts" setup>
import { onMounted, ref } from 'vue';

import { Page } from '@vben/common-ui';

import { TabPane, Tabs } from 'ant-design-vue';

import TabMovementCommand from './tabMovementCommand.vue';
import TabStockPL from './tabStockPL.vue';

const activeKey = ref('StockPL');
async function fetch() {}
onMounted(() => {
  fetch();
});
</script>

<template>
  <Page
    auto-content-height
    description="首先由WMS盘点差异，以701/702/707/708移动类型的StockMovement记录作为指令进行核销调整"
    title="库存差异核销"
  >
    <div class="flex h-full flex-col">
      <div class="inorder-tab min-h-0 flex-1">
        <Tabs v-model:active-key="activeKey" class="h-full" tab-position="left">
          <TabPane key="StockPL" tab="差异记录">
            <div v-if="activeKey === 'StockPL'" class="h-full">
              <TabStockPL />
            </div>
          </TabPane>
          <TabPane key="Command" tab="调整指令">
            <div v-if="activeKey === 'Command'" class="h-full">
              <TabMovementCommand />
            </div>
          </TabPane>
          <!-- <TabPane key="Verify" tab="核销单据">
            <div v-if="activeKey === 'Verify'" class="h-full">
              <TabStockPLVerify />
            </div>
          </TabPane> -->
        </Tabs>
      </div>
    </div>
  </Page>
</template>

<style scoped lang="less">
::v-deep(.inorder-tab) {
  background-color: #fff;

  .ant-tabs-tabpane .ant-tabs-tabpane-active {
    padding-left: 0 !important;
  }

  .ant-tabs-left > .ant-tabs-nav .ant-tabs-tab {
    padding: 8px 12px;
  }

  .ant-tabs-tab-active {
    background-color: #e7f5ff;
  }

  .ant-tabs-content-holder > .ant-tabs-content > .ant-tabs-tabpane {
    padding-left: 0;
  }
  .ant-tabs-content-left {
    height: 100%;
  }
}
</style>
