import { requestClient, sleep } from '#/api/request';

/**
 * 出库明细 相关API
 */
export async function getRecord4BWOutDetail(obj: object) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/outbound/record/getDetail', {
    params: obj,
  });
}

export async function getRecord4BWOutProcessing(obj: object) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/outbound/record/getProcessing', {
    params: obj,
  });
}

export async function getRecord4BWOutTally(obj: object) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/outbound/record/getTally', {
    params: obj,
  });
}

export async function getRecord4BWOutPicking(obj: object) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/outbound/record/getPicking', {
    params: obj,
  });
}

export async function getRecord4BWOutDispatch(obj: object) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/outbound/record/getDispatch', {
    params: obj,
  });
}
