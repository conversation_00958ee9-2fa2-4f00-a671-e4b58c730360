<script lang="ts" setup>
import type { VxeGridListeners, VxeGridProps } from '#/adapter';

import { onMounted, reactive, ref } from 'vue';

import { Page, useVbenDrawer } from '@vben/common-ui';
import { AntClear, AntDelete } from '@vben/icons';

import { Card, message, Modal, Tag, Tree } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import { getOptions } from '#/api/common';
import { dictionaryDelete, getDictionaryList } from '#/api/maintenance/data';

import dictionaryDrawer from './dictionaryDrawer.vue';

const treeData = ref([]) as any;
const treeSelectedKeys = ref([]) as any;
const typOptions = ref([]) as any;
const searchField: any = reactive({
  DictionaryType: undefined,
});
const [DictionaryDrawer, dictionaryDrawerApi] = useVbenDrawer({
  // 连接抽离的组件
  connectedComponent: dictionaryDrawer,
});
function openDictionaryDrawer(id: string, typ: any) {
  const typItem = typOptions.value.find((item: any) => item.value === typ);
  if (typItem) {
    dictionaryDrawerApi.setData({ id, typItem });
    dictionaryDrawerApi.open();
  }
}
const gridOptions = reactive<VxeGridProps>({
  id: 'DataDictionary',
  autoResize: true,
  columns: [
    {
      title: '#',
      type: 'seq',
      width: 60,
    },
    {
      field: 'DictionaryName',
      title: '选项名称',
      minWidth: 100,
    },
    {
      field: 'DictionaryValue',
      title: '选项值',
      minWidth: 100,
    },
    {
      field: 'SortBy',
      title: '排序',
      width: 100,
    },
    {
      field: 'IsActived',
      title: '启用状态',
      width: 120,
      slots: { default: 'isActived' },
    },
    {
      field: 'IsDefault',
      title: '是否默认',
      width: 120,
      slots: { default: 'isDefault' },
    },
    {
      field: 'DictionaryValueExt',
      title: '扩展属性',
      minWidth: 100,
    },
    {
      field: 'CreateBy',
      title: '创建人',
      width: 120,
    },
    {
      field: 'CreateDate',
      formatter: 'formatDateTime',
      sortable: true,
      title: '创建日期',
      width: 136,
    },
    {
      field: 'UpdateDate',
      formatter: 'formatDateTime',
      sortable: true,
      title: '更新日期',
      width: 136,
    },
    {
      align: 'center',
      slots: { default: 'action' },
      title: '删除',
      width: 60,
    },
  ],
  filterConfig: {
    remote: true,
  },
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: ({ page }) => {
        const queryParams: any = Object.assign(
          { sort: 'SortBy, DictionaryName' },
          page,
          searchField,
        );
        return getDictionaryList(queryParams);
      },
    },
    seq: true,
  },
  scrollY: { enabled: true, gt: 0 },
  size: 'mini',
  sortConfig: {
    remote: true,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
});
const gridEvents: VxeGridListeners = {
  cellDblclick({ row }) {
    openDictionaryDrawer(row.Guid, searchField.DictionaryType);
  },
};
const [Grid, gridApi] = useVbenVxeGrid({ gridEvents, gridOptions });

function clearFilterAndSort() {
  treeSelectedKeys.value = [];
  searchField.DictionaryType = undefined;
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
  gridApi.grid.commitProxy('query');
}
function handleSuccess() {
  gridApi.grid.commitProxy('query');
}
function handleTreeSelect(selectedKeys: any) {
  treeSelectedKeys.value = selectedKeys;

  searchField.DictionaryType = selectedKeys[0];
  handleSuccess();
}
function handleSelectChange(value: string) {
  searchField.DictionaryType = value;
  treeSelectedKeys.value = [value];
  handleSuccess();
}
function handleCreate() {
  if (searchField.DictionaryType) {
    openDictionaryDrawer('new', searchField.DictionaryType);
  } else {
    message.info('请先选择选项类型');
  }
}
function handleDelete(id: string) {
  dictionaryDelete(id)
    .then(() => {
      message.success('操作成功');
      handleSuccess();
    })
    .catch(() => {})
    .finally(() => {});
}
function showDeleteModal(id: string, name: string) {
  Modal.confirm({
    content: `请确认是否删除？`,
    /* icon: createVNode(ExclamationCircleOutlined), */
    onCancel() {},
    onOk() {
      handleDelete(id);
    },
    title: `选项名称: ${name}`,
    okButtonProps: { danger: true },
  });
}
async function fetch() {
  typOptions.value = await getOptions('DictionaryType');
  treeData.value = typOptions.value.map((item: any) => {
    item.title = item.label;
    item.key = item.value;
    return item;
  });
}
onMounted(() => {
  searchField.DictionaryType = undefined;
  fetch();
});
</script>

<template>
  <Page auto-content-height>
    <div class="grid h-full w-full grid-cols-1 md:grid-cols-4 xl:grid-cols-5">
      <div class="hidden md:col-span-1 md:block xl:col-span-1">
        <Card
          :body-style="{ padding: '12px' }"
          class="h-[calc(100%-10px)]"
          title="选项类型"
        >
          <Tree
            :selected-keys="treeSelectedKeys"
            :tree-data="treeData"
            block-node
            @select="handleTreeSelect"
          />
        </Card>
      </div>
      <div class="col-span-1 md:col-span-3 xl:col-span-4">
        <Grid class="h-[calc(100%-10px)]">
          <template #toolbar_left>
            <a-button class="mr-1" @click="clearFilterAndSort()">
              <AntClear class="size-5" />
            </a-button>
            <div class="pl-1">
              <a-select
                v-model:value="searchField.DictionaryType"
                :options="typOptions"
                allow-clear
                class="w-30 mr-2 sm:w-60"
                placeholder="选项类型"
                @change="handleSelectChange"
              />
            </div>
          </template>
          <template #toolbar-tools>
            <a-button class="mr-3" type="primary" @click="handleCreate()">
              新增选项
            </a-button>
          </template>
          <template #action="{ row }">
            <a-button
              danger
              size="small"
              type="link"
              @click="showDeleteModal(row.Guid, row.DictionaryName)"
            >
              <AntDelete class="size-5" />
            </a-button>
          </template>
          <template #isActived="{ row }">
            <Tag :color="row.IsActived ? 'green' : 'red'">
              {{ row.IsActived ? '启用' : '停用' }}
            </Tag>
          </template>
          <template #isDefault="{ row }">
            <Tag :color="row.IsDefault ? 'green' : 'blue'">
              {{ row.IsDefault ? '是' : '否' }}
            </Tag>
          </template>
        </Grid>
      </div>
    </div>
    <DictionaryDrawer @success="handleSuccess()" />
  </Page>
</template>
