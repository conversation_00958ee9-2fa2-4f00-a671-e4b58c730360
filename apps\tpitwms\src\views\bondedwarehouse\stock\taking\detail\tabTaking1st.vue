<script setup lang="ts">
import type { VxeGridListeners, VxeGridProps } from '#/adapter';

import { defineProps, reactive, ref } from 'vue';

import {
  useVbenDrawer,
  useVbenModal,
  VbenButton,
  VbenLoading,
} from '@vben/common-ui';
import { AntClear, AntDelete, AntSandBox } from '@vben/icons';

import { message, Modal, Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import {
  getStockTakingItem1st,
  stockTakingItem1stCancel,
  stockTakingItem1stDelete,
} from '#/api/bondedwarehouse/stock';
import batchDrawer from '#/views/sapneo/master/batch/batchDrawer.vue';
import skuDrawer from '#/views/sapneo/master/product/skuDrawer.vue';

import itemImportModal4Missing from './itemImportModal4Missing.vue';
import itemImportModal from './itemImportModal.vue';
import taking1stModal from './taking1stModal.vue';

/* import csarDrawer from './CSARDrawer.vue'; */

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
  doc: {
    type: String,
    default: '',
  },
  warehouse: {
    type: String,
    default: '',
  },
  owner: {
    type: String,
    default: '',
  },
  isClosed: {
    type: Boolean,
    default: false,
  },
  isBright: {
    type: Boolean,
    default: false,
  },
});
const loadingRef = ref(false);
const [ItemImportModal, ItemImportModalApi] = useVbenModal({
  connectedComponent: itemImportModal,
});
const [ItemImportModal4Missing, ItemImportModal4MissingApi] = useVbenModal({
  connectedComponent: itemImportModal4Missing,
});
const [Taking1stModal, Taking1stModalApi] = useVbenModal({
  connectedComponent: taking1stModal,
});
const [SkuDrawer, skuDrawerApi] = useVbenDrawer({
  connectedComponent: skuDrawer,
});
const [BatchDrawer, batchDrawerApi] = useVbenDrawer({
  connectedComponent: batchDrawer,
});
function openSkuDrawer(id: string) {
  skuDrawerApi.setData({ id });
  skuDrawerApi.open();
}
function openBatchDrawer(sku: string, batch: string) {
  batchDrawerApi.setData({ sku, batch });
  batchDrawerApi.open();
}
function openItemImportModal() {
  ItemImportModalApi.setData({
    id: props.id,
    warehouse: props.warehouse,
    owner: props.owner,
  });
  ItemImportModalApi.open();
}
function openItemImportModal4Missing() {
  ItemImportModal4MissingApi.setData({
    id: props.id,
    warehouse: props.warehouse,
    owner: props.owner,
  });
  ItemImportModal4MissingApi.open();
}
function openTaking1stModal(row: any) {
  Taking1stModalApi.setData({ row, isBright: props.isBright });
  Taking1stModalApi.open();
}
/* const [CSARDrawer, CSARDrawerApi] = useVbenDrawer({
  connectedComponent: csarDrawer,
});
function openCSARDrawer(id: string) {
  CSARDrawerApi.setData({ id });
  CSARDrawerApi.open();
} */

const gridOptions = reactive<VxeGridProps>({
  id: 'BWStockTaking1st',
  checkboxConfig: { highlight: true, range: true },
  columns: [
    { type: 'checkbox', width: 48, fixed: 'left' },
    {
      fixed: 'left',
      title: '#',
      type: 'seq',
      width: 60,
    },
    {
      field: 'TakingStatus',
      title: '初盘状态',
      width: 88,
      align: 'center',
      formatter({ cellValue }) {
        return cellValue ? '已盘点' : '待盘点';
      },
      slots: { default: 'status' },
    },
    { field: 'AreaName', title: '库区', width: 80 },
    { field: 'BIN', title: '货位', width: 96 },
    { field: 'StockStatus', title: '库存状态', width: 80 },
    { field: 'HBL', title: 'HBL', width: 136 },
    { field: 'PalletNo', title: '托盘号', width: 96 },
    {
      field: 'MatCode',
      title: '物料编号',
      width: 80,
      slots: { default: 'material' },
    },
    {
      field: 'BatchNo',
      title: '批号',
      width: 80,
      slots: { default: 'batch' },
    },
    {
      field: 'SAPBatch',
      title: 'SAP批次',
      width: 80,
    },
    {
      field: 'StorageLocation',
      title: 'SAP位置',
      width: 96,
    },
    { field: 'StockQty', title: '账面库存', width: 100 },
    { field: 'TakingQty', title: '盘点库存', width: 88 },
    /* { field: 'TakingDiffQty', title: '盘点差异', width: 80 }, */
    { field: 'CName', title: '中文品名', minWidth: 200 },
    { field: 'TakingBy1st', title: '操作人', minWidth: 72 },
    {
      field: 'TakingDate1st',
      title: '操作日期',
      width: 88,
      formatter: 'formatDate',
    },
    {
      title: '操作',
      fixed: 'right',
      width: 96,
      slots: { default: 'taking' },
      align: 'center',
    },
    /* {
      field: 'GoodsMovementReasonCode',
      title: '调整原因',
      minWidth: 150,
    }, */
    /* {
      field: 'CreateBy',
      title: '添加人',
      minWidth: 100,
    },
    {
      field: 'CreateDate',
      title: '添加日期',
      minWidth: 150,
      sortable: true,
    }, */
  ],
  filterConfig: {
    remote: false,
  },
  footerRowStyle: { 'font-weight': 'bold ' },
  footerMethod({ columns, data }) {
    return [
      columns.map((column, columnIndex) => {
        if (columnIndex === 0) {
          return `合计`;
        }
        if (columnIndex === 1) {
          return `${data.length} 项`;
        }
        if (['StockQty', 'TakingQty'].includes(column.field)) {
          return sumNum(data, column.field);
        }
        return null;
      }),
    ];
  },
  height: 'auto',
  keepSource: true,
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    ajax: {
      query: async () => {
        return await getStockTakingItem1st(props.id);
      },
    },
    seq: true,
  },
  showFooter: true,
  size: 'mini',
  sortConfig: {
    remote: false,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
  scrollX: { enabled: true, gt: 0 },
  scrollY: { enabled: true, mode: 'wheel', gt: 30, oSize: 0 },
  menuConfig: {
    trigger: 'cell',
    body: {
      options: [
        [
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuCopy',
            disabled: false,
            name: '复制单元格',
            prefixConfig: {
              icon: 'vxe-icon-copy',
            },
            visible: true,
          },
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuRefresh',
            disabled: false,
            name: '刷新',
            prefixConfig: { icon: 'vxe-icon-refresh' },
            visible: true,
          },
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuExport',
            disabled: false,
            name: '导出明细',
            prefixConfig: { icon: 'vxe-icon-download' },
            visible: true,
          },
        ],
      ],
    },
    className: 'font-medium shadow rounded-md',
  },
  rowStyle({ row }) {
    if (row.TakingStatus && row.IsDiff) {
      return {
        backgroundColor: '#ffe3e3',
        color: '#222',
      };
    }
  },
});
const gridEvents: VxeGridListeners = {
  /* cellDblclick({ row }) {
    openCSARDrawer(row.Guid);
  }, */
  menuClick({ menu }) {
    switch (menu.code) {
      case 'menuExport': {
        handleExport();
        break;
      }
      default: {
        break;
      }
    }
  },
};
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions, gridEvents });
function sumNum(list: any[], field: string) {
  let count = 0;
  list.forEach((item) => {
    count += Number(item[field]);
  });
  return count;
}
function handleExport() {
  gridApi.grid.exportData({
    filename: `盘点作业_${props.doc}`,
    sheetName: 'Sheet1',
    type: 'xlsx',
    isFooter: false,
    columnFilterMethod: ({ column }) => {
      return column.field !== undefined;
    },
  });
}
function handleSuccess() {
  gridApi.grid.commitProxy('query');
}
function handleRemoveCheck() {
  if (props.isClosed) {
    message.error(`作业单据已关闭，无法移除明细！`);
    return;
  }
  const checkedRecords = gridApi.grid
    .getCheckboxRecords()
    .map((item: any) => item.Guid);
  if (checkedRecords.length === 0) {
    message.info(`请勾选需要移除的作业明细！`);
    return;
  }
  Modal.confirm({
    content: `请确认是否移除勾选的作业明细？`,
    /* icon: createVNode(ExclamationCircleOutlined), */
    onCancel() {},
    onOk() {
      handleDelete(checkedRecords);
    },
    title: `移除作业明细`,
    okButtonProps: { danger: true },
  });
}
function handleDelete(ids: Array<string>) {
  stockTakingItem1stDelete(props.id, ids)
    .then(() => {
      handleSuccess();
    })
    .catch(() => {})
    .finally(() => {});
}
function showDeleteModal() {
  Modal.confirm({
    content: `请确认是否撤销？若已存在复盘记录将被同步撤销！`,
    /* icon: createVNode(ExclamationCircleOutlined), */
    onCancel() {},
    onOk() {
      handleCancel();
    },
    title: `操作确认`,
    okButtonProps: { danger: true },
  });
}
function handleCancel() {
  const checkedRecords = gridApi.grid
    .getCheckboxRecords()
    .map((item: any) => item.Guid);
  if (checkedRecords.length === 0) {
    message.info(`请勾选已盘点的作业明细`);
    return;
  }
  loadingRef.value = true;
  stockTakingItem1stCancel(props.id, checkedRecords)
    .then(() => {
      message.success('操作成功！');
      handleSuccess();
    })
    .catch(() => {})
    .finally(() => {
      loadingRef.value = false;
    });
}

function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
}
</script>

<template>
  <div class="flex h-full flex-col">
    <Grid>
      <template #toolbar_left>
        <div class="hidden pl-1 text-base font-medium md:block">
          <span class="mr-2">作业明细</span>
        </div>
        <a-button class="mr-1" size="small" @click="clearFilterAndSort()">
          <AntClear class="size-5" />
        </a-button>
        <a-button
          :disabled="props.isClosed"
          class="ml-2"
          size="small"
          type="primary"
          @click="openItemImportModal()"
        >
          添加作业明细
        </a-button>
        <a-button
          :disabled="props.isClosed"
          class="ml-2"
          size="small"
          @click="openItemImportModal4Missing()"
        >
          批量添加贴标少
        </a-button>
      </template>
      <template #toolbar-tools>
        <VbenButton
          :disabled="props.isClosed"
          class="h-7 w-7 rounded-full px-1"
          variant="destructive"
          @click="handleRemoveCheck()"
        >
          <AntDelete class="size-5" />
        </VbenButton>
        <a-button
          class="ml-2"
          size="small"
          @click="showDeleteModal()"
          :disabled="props.isClosed"
        >
          撤销盘点
        </a-button>
      </template>
      <template #status="{ row }">
        <Tag :color="row.TakingStatus ? 'green' : 'blue'">
          {{ row.TakingStatus ? '已盘点' : '待盘点' }}
        </Tag>
      </template>
      <template #material="{ row }">
        <a
          v-if="row.MatCode"
          class="text-blue-500"
          @click="openSkuDrawer(row.MatCode)"
        >
          {{ row.MatCode }}
        </a>
      </template>
      <template #batch="{ row }">
        <a
          v-if="row.BatchNo"
          class="text-blue-500"
          @click="openBatchDrawer(row.MatCode, row.BatchNo)"
        >
          {{ row.BatchNo }}
        </a>
      </template>
      <template #taking="{ row }">
        <!-- <a-button
          :disabled="row.TakingStatus"
          type="primary"
          size="small"
          @click="1"
        >
          <Icon icon="ant-design:carry-out-outlined" size="16" />盘点
        </a-button> -->
        <a-button
          :disabled="row.TakingStatus"
          size="small"
          type="primary"
          @click="openTaking1stModal(row)"
        >
          <AntSandBox class="size-4" />
          盘点
        </a-button>
      </template>
    </Grid>
    <ItemImportModal @success="handleSuccess()" />
    <ItemImportModal4Missing @success="handleSuccess()" />
    <Taking1stModal @success="handleSuccess()" />
    <SkuDrawer />
    <BatchDrawer />
    <VbenLoading :spinning="loadingRef" />
  </div>
</template>
