<script lang="ts" setup>
import type { VxeGridProps } from '#/adapter';

import { reactive, ref } from 'vue';
import { useRouter } from 'vue-router';

import { Fallback, useVbenDrawer } from '@vben/common-ui';
import { AntFileSearch } from '@vben/icons';

import { Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import { getEventAPIRecord } from '#/api/sapneo';

const data = ref();
const isNotFound = ref(false);
const router = useRouter();
const [Drawer, drawerApi] = useVbenDrawer({
  class: 'w-[560px]',
  showConfirmButton: false,
  cancelText: '关闭',
  destroyOnClose: true,
  zIndex: 900,
  onCancel() {
    drawerApi.close();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      isNotFound.value = false;
      data.value = drawerApi.getData<Record<string, any>>();
      handleGetData(data.value.id);
    }
  },
});
const gridOptions = reactive<VxeGridProps>({
  id: 'EventAPIRecord',
  columns: [
    {
      title: '#',
      type: 'seq',
      width: 60,
    },
    {
      field: 'Params',
      title: 'Params',
      minWidth: 120,
    },
    {
      field: 'ExecuteDate',
      title: 'ExecuteDate',
      width: 136,
      formatter: 'formatDateTime',
    },
    {
      field: 'StatusCode',
      title: 'StatusCode',
      slots: { default: 'statuscode' },
      width: 88,
    },
    {
      title: 'Record',
      slots: { default: 'action' },
      width: 72,
    },
  ],
  customConfig: {
    storage: false,
  },
  expandConfig: {
    accordion: true,
  },
  menuConfig: {
    body: {
      options: [
        [
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuCopy',
            disabled: false,
            name: '复制单元格',
            prefixConfig: {
              icon: 'vxe-icon-copy',
            },
            visible: true,
          },
        ],
      ],
    },
    className: 'font-medium shadow rounded-md',
  },
  rowConfig: {
    useKey: true,
  },
  size: 'mini',
  stripe: false,
  showOverflow: true,
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    enabled: false,
  },
  toolbarConfig: {
    enabled: false,
  },
});
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });

function handleGetData(id: string) {
  drawerApi.setState({ loading: true });
  getEventAPIRecord(id)
    .then((res) => {
      gridApi.grid.loadData(res);
      isNotFound.value = false;
    })
    .catch(() => {
      isNotFound.value = true;
    })
    .finally(() => {
      drawerApi.setState({ loading: false });
    });
}
function handleGo(id: string) {
  drawerApi.close();
  router.push({
    name: 'Log4APIN2WDetail',
    params: { id },
  });
}
</script>
<template>
  <Drawer>
    <Fallback v-if="isNotFound" :show-back="false" status="404" />
    <div v-else>
      <Grid>
        <template #statuscode="{ row }">
          <Tag
            :color="
              row.StatusCode === 0
                ? 'grey'
                : row.StatusCode === 200
                  ? 'green'
                  : 'red'
            "
          >
            {{ row.StatusCode === 0 ? 'Pending' : row.StatusCode }}
          </Tag>
        </template>
        <template #action="{ row }">
          <a-button
            size="small"
            style="margin-right: 8px"
            type="link"
            @click="handleGo(row.Guid)"
          >
            <AntFileSearch class="size-6" />
          </a-button>
        </template>
      </Grid>
    </div>
  </Drawer>
</template>
