<script setup lang="ts">
import type { VxeGridProps } from '#/adapter';

import { onMounted, reactive } from 'vue';

import { Page } from '@vben/common-ui';
import { AntClear } from '@vben/icons';

import { Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import { getRecord4BWOutDetail } from '#/api/bondedwarehouse/outbound';
import { getOptions } from '#/api/common';

const searchField: any = reactive({
  BLNo: undefined,
  /* MatCode: undefined,
  BatchNo: undefined, */
});
const footQty: any = reactive({
  GoodsNum: 0,
  DeliveryQty: 0,
  UnDeliveryQty: 0,
});
const gridOptions = reactive<VxeGridProps>({
  id: 'Record4BWOutboundDetail',
  columns: [
    {
      fixed: 'left',
      title: '#',
      type: 'seq',
      width: 60,
    },
    {
      field: 'BLNo',
      title: '出库提单BL',
      width: 150,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    { field: 'OwnerShortName', title: '货主', minWidth: 100, filters: [] },
    { field: 'WarehouseName', title: '仓库', minWidth: 100, filters: [] },
    {
      field: 'MatCode',
      title: '物料编号',
      width: 88,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      /* slots: { default: 'material' }, */
    },
    {
      field: 'BatchNo',
      title: '批号',
      width: 80,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      /* slots: { default: 'batchno' }, */
    },
    {
      field: 'OrderPickUpStatus',
      title: '备货状态',
      width: 88,
      formatter({ cellValue }) {
        return cellValue ? '已备货' : '待备货';
      },
      filters: [
        { label: '已备货', value: true },
        { label: '待备货', value: false },
      ],
      filterMultiple: false,
      slots: { default: 'opStatus' },
    },
    {
      field: 'GoodsNum',
      title: '产品数量',
      width: 100,
      filters: [{ data: { type: '0', num1: undefined, num2: undefined } }],
      filterRender: { name: 'FilterNumberRange' },
    },
    {
      field: 'DeliveryQty',
      title: '已出库',
      width: 88,
      filters: [{ data: { type: '0', num1: undefined, num2: undefined } }],
      filterRender: { name: 'FilterNumberRange' },
    },
    {
      field: 'UnDeliveryQty',
      title: '未出库',
      width: 88,
      filters: [{ data: { type: '0', num1: undefined, num2: undefined } }],
      filterRender: { name: 'FilterNumberRange' },
    },
    {
      field: 'IsProcessingRequired',
      title: '是否需贴标',
      width: 100,
      formatter({ cellValue }) {
        return cellValue ? '是' : '否';
      },
      filters: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      filterMultiple: false,
      slots: {
        default: 'processingRequired',
      },
    },
    {
      field: 'PalletNo',
      title: '入库托盘',
      width: 88,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'SSCC',
      title: 'SSCC',
      width: 144,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'AreaName',
      title: '来源库区',
      width: 88,
      /* visible: false, */
    },
    {
      field: 'BIN',
      title: '来源货位',
      width: 96,
      visible: false,
    },
    {
      field: 'OrderPickUpAreaName',
      title: '备货库区',
      width: 88,
      visible: false,
    },
    {
      field: 'oMatType',
      title: '物料类型',
      width: 88,
      filters: [],
    },
    {
      field: 'oBrandName',
      title: '品牌',
      width: 80,
      filters: [],
    },
    {
      field: 'oCName',
      title: '出库中文品名',
      minWidth: 200,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
  ],
  filterConfig: {
    remote: true,
  },
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: ({ filters, page, sorts }) => {
        const queryParams: any = Object.assign({}, page);
        // 处理排序条件
        if (sorts.length === 0) {
          queryParams.sort = 'OrderPickUpStatus,_Identify desc';
        } else {
          const sortItem = sorts.map((item) => {
            const newItem = `${item.field} ${item.order}`;
            return newItem;
          });
          queryParams.sort = sortItem.join(',');
        }
        // 处理筛选
        Object.getOwnPropertyNames(searchField).forEach((key) => {
          searchField[key] = undefined;
        });
        filters.forEach(({ field, values }) => {
          queryParams[field] = values.join(',');
          if (Object.prototype.hasOwnProperty.call(searchField, field)) {
            const jsonObject = JSON.parse(values.toString());
            searchField[field] = jsonObject.text;
          }
        });
        return getRecord4BWOutDetail(queryParams)
          .then((res) => {
            Object.assign(footQty, res.qty);
            return res;
          })
          .catch(() => {})
          .finally(() => {});
      },
    },
    seq: true,
  },
  scrollX: { enabled: true, gt: 0 },
  scrollY: { enabled: true, gt: 0 },
  showFooter: true,
  size: 'mini',
  sortConfig: {
    remote: true,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
  footerRowStyle: { 'font-weight': 'bold ', backgroundColor: '#ebfbee' },
  footerMethod({ columns }) {
    return [
      columns.map((column, columnIndex) => {
        if (columnIndex === 0) {
          return `合计`;
        }
        if (
          ['DeliveryQty', 'GoodsNum', 'UnDeliveryQty'].includes(column.field)
        ) {
          return footQty[column.field];
        }

        return null;
      }),
    ];
  },
});
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });
function searchEvent(field: string) {
  const column = gridApi.grid.getColumnByField(field);
  if (column) {
    // 修改第一个选项为勾选状态
    const option = column.filters[0];
    if (!option) {
      return;
    }

    if (searchField[field]) {
      option.data = { type: '1', text: searchField[field] };
      option.value = JSON.stringify(option.data);
      option.checked = true;
    } else {
      option.data = { type: '0', text: undefined };
      option.checked = false;
    }

    // 修改条件之后，需要手动调用 updateData 处理表格数据
    gridApi.grid.updateData();
    gridApi.grid.commitProxy('query');
  }
}
function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
  gridApi.grid.commitProxy('query');
}
async function fetch() {
  const options = await getOptions('Record4OutDetail');
  const fieldList = [
    'oBrandName',
    'oMatType',
    'OwnerShortName',
    'WarehouseName',
  ];
  fieldList.forEach((field) => {
    gridApi.grid.setFilter(
      field,
      options.filter((item: any) => {
        return item.type === field;
      }),
    );
  });
}
onMounted(() => {
  setTimeout(() => {
    fetch();
  }, 300);
});
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar_left>
        <a-button class="mr-1" @click="clearFilterAndSort()">
          <AntClear class="size-5" />
        </a-button>
        <div class="hidden pl-1 md:block">
          <a-input-search
            v-model:value="searchField.BLNo"
            allow-clear
            class="mr-2 w-60"
            placeholder="出库提单BL"
            @search="searchEvent('BLNo')"
          />
        </div>
      </template>
      <template #opStatus="{ row }">
        <Tag :color="row.OrderPickUpStatus ? 'green' : 'red'">
          {{ row.OrderPickUpStatus ? '已备货' : '待备货' }}
        </Tag>
      </template>
      <template #processingRequired="{ row }">
        <Tag :color="row.IsProcessingRequired ? 'green' : 'red'">
          {{ row.IsProcessingRequired ? '是' : '否' }}
        </Tag>
      </template>
      <template #toolbar-tools> </template>
    </Grid>
  </Page>
</template>
