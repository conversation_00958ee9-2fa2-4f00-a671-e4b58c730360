<script lang="ts" setup>
import type { VxeGridProps } from '#/adapter';

import { reactive, ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import {
  bwInReturnOBDRemove,
  getInReturnOBDByDoc,
} from '#/api/bondedwarehouse/inbound';

const emit = defineEmits(['success']);
const data = ref();

const [Drawer, drawerApi] = useVbenDrawer({
  confirmText: '确认移除',
  zIndex: 900,
  onCancel() {
    drawerApi.close();
  },
  onConfirm: async () => {
    await handleRemove();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      data.value = drawerApi.getData<Record<string, any>>();
    }
  },
});
const gridOptions = reactive<VxeGridProps>({
  id: 'ReturnDeliveryRemove',
  checkboxConfig: { highlight: true, range: true },
  columns: [
    { type: 'checkbox', width: 48, fixed: 'left' },
    { field: 'DeliveryDocument', title: 'ReturnOBD', minWidth: 88 },
    { field: 'SO', title: '销售订单', minWidth: 88 },
    { field: 'TotalQty', title: '总件数', minWidth: 80 },
    { field: 'CreationDate', title: 'S创建日期', minWidth: 136 },
  ],
  filterConfig: {
    remote: false,
  },
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    ajax: {
      query: () => {
        return getInReturnOBDByDoc(data.value.id);
      },
    },
    seq: true,
  },
  size: 'mini',
  sortConfig: {
    remote: false,
  },
  toolbarConfig: {
    slots: {
      /* buttons: 'toolbar_left', */
    },
  },
});
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });
function handleRemove() {
  const checkedRecords = gridApi.grid
    .getCheckboxRecords()
    .map((item: any) => item.Guid);
  if (checkedRecords.length === 0) {
    message.info(`请勾选需要移除的ReturnOBD`);
    return;
  }
  drawerApi.setState({ confirmLoading: true });
  drawerApi.setState({ loading: true });
  bwInReturnOBDRemove(data.value.id, checkedRecords)
    .then(() => {
      handleClose();
    })
    .catch(() => {})
    .finally(() => {
      drawerApi.setState({ loading: false });
      drawerApi.setState({ confirmLoading: false });
    });
}
function handleClose() {
  drawerApi.close();
  emit('success');
}
</script>
<template>
  <Drawer title="Inbound移除">
    <Grid />
  </Drawer>
</template>
