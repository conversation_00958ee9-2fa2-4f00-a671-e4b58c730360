<script setup lang="ts">
import type { VxeGridListeners, VxeGridProps } from '#/adapter';

import { reactive } from 'vue';

import { Page, useVbenDrawer } from '@vben/common-ui';
import { AntClear } from '@vben/icons';

import { useVbenVxeGrid } from '#/adapter';
import { getStockMovementItem, getStockMovementList } from '#/api/sapneo';

import batchDrawer from '../../master/batch/batchDrawer.vue';
import skuDrawer from '../../master/product/skuDrawer.vue';

const [SkuDrawer, skuDrawerApi] = useVbenDrawer({
  connectedComponent: skuDrawer,
});
const [BatchDrawer, batchDrawerApi] = useVbenDrawer({
  connectedComponent: batchDrawer,
});
function openSkuDrawer(id: string) {
  skuDrawerApi.setData({ id });
  skuDrawerApi.open();
}
function openBatchDrawer(sku: string, batch: string) {
  batchDrawerApi.setData({ sku, batch });
  batchDrawerApi.open();
}
const searchField: any = reactive({
  MaterialDocument: undefined,
});
const itemGridOptions = reactive<VxeGridProps>({
  columns: [
    /* { type: 'seq', width: 48 }, */
    {
      field: 'MaterialDocumentItem',
      title: '调整项号',
      minWidth: 88,
      treeNode: true,
    },
    { field: 'GoodsMovementType', title: '移动类型', minWidth: 150 },
    { field: 'Plant', title: '工厂', minWidth: 80 },
    { field: 'StorageLocation', title: 'SAP存储位置', minWidth: 100 },
    {
      field: 'Material',
      title: '产品编号',
      minWidth: 100,
      slots: { default: 'skucode1' },
    },
    {
      field: 'Batch',
      title: '批次',
      minWidth: 100,
      slots: { default: 'batch1' },
    },
    { field: 'InventoryStockType', title: '库存类型', minWidth: 128 },
    { field: 'QuantityInEntryUnit', title: '调整件数', minWidth: 88 },
    { field: 'EntryUnit', title: '单位', minWidth: 72 },
    { field: 'IssuingOrReceivingPlant', title: '收货工厂', minWidth: 80 },
    { field: 'IssuingOrReceivingStorageLoc', title: '收货位置', minWidth: 100 },
    {
      field: 'IssgOrRcvgMaterial',
      title: '收货产品',
      minWidth: 100,
      slots: { default: 'skucode2' },
    },
    {
      field: 'IssgOrRcvgBatch',
      title: '收货批次',
      minWidth: 100,
      slots: { default: 'batch2' },
    },
    {
      field: 'IssuingOrReceivingStockType',
      title: '收货库存类型',
      minWidth: 128,
    },
    /* {
      field: 'IssgOrRcvgSpclStockInd',
      title: '收货特殊库存标识',
      minWidth: 120,
    }, */
  ],
  customConfig: {
    storage: false,
  },
  menuConfig: {
    body: {
      options: [
        [
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuCopy',
            disabled: false,
            name: '复制单元格',
            prefixConfig: {
              icon: 'vxe-icon-copy',
            },
            visible: true,
          },
        ],
      ],
    },
    className: 'font-medium shadow rounded-md',
  },
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    enabled: false,
  },
  rowConfig: {
    useKey: true,
    keyField: 'Guid',
  },
  rowStyle({ row }) {
    if (row.Qty === 0) {
      return {
        backgroundColor: '#fff4e6',
        color: '#222',
      };
    }
  },
  size: 'mini',
  stripe: false,
  toolbarConfig: {
    enabled: false,
  },
  treeConfig: {
    expandAll: false,
    transform: true,
    rowField: 'MaterialDocumentLine',
    parentField: 'MaterialDocumentParentLine',
    showLine: true,
    /* expandAll: true, */
  },
});
const [ItemGrid, itemGridApi] = useVbenVxeGrid({
  gridOptions: itemGridOptions,
});
const gridOptions = reactive<VxeGridProps>({
  id: 'NEOStockmovement',
  columns: [
    {
      title: '#',
      type: 'seq',
      width: 60,
    },
    { type: 'expand', width: 48, slots: { content: 'expand_content' } },
    {
      field: 'MaterialDocumentYear',
      title: '单据年份',
      width: 100,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'MaterialDocument',
      title: 'SAP调整单据',
      minWidth: 120,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'MaterialDocumentHeaderText',
      title: '抬头文本',
      minWidth: 160,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'ReferenceDocument',
      title: '参考单据',
      minWidth: 108,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'MovementItemsCount',
      title: '有效项数',
      width: 88,
    },
    {
      field: 'MovementType',
      title: '包含移动类型',
      minWidth: 128,
      filters: [{ data: null }],
      filterRender: { name: 'FilterInput' },
    },
    {
      field: 'GoodsMovementCode',
      title: '货物移动代码',
      width: 120,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'InventoryTransactionType',
      title: '事务类型',
      width: 120,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'PostingDate',
      title: '记账日期',
      width: 120,
      formatter: 'formatDate',
      sortable: true,
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'CreationDate',
      title: 'S创建日期',
      minWidth: 132,
      formatter: 'formatDateTime',
      sortable: true,
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'CreateDate',
      title: 'W创建日期',
      minWidth: 136,
      formatter: 'formatDateTime',
      sortable: true,
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'UpdateDate',
      title: 'W更新日期',
      minWidth: 136,
      formatter: 'formatDateTime',
      sortable: true,
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
  ],
  expandConfig: {
    accordion: true,
  },
  filterConfig: {
    remote: true,
  },
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: ({ filters, page, sorts }) => {
        const queryParams: any = Object.assign({}, page);
        // 处理排序条件
        if (sorts.length === 0) {
          queryParams.sort = '_Identify desc';
        } else {
          const sortItem = sorts.map((item) => {
            const newItem = `${item.field} ${item.order}`;
            return newItem;
          });
          queryParams.sort = sortItem.join(',');
        }
        // 处理筛选
        Object.getOwnPropertyNames(searchField).forEach((key) => {
          searchField[key] = undefined;
        });
        filters.forEach(({ field, values }) => {
          queryParams[field] = values.join(',');
          if (Object.prototype.hasOwnProperty.call(searchField, field)) {
            const jsonObject = JSON.parse(values.toString());
            searchField[field] = jsonObject.text;
          }
        });
        return getStockMovementList(queryParams);
      },
    },
    seq: true,
  },
  /* scrollY: { enabled: true, gt: 0 }, */
  size: 'mini',
  sortConfig: {
    remote: true,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
});
const gridEvents: VxeGridListeners = {
  async toggleRowExpand({ expanded, row }) {
    await handleItemData([]);
    if (expanded) {
      handleItemLoading(true);
      const itemData = await getStockMovementItem(row.Guid);
      await handleHeadRow(row);
      await handleItemData(itemData);
      handleItemLoading(false);
      /* itemGridApi.grid?.setAllTreeExpand(true); */
    }
  },
};
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions, gridEvents });

function handleHeadRow(row: any) {
  gridApi.grid.setCurrentRow(row);
}
function handleItemData(data: any) {
  itemGridApi.setGridOptions({
    data,
  });
}
function handleItemLoading(isLoading: boolean) {
  itemGridApi.setLoading(isLoading);
}
function searchEvent(field: string) {
  const column = gridApi.grid.getColumnByField(field);
  if (column) {
    // 修改第一个选项为勾选状态
    const option = column.filters[0];
    if (!option) {
      return;
    }

    if (searchField[field]) {
      option.data = { type: '1', text: searchField[field] };
      option.value = JSON.stringify(option.data);
      option.checked = true;
    } else {
      option.data = { type: '0', text: undefined };
      option.checked = false;
    }

    // 修改条件之后，需要手动调用 updateData 处理表格数据
    gridApi.grid.updateData();
    gridApi.grid.commitProxy('query');
  }
}
function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
  gridApi.grid.commitProxy('query');
}
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar_left>
        <a-button class="mr-1" @click="clearFilterAndSort()">
          <AntClear class="size-5" />
        </a-button>
        <div class="hidden pl-1 md:block">
          <a-input-search
            v-model:value="searchField.MaterialDocument"
            allow-clear
            class="mr-2 w-60"
            placeholder="SAP调整单据"
            @search="searchEvent('MaterialDocument')"
          />
        </div>
      </template>
      <template #toolbar-tools> </template>
      <template #expand_content>
        <div>
          <ItemGrid>
            <template #skucode1="{ row }">
              <a-button
                v-if="row.Material"
                size="small"
                type="link"
                @click="openSkuDrawer(row.Material)"
              >
                {{ row.Material }}
              </a-button>
            </template>
            <template #skucode2="{ row }">
              <a-button
                v-if="row.IssgOrRcvgMaterial"
                size="small"
                type="link"
                @click="openSkuDrawer(row.IssgOrRcvgMaterial)"
              >
                {{ row.IssgOrRcvgMaterial }}
              </a-button>
            </template>
            <template #batch1="{ row }">
              <a-button
                v-if="row.Batch"
                size="small"
                type="link"
                @click="openBatchDrawer(row.Material, row.Batch)"
              >
                {{ row.Batch }}
              </a-button>
            </template>
            <template #batch2="{ row }">
              <a-button
                v-if="row.IssgOrRcvgBatch"
                size="small"
                type="link"
                @click="
                  openBatchDrawer(row.IssgOrRcvgMaterial, row.IssgOrRcvgBatch)
                "
              >
                {{ row.IssgOrRcvgBatch }}
              </a-button>
            </template>
          </ItemGrid>
        </div>
      </template>
    </Grid>
    <SkuDrawer />
    <BatchDrawer />
  </Page>
</template>
