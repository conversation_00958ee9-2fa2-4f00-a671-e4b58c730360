import { requestClient, sleep } from '#/api/request';

/**
 * 入库作业 销售退货相关API
 */
export async function getBWInReturnList(obj: object) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/inbound/return/getList', {
    params: obj,
  });
}

export async function bwInReturnCreate(params: object) {
  await sleep(100);
  return requestClient.post('/bondedwarehouse/inbound/return/create', params);
}

export async function bwInReturnDelete(id: string) {
  await sleep(100);
  return requestClient.post('/bondedwarehouse/inbound/return/del', { id });
}

export async function getInReturnHead(id: string) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/inbound/return/head/getData', {
    params: { id },
  });
}

export async function getInReturnItem(id: string) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/inbound/return/item/getList', {
    params: { id },
  });
}
export async function bwInReturnItemUpdate(params: object) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/inbound/return/item/update',
    params,
  );
}

export async function bwInReturnHeadUpdate(params: object) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/inbound/return/head/update',
    params,
  );
}

export async function bwInReturnInStatusCheck(id: string) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/inbound/return/instatus/check', {
    params: { id },
  });
}

export async function bwInReturnInStatusConfirm(params: object) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/inbound/return/instatus/confirm',
    params,
  );
}

export async function getUnUsedReturnOBDByDoc(obj: object) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/inbound/return/obd/getList', {
    params: obj,
  });
}

export async function getInReturnOBDByDoc(id: string) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/inbound/return/obd/getUsed', {
    params: { id },
  });
}

export async function bwInReturnOBDImport(id: string, rid: string) {
  await sleep(100);
  return requestClient.post('/bondedwarehouse/inbound/return/obd/import', {
    id,
    rid,
  });
}

export async function bwInReturnOBDRemove(id: string, ids: Array<string>) {
  await sleep(100);
  return requestClient.post('/bondedwarehouse/inbound/return/obd/remove', {
    id,
    ids,
  });
}

export async function bwInReturnItemGetData(id: string) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/inbound/return/item/getData', {
    params: { id },
  });
}

export async function bwInReturnItemReceiving(params: object) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/inbound/return/item/receiving',
    params,
  );
}

export async function bwInReturnReveivingCancel(
  id: string,
  ids: Array<string>,
) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/inbound/return/item/receiving/cancel',
    { id, ids },
  );
}

export async function getInReturnInspectionItem(id: string) {
  await sleep(100);
  return requestClient.get(
    '/bondedwarehouse/inbound/return/inspection/getList',
    {
      params: { id },
    },
  );
}

export async function bwInReturnInspectionGetData(id: string) {
  await sleep(100);
  return requestClient.get(
    '/bondedwarehouse/inbound/return/inspection/getData',
    {
      params: { id },
    },
  );
}
export async function bwInReturnItemInspection(params: object) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/inbound/return/item/inspection',
    params,
  );
}

export async function getInReturnInspectionSplit(id: string) {
  await sleep(100);
  return requestClient.get(
    '/bondedwarehouse/inbound/return/inspection/split/getList',
    {
      params: { id },
    },
  );
}

export async function bwInReturnInspectionSplitRepeal(id: string) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/inbound/return/inspection/split/repeal',
    { id },
  );
}
export async function bwInReturnInspectionCancel(
  id: string,
  ids: Array<string>,
) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/inbound/return/inspection/cancel',
    { id, ids },
  );
}

export async function bwInReturnClosing(id: string) {
  await sleep(100);
  return requestClient.post('/bondedwarehouse/inbound/return/closing', { id });
}
/* 


export async function getInOrderInboundItem(id: string) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/inbound/order/ibd/item/getList', {
    params: { id },
  });
}














export async function getInOrderCSARList(id: string) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/inbound/order/csar/getList', {
    params: { id },
  });
}

export async function getInOrderCSARData(id: string) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/inbound/order/csar/getData', {
    params: { id },
  });
}

export async function bwInOrderCSARUpdate(params: object) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/inbound/order/csar/update',
    params,
  );
}

export async function bwInOrderInboundGRCheck(params: object) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/inbound/order/ibd/gr/check',
    params,
  );
}

export async function bwInOrderInboundGR(params: object) {
  await sleep(100);
  return requestClient.post('/bondedwarehouse/inbound/order/ibd/gr', params);
}

export async function bwInOrderStockSetup(params: object) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/inbound/order/stockSetup',
    params,
  );
} */
