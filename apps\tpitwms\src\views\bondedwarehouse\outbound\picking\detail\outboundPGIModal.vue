<script lang="ts" setup>
import type { VxeGridListeners, VxeGridProps } from '#/adapter';

import { reactive, ref } from 'vue';
/* import { AntClear } from '@vben/icons'; */

import { useVbenModal } from '@vben/common-ui';

import { Descriptions, message, Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import {
  bwOutPickingHandlePGI,
  bwOutPickingPGIGetData,
} from '#/api/bondedwarehouse/outbound';

const emit = defineEmits(['success']);

const data = ref();
const headData = ref({}) as any;
const dispatchData = ref([]) as any;
const [Modal, modalApi] = useVbenModal({
  class: 'w-full h-full',
  fullscreenButton: true,
  closeOnClickModal: false,
  confirmText: 'PGI关单',
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await handlePGI();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      /* modalApi.setState({ showConfirmButton: false });
      modalApi.setState({ loading: true }); */
      headData.value = {};
      data.value = modalApi.getData<Record<string, any>>();
      fetch();
    }
  },
  title: 'PGI 关单确认',
});
const gridOptions = reactive<VxeGridProps>({
  id: 'OutboundPGI',
  columns: [
    {
      fixed: 'left',
      title: '#',
      type: 'seq',
      width: 60,
    },
    { type: 'expand', width: 48, slots: { content: 'expand_content' } },
    { field: 'ReferenceSDDocument', title: 'SAP销售订单', minWidth: 100 },
    { field: 'DeliveryDocument', title: 'Outbound', minWidth: 100 },
    { field: 'DeliveryDocumentItem', title: '项号', minWidth: 100 },
    { field: 'Plant', title: '工厂编码', minWidth: 80 },
    {
      field: 'StorageLocation',
      title: '库存地点',
      minWidth: 80,
    },
    { field: 'Material', title: '物料编码', minWidth: 100 },
    {
      field: 'MaterialIsBatchManaged',
      title: '批次管理',
      minWidth: 88,
      slots: { default: 'batchmanage' },
    },
    { field: 'DeliveryQuantity', title: '指令件数', minWidth: 100 },
    { field: 'PickingQty', title: '拣货数量', minWidth: 100 },
  ],
  expandConfig: {
    accordion: true,
  },
  filterConfig: {
    remote: true,
  },
  layouts: ['Top', 'Toolbar', 'Table'],
  height: 'auto',
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    autoLoad: false,
    ajax: {
      query: async () => {
        const res = await bwOutPickingPGIGetData(data.value.id);
        headData.value = res.head;
        dispatchData.value = res.dispatch;
        return res.item;
      },
    },
    seq: true,
  },
  /* scrollY: { enabled: true, gt: 0 }, */
  size: 'mini',
  sortConfig: {
    remote: true,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
});
const itemGridOptions = reactive<VxeGridProps>({
  columns: [
    { field: 'MatCode', title: '物料编码', minWidth: 100 },
    { field: 'BatchNo', title: '批号', minWidth: 100 },
    { field: 'SAPBatch', title: '标准批次', minWidth: 100 },
    { field: 'ActualExpiration', title: '限用日期', minWidth: 100 },
    { field: 'GoodsNum', title: '产品数量', minWidth: 100 },
    {
      field: 'DeliveryGoodsType',
      title: '产品分类',
      minWidth: 100,
    },
    { field: 'SSCC', title: '分类SSCC', minWidth: 150 },
    { field: 'NewPalletNo', title: '出库托盘', minWidth: 150 },
    { field: 'DispatchNo', title: '派车单', minWidth: 160 },
    { field: 'DeliveryDate', title: '发货时间', minWidth: 150 },
  ],
  customConfig: {
    storage: false,
  },
  menuConfig: {
    body: {
      options: [
        [
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuCopy',
            disabled: false,
            name: '复制单元格',
            prefixConfig: {
              icon: 'vxe-icon-copy',
            },
            visible: true,
          },
        ],
      ],
    },
    className: 'font-medium shadow rounded-md',
  },
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    enabled: false,
  },
  rowConfig: {
    useKey: true,
    keyField: 'Guid',
  },
  rowStyle({ row }) {
    if (row.Qty === 0) {
      return {
        backgroundColor: '#fff4e6',
        color: '#222',
      };
    }
  },
  size: 'mini',
  toolbarConfig: {
    enabled: false,
  },
  treeConfig: {
    transform: true,
    rowField: 'ItemNumber',
    parentField: 'ParentItem',
    showLine: true,
  },
});
const [ItemGrid, itemGridApi] = useVbenVxeGrid({
  gridOptions: itemGridOptions,
});
const gridEvents: VxeGridListeners = {
  async toggleRowExpand({ expanded, row }) {
    await handleItemData([]);
    if (expanded) {
      handleItemLoading(true);
      const itemData = dispatchData.value.filter(
        (item: any) =>
          item.Outbound === row.DeliveryDocument &&
          item.MatCode === row.Material,
      );
      await handleHeadRow(row);
      await handleItemData(itemData);
      handleItemLoading(false);
      itemGridApi.grid?.setAllTreeExpand(true);
    }
  },
};
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions, gridEvents });
function handleHeadRow(row: any) {
  gridApi.grid.setCurrentRow(row);
}
function handleItemData(data: any) {
  itemGridApi.setGridOptions({
    data,
  });
}
function handleItemLoading(isLoading: boolean) {
  itemGridApi.setLoading(isLoading);
}

function handlePGI() {
  modalApi.setState({ confirmLoading: true });
  modalApi.setState({ loading: true });
  bwOutPickingHandlePGI(data.value.id)
    .then(() => {
      handleClose();
      message.success('操作成功！');
    })
    .catch(() => {})
    .finally(() => {
      modalApi.setState({ loading: false });
      modalApi.setState({ confirmLoading: false });
    });
}
function handleClose() {
  modalApi.close();
  emit('success');
}

async function fetch() {
  setTimeout(() => {
    gridApi.grid.commitProxy('query');
  }, 200);
}
</script>
<template>
  <Modal>
    <Grid>
      <template #top>
        <Descriptions
          :column="{ xs: 1, sm: 2, md: 3, lg: 5 }"
          bordered
          size="small"
        >
          <Descriptions.Item label="拣货单号">
            {{ headData.PickingDocNo }}
          </Descriptions.Item>
          <Descriptions.Item label="贴标少">
            {{ headData.MissingQty }}
          </Descriptions.Item>
          <Descriptions.Item label="拣货托盘">
            {{ headData.PalletsCount }}
          </Descriptions.Item>
          <Descriptions.Item label="拣货总数">
            {{ headData.PickingQty }}
          </Descriptions.Item>
          <Descriptions.Item label="未发数量">
            {{ headData.UnDeliveryQty }}
          </Descriptions.Item>
        </Descriptions>
      </template>
      <template #toolbar_left>
        <div class="hidden pl-1 text-base font-medium md:block">
          <span class="mr-2">Outbound明细</span>
        </div>
      </template>
      <template #toolbar-tools> </template>
      <template #batchmanage="{ row }">
        <Tag :color="row.MaterialIsBatchManaged ? 'green' : 'red'">
          {{ row.MaterialIsBatchManaged ? '是' : '否' }}
        </Tag>
      </template>
      <template #expand_content>
        <div class="m-4">
          <ItemGrid />
        </div>
      </template>
    </Grid>
  </Modal>
</template>
