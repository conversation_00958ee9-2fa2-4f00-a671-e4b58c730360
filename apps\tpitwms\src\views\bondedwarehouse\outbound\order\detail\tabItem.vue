<script setup lang="ts">
import type { VxeGridListeners, VxeGridProps } from '#/adapter';

import { defineProps, reactive, ref } from 'vue';

import { useVbenDrawer, useVbenModal, VbenLoading } from '@vben/common-ui';
import { AntClear } from '@vben/icons';

import {
  Dropdown,
  InputNumber,
  Menu,
  MenuItem,
  message,
  /* CheckableTag, */ Popconfirm,
  Switch,
  Tag,
} from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import {
  bwOutOrderItemProcessingRequiredUpdate,
  bwOutOrderItemStatusUpdate,
  bwOutOrderItemUpdate,
  getOutOrderItem,
} from '#/api/bondedwarehouse/outbound';
import materialDrawer from '#/views/maintenance/data/material/materialDrawer.vue';
import batchDrawer from '#/views/sapneo/master/batch/batchDrawer.vue';
import skuDrawer from '#/views/sapneo/master/product/skuDrawer.vue';

import itemAdjustmentModal from './itemAdjustmentModal.vue';
import specialAdjustmentModal from './specialAdjustmentModal.vue';

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
  bl: {
    type: String,
    default: '',
  },
});

const loadingRef = ref(false);

const allBrands = ref([]) as any;
const allDivisions = ref([]) as any;
/* const processingTypeOptions = ref([]); */

const [SkuDrawer, skuDrawerApi] = useVbenDrawer({
  connectedComponent: skuDrawer,
});
const [BatchDrawer, batchDrawerApi] = useVbenDrawer({
  connectedComponent: batchDrawer,
});
const [MaterialDrawer, materialDrawerApi] = useVbenDrawer({
  // 连接抽离的组件
  connectedComponent: materialDrawer,
});
const [ItemAdjustmentModal, itemAdjustmentModalApi] = useVbenModal({
  // 连接抽离的组件
  connectedComponent: itemAdjustmentModal,
});
const [SpecialAdjustmentModal, specialAdjustmentModalApi] = useVbenModal({
  // 连接抽离的组件
  connectedComponent: specialAdjustmentModal,
});
function openSkuDrawer(id: string) {
  skuDrawerApi.setData({ id });
  skuDrawerApi.open();
}
function openMaterialDrawer(id: string) {
  materialDrawerApi.setData({ id });
  materialDrawerApi.open();
}
function openBatchDrawer(sku: string, batch: string) {
  batchDrawerApi.setData({ sku, batch });
  batchDrawerApi.open();
}
function openAdjustmentModal() {
  itemAdjustmentModalApi.setData({ id: props.id });
  itemAdjustmentModalApi.open();
}
function openSpecialAdjustmentModal() {
  specialAdjustmentModalApi.setData({ id: props.id });
  specialAdjustmentModalApi.open();
}

/* const [ProcessingTypeForm, processingTypeFormApi] = useVbenForm({
  handleSubmit: handleUpdateProcessingType,
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
    labelClass: 'w-24',
  },
  schema: [
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择',
      },
      fieldName: 'ProcessingType',
      label: '加工类型',
      rules: 'required',
    },
  ],
  showDefaultActions: false,
}); */

const gridOptions = reactive<VxeGridProps>({
  id: 'BWOutOrderItem',
  checkboxConfig: { highlight: true, range: true },
  cellStyle({ row, column }) {
    if (['RetentionNum'].includes(column.field) && row.RetentionNum > 0) {
      return {
        backgroundColor: '#fff9db',
      };
    }
    return null;
  },
  columns: [
    { type: 'checkbox', width: 48, fixed: 'left' },
    {
      fixed: 'left',
      title: '#',
      type: 'seq',
      width: 60,
    },
    {
      field: 'MatCode',
      title: '物料编号',
      width: 88,
      fixed: 'left',
      filters: [{ data: { vals: [], sVal: '' } }],
      filterRender: {
        name: 'FilterContent',
      },
      slots: { default: 'material' },
    },
    {
      field: 'BatchNo',
      fixed: 'left',
      title: '批号',
      width: 80,
      filters: [{ data: { vals: [], sVal: '' } }],
      filterRender: {
        name: 'FilterContent',
      },
      slots: { default: 'batchno' },
    },
    /* {
      field: 'ShelfLifeExpirationDate',
      title: '产品效期',
      width: 88,
      formatter: 'formatDate',
    }, */
    {
      field: 'OrderPickUpStatus',
      fixed: 'left',
      title: '备货状态',
      width: 88,
      filters: [
        { label: '待备货', value: false },
        { label: '已备货', value: true },
      ],
      filterMultiple: false,
      slots: { default: 'opStatus' },
    },
    {
      field: 'GoodsNum',
      title: '产品数量',
      width: 100,
    },
    {
      field: 'RetentionNum',
      title: '建议留样',
      width: 96,
      editRender: { name: 'AInput' },
      slots: {
        edit: 'retention_edit',
      },
    },
    {
      field: 'IsProcessingRequired',
      title: '是否需贴标',
      width: 120,
      filters: [
        { label: '是', value: 1 },
        { label: '否', value: 0 },
      ],
      filterMultiple: false,
      editRender: {
        name: 'ASelect',
        autofocus: '',
      },
      slots: {
        default: 'processingRequired',
        edit: 'processingRequired_edit',
      },
    },
    {
      field: 'oSPCB',
      title: 'SPCB',
      width: 60,
    },
    {
      field: 'oExtLabNum',
      title: '外标数',
      width: 64,
    },
    {
      field: 'oInLabNum',
      title: '内标数',
      width: 64,
    },
    {
      field: 'oSealingStickerNum',
      title: '封口贴数',
      width: 72,
    },
    {
      field: 'oQRCodeNum',
      title: '二维码贴数',
      width: 88,
    },
    {
      field: 'oPlasticPackagingNum',
      title: '塑封数',
      width: 64,
    },
    {
      field: 'oOutBoxLabelNum',
      title: '外箱贴数',
      width: 72,
    },
    {
      field: 'PalletNo',
      title: '入库托盘号',
      width: 88,
    },
    {
      field: 'SSCC',
      title: 'SSCC',
      width: 144,
    },
    {
      field: 'AreaName',
      title: '来源库区',
      width: 88,
    },
    {
      field: 'BIN',
      title: '来源货位',
      width: 96,
    },
    {
      field: 'OrderPickUpAreaName',
      title: '备货库区',
      width: 88,
    },
    {
      field: 'Remark',
      title: '备货备注',
      width: 150,
      editRender: { name: 'AInput', autofocus: '' },
      slots: {
        edit: 'remark_edit',
      },
    },
    {
      field: 'Invoice',
      title: '发票号',
      width: 96,
    },
    {
      field: 'oDivision',
      title: 'Division',
      width: 80,
    },
    {
      field: 'oBrandName',
      title: '品牌',
      width: 80,
    },
    {
      field: 'oOriginName',
      title: '原产国',
      width: 64,
    },
    {
      field: 'oMatType',
      title: '物料类型',
      width: 88,
    },
    {
      field: 'oBarCode',
      title: '条码',
      width: 128,
    },
    {
      field: 'oCName',
      title: '中文品名',
      minWidth: 220,
    },
  ],
  editConfig: {
    trigger: 'dblclick',
    mode: 'cell',
    showIcon: true,
    showStatus: true,
  },
  filterConfig: {
    remote: false,
  },
  footerRowStyle: { 'font-weight': 'bold ' },
  footerMethod({ columns, data }) {
    return [
      columns.map((column, columnIndex) => {
        if (columnIndex === 0) {
          return `合计`;
        }
        if (columnIndex === 1) {
          return `${data.length} 项`;
        }
        if (['GoodsNum', 'RetentionNum'].includes(column.field)) {
          return sumNum(data, column.field);
        }
        if (['OrderPickUpStatus'].includes(column.field)) {
          const status = [
            ...new Set(
              data
                .map((item) => item.OrderPickUpStatus)
                .filter((item) => item !== ''),
            ),
          ];
          if (status.length === 1) {
            return `${status[0] ? '已备货' : '待备货'}`;
          } else if (status.length > 1) {
            return `部分备货`;
          }
        }
        if (['PalletNo'].includes(column.field)) {
          const traynos = [
            ...new Set(
              data.map((item) => item.PalletNo).filter((item) => item !== ''),
            ),
          ];
          return `托盘数: ${traynos.length}`;
        }
        if (['oDivision'].includes(column.field)) {
          /* allBrand.value = [
            ...new Set(
              data.map((item) => item.iBrandName).filter((item) => item !== ''),
            ),
          ]; */
          allDivisions.value = [
            ...new Set(
              data.map((item) => item.oDivision).filter((item) => item !== ''),
            ),
          ];
        }
        return null;
      }),
    ];
  },
  height: 'auto',
  keepSource: true,
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    ajax: {
      query: async ({ $grid }) => {
        const res = await getOutOrderItem(props.id, -1);
        /* processingTypeOptions.value = res.options.filter((item: any) => {
          return item.type === 'ProcessingType';
        }); */
        allBrands.value = res.allBrands;
        const oBrandColumn = $grid.getColumnByField('oBrandName');
        if (oBrandColumn) {
          $grid.setFilter(oBrandColumn, allBrands.value);
        }
        return res.data;
      },
    },
    seq: true,
  },
  rowStyle({ row }) {
    return row.IsUnilateral
      ? {
          backgroundColor: '#ffe8cc',
          color: '#222',
        }
      : null;
  },
  showFooter: true,
  size: 'mini',
  sortConfig: {
    remote: false,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
  scrollX: { enabled: true, gt: 0 },
  scrollY: { enabled: true, mode: 'wheel', gt: 30, oSize: 0 },
  menuConfig: {
    trigger: 'cell',
    body: {
      options: [
        [
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuCopy',
            disabled: false,
            name: '复制单元格',
            prefixConfig: {
              icon: 'vxe-icon-copy',
            },
            visible: true,
          },
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuRefresh',
            disabled: false,
            name: '刷新',
            prefixConfig: { icon: 'vxe-icon-refresh' },
            visible: true,
          },
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuExport',
            disabled: false,
            name: '导出明细',
            prefixConfig: { icon: 'vxe-icon-download' },
            visible: true,
          },
        ],
      ],
    },
    className: 'font-medium shadow rounded-md',
  },
});
const gridEvents: VxeGridListeners = {
  editClosed({ row, rowIndex }) {
    handleUpdateByRow(row, rowIndex);
  },
  menuClick({ menu }) {
    switch (menu.code) {
      case 'menuExport': {
        handleExport();
        break;
      }
      default: {
        break;
      }
    }
  },
};

const [Grid, gridApi] = useVbenVxeGrid({ gridOptions, gridEvents });

function handleExport() {
  gridApi.grid.exportData({
    filename: `出库订单明细_${props.bl}`,
    sheetName: 'Sheet1',
    type: 'xlsx',
    isFooter: false,
    columnFilterMethod: ({ column }) => {
      return column.field !== undefined;
    },
  });
}

function sumNum(list: any[], field: string) {
  let count = 0;
  list.forEach((item) => {
    count += Number(item[field]);
  });
  return count;
}

function handleSuccess() {
  gridApi.grid.commitProxy('query');
}

function handleUpdateByRow(row: any, rowIndex: number) {
  if (gridApi.grid.isUpdateByRow(row)) {
    gridApi.setLoading(true);
    bwOutOrderItemUpdate(row)
      .then(() => {
        message.success(`序号${rowIndex + 1} 保存成功！ `);
        handleSuccess();
      })
      .catch(() => {
        gridApi.grid.revertData(row);
      })
      .finally(() => {
        gridApi.setLoading(false);
      });
  }
}
function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
}
function handleMultiOperate(typ: number) {
  const checkedRecords = gridApi.grid
    .getCheckboxRecords()
    .map((item: any) => item.Guid);
  if (checkedRecords.length === 0) {
    message.info(`请勾选产品明细`);
    return;
  }
  loadingRef.value = true;
  bwOutOrderItemStatusUpdate(props.id, checkedRecords, typ)
    .then(() => {
      message.success('操作成功！');
      handleSuccess();
    })
    .catch(() => {})
    .finally(() => {
      loadingRef.value = false;
    });
}
function handleMenuClick(e: any) {
  const checkedRecords = gridApi.grid
    .getCheckboxRecords()
    .map((item: any) => item.Guid);
  if (checkedRecords.length === 0) {
    message.info(`请勾选产品明细`);
    return;
  }
  loadingRef.value = true;
  bwOutOrderItemProcessingRequiredUpdate(checkedRecords, e.key)
    .then(() => {
      message.success('操作成功！');
      handleSuccess();
    })
    .catch(() => {})
    .finally(() => {
      loadingRef.value = false;
    });
}
</script>

<template>
  <div class="flex h-full flex-col">
    <Grid>
      <template #toolbar_left>
        <div class="hidden pl-1 text-base font-medium md:block">
          <span class="mr-2">产品明细</span>
        </div>
        <a-button class="mr-1" size="small" @click="clearFilterAndSort()">
          <AntClear class="size-5" />
        </a-button>
        <div class="hidden md:block">
          <a-button
            class="ml-2"
            size="small"
            type="primary"
            @click="openAdjustmentModal()"
          >
            明细调整
          </a-button>
          <a-button
            class="ml-2"
            size="small"
            @click="openSpecialAdjustmentModal()"
          >
            单边冻结明细调整
          </a-button>

          <!-- <a-button class="ml-2" size="small" @click="1"> 订标 </a-button> -->
        </div>
      </template>
      <template #toolbar-tools>
        <Dropdown :trigger="['click']" class="mr-2">
          <template #overlay>
            <Menu @click="handleMenuClick">
              <MenuItem :key="1">需要贴标</MenuItem>
              <MenuItem :key="0">不贴标</MenuItem>
            </Menu>
          </template>
          <a-button class="mr-1" size="small"> 批量设置</a-button>
        </Dropdown>
        <a-button
          :loading="loadingRef"
          class="mr-2"
          size="small"
          type="primary"
          @click="handleMultiOperate(1)"
        >
          确认备货
        </a-button>
        <a-button
          :loading="loadingRef"
          size="small"
          @click="handleMultiOperate(0)"
        >
          撤销备货
        </a-button>
      </template>
      <template #opStatus="{ row }">
        <Tag :color="row.OrderPickUpStatus ? 'green' : 'red'">
          {{ row.OrderPickUpStatus ? '已备货' : '待备货' }}
        </Tag>
      </template>
      <template #material="{ row }">
        <Popconfirm
          cancel-text="产品主数据"
          ok-text="基础物料"
          title="数据展示"
          @cancel="openSkuDrawer(row.MatCode)"
          @confirm="openMaterialDrawer(row.MatCode)"
        >
          <a class="text-blue-500" @click.prevent>
            {{ row.MatCode }}
          </a>
        </Popconfirm>
      </template>
      <template #batchno="{ row }">
        <a
          v-if="row.BatchNo"
          class="text-blue-500"
          @click="openBatchDrawer(row.MatCode, row.BatchNo)"
        >
          {{ row.BatchNo }}
        </a>
      </template>
      <template #retention_edit="{ row }">
        <InputNumber
          v-model:value="row.RetentionNum"
          :disabled="row.OrderPickUpStatus"
          :min="0"
          size="small"
        />
      </template>
      <template #processingRequired="{ row }">
        <Tag :color="row.IsProcessingRequired ? 'green' : 'red'">
          {{ row.IsProcessingRequired ? '是' : '否' }}
        </Tag>
      </template>
      <!-- <template #processingRequired_head>
        <span>{{ '是否需贴标' }}</span>
        <a-button type="link" size="small" @click="1"> 12 </a-button>
      </template> -->
      <template #processingRequired_edit="{ row }">
        <!-- <a-select
          v-model:value="row.IPR"
          :disabled="row.OrderPickUpStatus !== 0"
          :options="[
            { label: '是', value: 1 },
            { label: '否', value: 0 },
          ]"
          allow-clear
        /> -->
        <Switch
          v-model:checked="row.IsProcessingRequired"
          :disabled="row.OrderPickUpStatus"
          checked-children="是"
          un-checked-children="否"
        />
      </template>
      <template #remark_edit="{ row }">
        <a-input
          v-model:value="row.Remark"
          :disabled="row.OrderPickUpStatus"
          :maxlength="128"
          autocomplete="off"
          size="small"
        />
      </template>
      <template #bottom>
        <div class="h-6 pt-1 text-sm font-bold">
          <div class="float-left hidden md:block">
            <span>{{ '品牌: ' }}</span>
            <Tag
              v-for="(item, index) in allBrands"
              :key="index"
              :value="item.value"
              color="#108ee9"
            >
              {{ item.value }}
            </Tag>
          </div>
          <div class="float-right hidden md:block">
            <span>{{ 'Division: ' }}</span>
            <Tag
              v-for="(item, index) in allDivisions"
              :key="index"
              color="#87d068"
            >
              {{ item }}
            </Tag>
          </div>
          <div class="float-right"></div>
        </div>
      </template>
    </Grid>
    <ItemAdjustmentModal @success="handleSuccess" />
    <SpecialAdjustmentModal @success="handleSuccess" />
    <MaterialDrawer @success="handleSuccess" />
    <SkuDrawer />
    <BatchDrawer />
    <VbenLoading :spinning="loadingRef" />
  </div>
</template>
