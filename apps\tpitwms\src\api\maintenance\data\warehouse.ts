import { requestClient, sleep } from '#/api/request';

/**
 * 系统维护 仓库信息相关API
 */
export async function getWarehouseList() {
  await sleep(100);
  return requestClient.get('/maintenance/data/warehouse/getList');
}

export async function warehouseUpdate(params: object) {
  await sleep(100);
  return requestClient.post('/maintenance/data/warehouse/update', params);
}

export async function warehouseDelete(id: string) {
  await sleep(100);
  return requestClient.post('/maintenance/data/warehouse/del', { id });
}

export async function getWarehouseData(id: string) {
  await sleep(100);
  return requestClient.get('/maintenance/data/warehouse/getData', {
    params: { id },
  });
}

export async function areaUpdate(params: object) {
  await sleep(100);
  return requestClient.post('/maintenance/data/warehouse/area/update', params);
}

export async function getAreaData(id: string) {
  await sleep(100);
  return requestClient.get('/maintenance/data/warehouse/area/getData', {
    params: { id },
  });
}

export async function areaDelete(id: string) {
  await sleep(100);
  return requestClient.post('/maintenance/data/warehouse/area/del', { id });
}
