<script lang="ts" setup>
import type { VxeGridListeners, VxeGridProps } from '#/adapter';

import { reactive, ref } from 'vue';

import { useVbenDrawer, useVbenModal } from '@vben/common-ui';
import { AntClear } from '@vben/icons';

import { message, Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import {
  bwInReturnOBDImport,
  getUnUsedReturnOBDByDoc,
} from '#/api/bondedwarehouse/inbound';
import { getReturnsOutboundItem } from '#/api/sapneo';
import customerDrawer from '#/views/sapneo/master/customer/customerDrawer.vue';
import skuDrawer from '#/views/sapneo/master/product/skuDrawer.vue';

const emit = defineEmits(['success']);
const [CustomerDrawer, customerDrawerApi] = useVbenDrawer({
  connectedComponent: customerDrawer,
});
const [SkuDrawer, skuDrawerApi] = useVbenDrawer({
  connectedComponent: skuDrawer,
});
function openCustomerDrawer(id: string) {
  customerDrawerApi.setData({ id });
  customerDrawerApi.open();
}
function openSkuDrawer(id: string) {
  skuDrawerApi.setData({ id });
  skuDrawerApi.open();
}
const data = ref();
const searchField: any = reactive({
  DeliveryDocument: undefined,
});
const [Modal, modalApi] = useVbenModal({
  class: 'w-full h-full',
  fullscreenButton: true,
  closeOnClickModal: false,
  confirmText: '确认绑定',
  zIndex: 900,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await handleImport();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      data.value = modalApi.getData<Record<string, any>>();
      fetch();
    }
  },
  title: 'Return OBD 绑定',
});
const gridOptions = reactive<VxeGridProps>({
  id: 'ReturnDeliveryImport',
  checkboxConfig: { highlight: true, range: true },
  columns: [
    { type: 'radio', width: 40 },
    { type: 'expand', width: 48, slots: { content: 'expand_content' } },
    {
      field: 'DeliveryDocument',
      title: 'ReturnOBD',
      minWidth: 100,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'Plant',
      title: '货主工厂',
      minWidth: 88,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'SO',
      title: '销售订单',
      minWidth: 88,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'DeliveryDate',
      title: '计划交付日期',
      minWidth: 136,
      formatter: 'formatDateTime',
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'TotalQty',
      title: '总件数',
      minWidth: 88,
    },
    {
      field: 'ItemsCount',
      title: 'Item项',
      minWidth: 60,
    },
    {
      field: 'CreationDate',
      title: 'S创建日期',
      minWidth: 132,
      formatter: 'formatDateTime',
      sortable: true,
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'CreateDate',
      title: 'W创建日期',
      minWidth: 132,
      formatter: 'formatDateTime',
      sortable: true,
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
  ],
  expandConfig: {
    accordion: true,
  },
  filterConfig: {
    remote: true,
  },
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: ({ filters, page, sorts }) => {
        const queryParams: any = Object.assign({ id: data.value.id }, page);
        // 处理排序条件
        if (sorts.length === 0) {
          queryParams.sort = '_Identify desc';
        } else {
          const sortItem = sorts.map((item) => {
            const newItem = `${item.field} ${item.order}`;
            return newItem;
          });
          queryParams.sort = sortItem.join(',');
        }
        // 处理筛选
        Object.getOwnPropertyNames(searchField).forEach((key) => {
          searchField[key] = undefined;
        });
        filters.forEach(({ field, values }) => {
          queryParams[field] = values.join(',');
          if (Object.prototype.hasOwnProperty.call(searchField, field)) {
            const jsonObject = JSON.parse(values.toString());
            searchField[field] = jsonObject.text;
          }
        });
        return getUnUsedReturnOBDByDoc(queryParams);
      },
    },
    seq: true,
  },
  /* scrollY: { enabled: true, gt: 0 }, */
  size: 'mini',
  sortConfig: {
    remote: true,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
});
const itemGridOptions = reactive<VxeGridProps>({
  columns: [
    { type: 'seq', width: 48 },
    {
      field: 'DeliveryDocumentItem',
      title: '项号',
      width: 88,
    },
    { field: 'Plant', title: '工厂编码', width: 72 },
    {
      field: 'StorageLocation',
      title: '库存地点',
      width: 72,
    },

    {
      field: 'Material',
      title: '产品编号',
      width: 100,
      slots: { default: 'skucode' },
    },
    {
      field: 'MaterialIsBatchManaged',
      title: '批次管理',
      width: 72,
      align: 'center',
      slots: { default: 'batchmanage' },
    },
    { field: 'DeliveryQuantity', title: '指令数量', minWidth: 72 },
    { field: 'DeliveryQuantityUnit', title: '单位', width: 48 },
    { field: 'ItemGrossWeight', title: '毛重', width: 80 },
    { field: 'ItemNetWeight', title: '净重', width: 80 },
    { field: 'ItemWeightUnit', title: '重量单位', width: 72 },
    { field: 'ItemVolume', title: '体积', width: 80 },
    { field: 'ItemVolumeUnit', title: '体积单位', width: 72 },
    { field: 'DistributionChannel', title: '分销渠道', width: 72 },
    { field: 'Division', title: 'Division', width: 72 },
    { field: 'GoodsMovementType', title: '移动类型', width: 72 },
    { field: 'ReferenceSDDocument', title: '销售订单', width: 88 },
    { field: 'ReferenceSDDocumentItem', title: '销售项号', width: 72 },
    {
      field: 'ZZ1_Customer_referenc1_DLI',
      title: '采购订单',
      minWidth: 108,
    },
    {
      field: 'ZZ1_Customer_PO_Type_DLI',
      title: '直流订单',
      width: 72,
    },
  ],
  customConfig: {
    storage: false,
  },
  menuConfig: {
    body: {
      options: [
        [
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuCopy',
            disabled: false,
            name: '复制单元格',
            prefixConfig: {
              icon: 'vxe-icon-copy',
            },
            visible: true,
          },
        ],
      ],
    },
    className: 'font-medium shadow rounded-md',
  },
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    enabled: false,
  },
  rowConfig: {
    useKey: true,
    keyField: 'Guid',
  },
  rowStyle({ row }) {
    if (row.Qty === 0) {
      return {
        backgroundColor: '#fff4e6',
        color: '#222',
      };
    }
  },
  size: 'mini',
  toolbarConfig: {
    enabled: false,
  },
  treeConfig: {
    transform: true,
    rowField: 'ItemNumber',
    parentField: 'ParentItem',
    showLine: true,
  },
});
const [ItemGrid, itemGridApi] = useVbenVxeGrid({
  gridOptions: itemGridOptions,
});
const gridEvents: VxeGridListeners = {
  async toggleRowExpand({ expanded, row }) {
    await handleItemData([]);
    if (expanded) {
      handleItemLoading(true);
      const itemData = await getReturnsOutboundItem(row.Guid);
      await handleHeadRow(row);
      await handleItemData(itemData);
      handleItemLoading(false);
      itemGridApi.grid?.setAllTreeExpand(true);
    }
  },
};
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions, gridEvents });
function handleHeadRow(row: any) {
  gridApi.grid.setCurrentRow(row);
}
function handleItemData(data: any) {
  itemGridApi.setGridOptions({
    data,
  });
}
function handleItemLoading(isLoading: boolean) {
  itemGridApi.setLoading(isLoading);
}
function handleImport() {
  const currRow = gridApi.grid.getRadioRecord();
  if (!currRow) {
    message.info(`请勾选需要绑定的Return OBD`);
    return;
  }
  /* const checkedRecords = gridApi.grid
    .getCheckboxRecords()
    .map((item: any) => item.Guid);
  if (checkedRecords.length === 0) {
    message.info(`请勾选需要导入的Return OBD`);
    return;
  } */
  modalApi.setState({ confirmLoading: true });
  modalApi.setState({ loading: true });
  bwInReturnOBDImport(data.value.id, currRow.Guid)
    .then(() => {
      handleClose();
    })
    .catch(() => {})
    .finally(() => {
      modalApi.setState({ loading: false });
      modalApi.setState({ confirmLoading: false });
    });
}
function handleClose() {
  modalApi.close();
  emit('success');
}
function searchEvent(field: string) {
  const column = gridApi.grid.getColumnByField(field);
  if (column) {
    // 修改第一个选项为勾选状态
    const option = column.filters[0];
    if (!option) {
      return;
    }

    if (searchField[field]) {
      option.data = { type: '1', text: searchField[field] };
      option.value = JSON.stringify(option.data);
      option.checked = true;
    } else {
      option.data = { type: '0', text: undefined };
      option.checked = false;
    }

    // 修改条件之后，需要手动调用 updateData 处理表格数据
    gridApi.grid.updateData();
    gridApi.grid.commitProxy('query');
  }
}
function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
  gridApi.grid.commitProxy('query');
}
async function fetch() {}
</script>
<template>
  <Modal>
    <Grid>
      <template #toolbar_left>
        <a-button class="mr-1" @click="clearFilterAndSort()">
          <AntClear class="size-5" />
        </a-button>
        <div class="hidden pl-1 md:block">
          <a-input-search
            v-model:value="searchField.DeliveryDocument"
            allow-clear
            class="mr-2 w-60"
            placeholder="ReturnOBD"
            @search="searchEvent('DeliveryDocument')"
          />
        </div>
      </template>
      <template #toolbar-tools> </template>
      <template #expand_content>
        <div>
          <ItemGrid>
            <template #skucode="{ row }">
              <a
                v-if="row.Material"
                class="text-blue-500"
                @click="openSkuDrawer(row.Material)"
              >
                {{ row.Material }}
              </a>
            </template>
            <template #batchmanage="{ row }">
              <Tag :color="row.isBatchManagementRequired ? 'green' : 'red'">
                {{ row.isBatchManagementRequired ? '是' : '否' }}
              </Tag>
            </template>
          </ItemGrid>
        </div>
      </template>
      <template #supplier="{ row }">
        <a-button
          size="small"
          type="link"
          @click="openCustomerDrawer(row.Supplier)"
        >
          {{ row.Supplier }}
        </a-button>
      </template>
    </Grid>
    <CustomerDrawer />
    <SkuDrawer />
  </Modal>
</template>
