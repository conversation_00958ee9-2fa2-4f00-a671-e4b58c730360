<script lang="ts" setup>
import { /* h, */ ref } from 'vue';

import { useVbenDrawer, useVbenModal } from '@vben/common-ui';
import { AntSearch } from '@vben/icons';

import { Descriptions, message, Modal } from 'ant-design-vue';

import { useVbenForm } from '#/adapter';
import {
  stockMovementGetStockData,
  stockMovementItemAdd,
} from '#/api/bondedwarehouse/stock';
import { getBatchStatus } from '#/api/sapneo';
import batchSelectDrawer from '#/views/bondedwarehouse/inbound/order/detail/batchSelectDrawer.vue';

const emit = defineEmits(['success']);

const data = ref();
const itemData = ref({}) as any;
const submitData = ref({}) as any;
const [BatchSelectDrawer, BatchSelectDrawerApi] = useVbenDrawer({
  connectedComponent: batchSelectDrawer,
});

const [Form, formApi] = useVbenForm({
  handleSubmit: onSubmit,
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
    labelClass: 'w-24',
  },
  schema: [
    {
      component: 'Select',
      componentProps: {
        getPopupContainer: () => document.body,
        options: [],
      },
      fieldName: 'GoodsMovementType',
      label: '移动类型',
      rules: 'required',
      help: '不带*的移动类型由WMS发起，通过API提交到SAP。带*的移动类型由SAP发起，需WMS做同样的处理。',
    },
    {
      component: 'Select',
      componentProps: {
        getPopupContainer: () => document.body,
        options: [],
      },
      /* disabled: true, */
      fieldName: 'GoodsMovementReasonCode',
      help: '移动类型344必须选择移动原因，其余类型不需要',
      label: '移动原因',
      dependencies: {
        disabled(values) {
          return values.GoodsMovementType !== '344';
        },
        trigger(values, formApi) {
          if (values.GoodsMovementType !== '344') {
            formApi.setFieldValue('GoodsMovementReasonCode', undefined);
          }
        },
        rules(values) {
          if (values.GoodsMovementType === '344') {
            return 'required';
          }
          return null;
        },
        triggerFields: ['GoodsMovementType'],
      },
    },
    {
      component: 'Select',
      componentProps: {
        getPopupContainer: () => document.body,
        options: [],
      },
      /* disabled: true, */
      fieldName: 'StockType',
      label: '库存类型',
      help: '移动类型301和309，需手动选择当前库存类型',
      rules: 'required',
      dependencies: {
        disabled(values) {
          return [
            '311',
            '325',
            '343',
            '344',
            '701',
            '702',
            '707',
            '708',
          ].includes(values.GoodsMovementType);
        },
        trigger(values, formApi) {
          switch (true) {
            case ['311', '344', '701', '702'].includes(
              values.GoodsMovementType,
            ): {
              formApi.setFieldValue('StockType', 'UNR');
              break;
            }
            case ['325', '343', '707', '708'].includes(
              values.GoodsMovementType,
            ): {
              formApi.setFieldValue('StockType', 'BLK');
              break;
            }
            default: {
              formApi.setFieldValue('StockType', undefined, false);
            }
          }
        },
        triggerFields: ['GoodsMovementType'],
      },
    },
    {
      component: 'Select',
      componentProps: {
        getPopupContainer: () => document.body,
        options: [],
      },
      disabled: true,
      fieldName: 'ToStockType',
      label: '目标库存类型',
      /* rules: 'required', */
      dependencies: {
        trigger(values, formApi) {
          switch (true) {
            case ['343', '701', '702'].includes(values.GoodsMovementType): {
              formApi.setFieldValue('ToStockType', 'UNR');
              break;
            }
            case ['344', '707', '708'].includes(values.GoodsMovementType): {
              formApi.setFieldValue('ToStockType', 'BLK');
              break;
            }
            default: {
              formApi.setFieldValue('ToStockType', values.StockType);
            }
          }
        },
        triggerFields: ['GoodsMovementType', 'StockType'],
      },
    },
    {
      component: 'Select',
      componentProps: {
        getPopupContainer: () => document.body,
        options: [],
      },
      disabled: true,
      fieldName: 'Plant',
      label: '货主工厂',
    },
    {
      component: 'Select',
      componentProps: {
        getPopupContainer: () => document.body,
        options: [],
      },
      disabled: true,
      fieldName: 'ToPlant',
      label: '目标工厂',
      rules: 'required',
      dependencies: {
        trigger(values, formApi) {
          formApi.setFieldValue('ToPlant', values.Plant);
        },
        triggerFields: ['Plant'],
      },
    },
    {
      component: 'Select',
      componentProps: {
        getPopupContainer: () => document.body,
        options: [],
      },
      disabled: true,
      fieldName: 'StorageLocation',
      label: 'SAP位置',
    },
    {
      component: 'Select',
      componentProps: {
        getPopupContainer: () => document.body,
        options: [],
      },
      /* disabled: true, */
      fieldName: 'ToLocation',
      label: '目标位置',
      rules: 'required',
      dependencies: {
        disabled(values) {
          return !['311', '325'].includes(values.GoodsMovementType);
        },
        trigger(values, formApi) {
          switch (true) {
            case ['311', '325'].includes(values.GoodsMovementType): {
              formApi.setFieldValue('ToLocation', undefined, false);
              break;
            }
            default: {
              if (values.StorageLocation) {
                formApi.setFieldValue('ToLocation', values.StorageLocation);
              }
            }
          }
        },
        triggerFields: ['GoodsMovementType', 'StorageLocation'],
      },
    },
    {
      component: 'Input',
      componentProps: {
        maxlength: 16,
      },
      disabled: true,
      fieldName: 'MatCode',
      label: '当前物料',
    },
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 16,
      },
      /* disabled: true, */
      fieldName: 'ToMaterial',
      label: '目标物料',
      rules: 'required',
      dependencies: {
        disabled(values) {
          return values.GoodsMovementType !== '309';
        },
        trigger(values, formApi) {
          if (values.MatCode) {
            formApi.setFieldValue('ToMaterial', values.MatCode);
          }
          if (values.GoodsMovementType === '309') {
            formApi.setFieldValue('ToMaterial', undefined, false);
          }
        },
        triggerFields: ['MatCode', 'GoodsMovementType'],
      },
    },
    {
      component: 'Input',
      componentProps: {
        maxlength: 16,
      },
      disabled: true,
      fieldName: 'BatchNo',
      label: '当前批次',
    },
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 16,
      },
      /* disabled: true, */
      fieldName: 'ToBatch',
      label: '目标批次',
      /* help: '目标批次主数据不存在时，请通过344类型调整并冻结库存', */
      /* rules: 'required', */
      dependencies: {
        disabled(values) {
          return (
            !values.isBatchManagementRequired ||
            !['301', '344'].includes(values.GoodsMovementType)
          );
        },
        trigger(values, formApi) {
          if (values.BatchNo) {
            formApi.setFieldValue('ToBatch', values.BatchNo);
          }
          if (values.GoodsMovementType === '301') {
            formApi.setFieldValue('ToBatch', undefined, false);
          }
        },
        rules(values) {
          if (values.isBatchManagementRequired) {
            return 'required';
          }
          return null;
        },
        triggerFields: [
          'GoodsMovementType',
          'BatchNo',
          'isBatchManagementRequired',
        ],
      },
    },
    {
      component: 'Input',
      componentProps: {
        maxlength: 16,
      },
      disabled: true,
      fieldName: 'ActualExpiration',
      label: '当前效期',
    },
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 10,
      },
      fieldName: 'ToExpiration',
      label: '目标效期',
      dependencies: {
        disabled(values) {
          return (
            !values.isBatchManagementRequired ||
            !['301', '344'].includes(values.GoodsMovementType)
          );
        },
        trigger(values, formApi) {
          if (values.ActualExpiration) {
            formApi.setFieldValue('ToExpiration', values.ActualExpiration);
          }
          if (values.GoodsMovementType === '301') {
            formApi.setFieldValue('ToExpiration', undefined, false);
          }
        },
        rules(values) {
          if (values.isBatchManagementRequired) {
            return 'required';
          }
          return null;
        },
        triggerFields: [
          'GoodsMovementType',
          'BatchNo',
          'isBatchManagementRequired',
        ],
      },
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: 0,
      },
      defaultValue: 0,
      disabled: true,
      fieldName: 'Residual',
      label: '当前库存',
      dependencies: {
        trigger(values, formApi) {
          switch (values.StockType) {
            case 'BLK': {
              formApi.setFieldValue('Residual', values.BlockedQty, false);
              break;
            }
            case 'UNR': {
              formApi.setFieldValue('Residual', values.UnrestrictedQty, false);
              break;
            }
            default: {
              formApi.setFieldValue('Residual', 0, false);
            }
          }
        },
        triggerFields: ['StockType'],
      },
    },
    {
      component: 'InputNumber',
      componentProps: {
        autocomplete: 'off',
        min: 0,
      },
      defaultValue: 0,
      fieldName: 'MoveQty',
      help: '调整数量必须大于0，且除盘盈以外不能大于当前库存',
      label: '调整数量',
      rules: 'required',
    },
    {
      component: 'InputNumber',
      componentProps: {},
      fieldName: 'BlockedQty',
      label: 'BlockedQty',
      dependencies: {
        show: false,
        triggerFields: ['A'],
      },
    },
    {
      component: 'InputNumber',
      componentProps: {},
      fieldName: 'UnrestrictedQty',
      label: 'UnrestrictedQty',
      dependencies: {
        show: false,
        triggerFields: ['A'],
      },
    },
    {
      component: 'Switch',
      componentProps: {},
      fieldName: 'isBatchManagementRequired',
      label: 'isBatchManagementRequired',
      dependencies: {
        show: false,
        triggerFields: ['A'],
      },
    },
  ],
  showDefaultActions: false,
  wrapperClass: 'grid-cols-1 md:grid-cols-2',
});

const [BaseModal, modalApi] = useVbenModal({
  class: 'w-[640px]',
  fullscreenButton: false,
  closeOnClickModal: false,
  zIndex: 900,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await formApi.submitForm();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      modalApi.setState({ showConfirmButton: false });
      itemData.value = {};
      submitData.value = {};
      data.value = modalApi.getData<Record<string, any>>();
      formApi.resetForm();
      fetch();
    }
  },
  title: '添加调整项目',
});

async function onSubmit(values: Record<string, any>) {
  const check = await formApi.validate();
  if (check.valid) {
    if (values.MoveQty === 0) {
      message.error('调整数量需大于0！');
      return;
    }
    if (
      !['701', '707'].includes(values.GoodsMovementType) &&
      values.MoveQty - values.Residual > 0
    ) {
      message.error('除盘盈之外，调整数量不能大于当前库存！');
      return;
    }
    if (
      ['311', '325'].includes(values.GoodsMovementType) &&
      values.ToLocation === values.StorageLocation
    ) {
      message.info(
        `目标位置与当前位置一致，无需${values.GoodsMovementType}调整！`,
      );
      return;
    }
    submitData.value = values;
    if (values.GoodsMovementType === '301') {
      if (!values.isBatchManagementRequired) {
        message.info('此产品没有批次管理，无需301调整！');
        return;
      }
      if (
        values.ToBatch === values.BatchNo &&
        values.ToExpiration === values.ActualExpiration
      ) {
        message.info('目标批次效期与当前批次效期一致，无需301调整！');
        return;
      }
      checkBatch();
    } else {
      handleSubmit();
    }
  }
}
function handleSubmit() {
  Modal.destroyAll();
  modalApi.setState({ confirmLoading: true });
  modalApi.setState({ loading: true });
  stockMovementItemAdd(Object.assign({}, submitData.value, data.value))
    .then(() => {
      handleClose();
    })
    .catch(() => {})
    .finally(() => {
      modalApi.setState({ loading: false });
      modalApi.setState({ confirmLoading: false });
    });
}

function handleBatchSearch() {
  BatchSelectDrawerApi.setData({
    material: itemData.value.MatCode,
  });
  BatchSelectDrawerApi.open();
}
async function checkBatch() {
  const batchStatus = await getBatchStatus(
    submitData.value.MatCode,
    submitData.value.ToBatch,
  );
  if (batchStatus === -1) {
    message.error('目标批次主数据不存在，请通过344调整并冻结库存！');
  } else {
    handleSubmit();
  }
}

function handleBatchSelected(row: any) {
  formApi.setFieldValue('ToBatch', row.SubBatch);
  if (row.ShelfLifeExpirationDate) {
    formApi.setFieldValue(
      'ToExpiration',
      row.ShelfLifeExpirationDate.slice(0, 10),
    );
  }
}
function handleClose() {
  modalApi.close();
  emit('success');
}
async function fetch() {
  await stockMovementGetStockData(data.value.stockId)
    .then((res) => {
      modalApi.setState({ showConfirmButton: true });
      itemData.value = res.data;
      formApi.setValues(itemData.value);

      const fieldList = [
        'GoodsMovementType',
        'GoodsMovementReasonCode',
        'StockType',
        'StorageLocation',
      ];
      fieldList.forEach((field) => {
        formApi.updateSchema([
          {
            componentProps: {
              options: res.options?.filter((item: any) => {
                return item.type === field;
              }),
            },
            fieldName: field,
          },
        ]);
      });
      formApi.updateSchema([
        {
          componentProps: {
            options: res.options?.filter((item: any) => {
              return item.type === 'StockType';
            }),
          },
          fieldName: 'ToStockType',
        },
      ]);
      formApi.updateSchema([
        {
          componentProps: {
            options: res.options?.filter((item: any) => {
              return item.type === 'StorageLocation';
            }),
          },
          fieldName: 'ToLocation',
        },
      ]);
    })
    .catch(() => {})
    .finally(() => {});
}
</script>
<template>
  <BaseModal>
    <Descriptions :column="{ xs: 1, sm: 2, md: 3 }" bordered size="small">
      <Descriptions.Item label="库存状态">
        {{ itemData.StockStatus }}
      </Descriptions.Item>
      <!-- <Descriptions.Item label="HBL">
        {{ itemData.HBL }}
      </Descriptions.Item> -->
      <Descriptions.Item label="托盘号">
        {{ itemData.PalletNo }}
      </Descriptions.Item>
      <Descriptions.Item label="批次管理">
        {{ itemData.isBatchManagementRequired ? '是' : '否' }}
        <!-- <Tag :color="itemData.isBatchManagementRequired ? 'green' : 'blue'">
          {{ itemData.isBatchManagementRequired ? '是' : '否' }}
        </Tag> -->
      </Descriptions.Item>
    </Descriptions>
    <Form class="mt-4">
      <template #ToBatch="slotProps">
        <a-input-search v-bind="slotProps" @search="handleBatchSearch">
          <template #enterButton>
            <a-button :disabled="slotProps.disabled" class="!pl-1 !pr-1">
              <AntSearch class="size-5" />
            </a-button>
          </template>
        </a-input-search>
      </template>
    </Form>
    <BatchSelectDrawer @success="handleBatchSelected" />
  </BaseModal>
</template>
