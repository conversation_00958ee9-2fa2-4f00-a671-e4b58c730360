import { requestClient, sleep } from '#/api/request';

/**
 * 系统维护 品牌信息相关API
 */
export async function getBrandList(obj: object) {
  await sleep(100);
  return requestClient.get('/maintenance/data/brand/getList', {
    params: obj,
  });
}

export async function brandUpdate(params: object) {
  await sleep(100);
  return requestClient.post('/maintenance/data/brand/update', params);
}

export async function getBrandData(id: string) {
  await sleep(100);
  return requestClient.get('/maintenance/data/brand/getData', {
    params: { id },
  });
}

export async function brandDelete(id: string) {
  await sleep(100);
  return requestClient.post('/maintenance/data/brand/del', { id });
}
