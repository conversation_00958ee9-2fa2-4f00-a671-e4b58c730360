import { requestClient, sleep } from '#/api/request';

/**
 * LIS API配置相关
 */

export async function getLisApiPubData() {
  await sleep(100);
  return requestClient.get('/maintenance/interface/lisapi/pub/getData');
}

export async function lisApiPubUpdate(params: object) {
  await sleep(100);
  return requestClient.post('/maintenance/interface/lisapi/pub/update', params);
}

export async function getLisApiList() {
  await sleep(100);
  return requestClient.get('/maintenance/interface/lisapi/getList');
}

export async function lisApiUpdate(params: object) {
  await sleep(100);
  return requestClient.post('/maintenance/interface/lisapi/update', params);
}
