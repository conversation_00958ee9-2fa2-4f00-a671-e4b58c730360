<script lang="ts" setup>
import type { VxeGridProps } from '#/adapter';

import { onMounted, ref } from 'vue';

import { Page /* , useVbenModal */ } from '@vben/common-ui';

import { Card, message, Modal, Switch } from 'ant-design-vue';

import { useVbenForm, useVbenVxeGrid } from '#/adapter';
import {
  consumerStatusChange,
  consumerUpdate,
  getConsumerData,
  getTopicList,
  topicDelete,
  topicUpdate,
} from '#/api/maintenance/interface';

const submitLoading = ref(false);
const consumerData = ref({}) as any;
const apiOptions = ref([]) as any;
const isRunning = ref(false);

const [Form, baseFormApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'pb-1',
    labelClass: 'w-32',
  },
  // 提交函数
  handleSubmit: onSubmit,
  layout: 'horizontal',
  schema: [
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 128,
      },
      fieldName: 'BootstrapServers',
      label: 'BootstrapServers',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 80,
      },
      fieldName: 'GroupId',
      label: 'GroupId',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 128,
      },
      fieldName: 'TokenURL',
      label: 'AccessTokenURL',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 128,
      },
      fieldName: 'Scope',
      label: 'Scope',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 36,
      },
      fieldName: 'ClientID',
      label: 'ClientID',
      rules: 'required',
    },
    {
      component: 'InputPassword',
      componentProps: {
        autocomplete: 'off',
        maxlength: 64,
      },
      fieldName: 'ClientSecret',
      label: 'ClientSecret',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 16,
      },
      fieldName: 'ExtLogicalCluster',
      label: 'ExtLogicalCluster',
    },
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 16,
      },
      fieldName: 'ExtIdentityPoolId',
      label: 'ExtIdentityPoolId',
    },
    {
      component: 'Select',
      componentProps: {
        getPopupContainer: () => document.body,
        mode: 'tags',
        maxTagCount: 8,
      },
      fieldName: 'Topics',
      formItemClass: 'col-span-1 md:col-span-2',
      label: 'Topics',
    },
  ],
  showDefaultActions: false,
  wrapperClass: 'grid-cols-1 md:grid-cols-2',
});
const gridOptions: VxeGridProps = {
  columns: [
    { title: '#', type: 'seq', width: 48 },
    {
      editRender: {
        name: 'AInput',
        props: { autocomplete: 'off', maxlength: 48 },
      },
      field: 'TopicName',
      title: 'Topic Name',
      minWidth: 160,
    },
    {
      editRender: { name: 'ASelect' },
      field: 'APIName',
      title: 'Binding API',
      minWidth: 128,
      slots: { edit: 'apiname_edit' },
    },
    {
      editRender: {
        name: 'AInput',
        props: { autocomplete: 'off', maxlength: 255 },
      },
      field: 'KeyNode',
      title: 'API Params Xpath(Default: DATAKEY)',
      minWidth: 240,
    },

    {
      editRender: {
        name: 'AInput',
        props: { autocomplete: 'off', maxlength: 255 },
      },
      field: 'Filter',
      title: 'Local Filter By XPath',
      minWidth: 240,
    },
    { slots: { default: 'action' }, title: 'Action', width: 160 },
  ],
  customConfig: {
    storage: false,
  },
  editConfig: {
    mode: 'row',
    trigger: 'manual',
    autoClear: false,
    showStatus: true,
  },
  editRules: {
    TopicName: [{ required: true, message: 'TopicName is required' }],
  },
  keepSource: true,
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    ajax: {
      query: async () => {
        const res = await getTopicList();
        apiOptions.value = res.apis;
        baseFormApi.updateSchema([
          {
            componentProps: {
              options: res.topics,
            },
            fieldName: 'Topics',
          },
        ]);
        return res.item;
      },
    },
  },
  rowConfig: {
    useKey: true,
    keyField: 'Guid',
  },
  toolbarConfig: {
    enabled: false,
  },
};
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });
async function onSubmit(values: Record<string, any>) {
  const check = await baseFormApi.validate();
  if (check.valid) {
    submitLoading.value = true;
    consumerUpdate(Object.assign({ env: consumerData.value.ENV }, values))
      .then(() => {
        message.success('操作成功');
      })
      .catch(() => {})
      .finally(() => {
        setTimeout(() => {
          submitLoading.value = false;
        }, 500);
      });
  } else {
    message.info('必填项未填写完整，请检查！');
  }
}

function hasEditStatus(row: any) {
  return gridApi.grid?.isEditByRow(row);
}

function editRowEvent(row: any) {
  gridApi.grid?.setEditRow(row);
}

async function saveRowEvent(row: any) {
  const errMap = await gridApi.grid?.validate();
  if (errMap) {
    return;
  }
  gridApi.setLoading(true);
  topicUpdate(Object.assign({ id: row.Guid }, row))
    .then(() => {
      gridApi.grid?.clearEdit();
      handleSuccess();
      message.success('操作成功');
    })
    .catch(() => {})
    .finally(() => {
      gridApi.setLoading(false);
    });
}

const cancelRowEvent = (row: any) => {
  if (row.Guid === 'New') {
    gridApi.grid?.remove(row);
  } else {
    gridApi.grid?.clearEdit();
    gridApi.grid?.revertData(row);
  }
};
function handleSuccess() {
  gridApi.grid.commitProxy('query');
}
async function handleAddTopic() {
  if (gridApi.grid && !gridApi.grid.getEditRecord()) {
    const { row: newRow } = await gridApi.grid.insertAt(
      { Guid: 'New', KeyNode: [] },
      -1,
    );
    gridApi.grid?.setEditRow(newRow);
  }
}
function showOptModal() {
  Modal.confirm({
    onCancel() {
      isRunning.value = !isRunning.value;
    },
    onOk() {
      handleStatusChange();
    },
    title: `${isRunning.value ? 'Run KafkaConsumer?' : 'Close KafkaConsumer?'}`,
  });
}
function handleStatusChange() {
  consumerStatusChange(isRunning.value)
    .then(() => {
      message.success(`The operation was successful.`);
    })
    .catch(() => {})
    .finally(() => {
      fetch();
    });
}
function showDeleteModal(row: any) {
  Modal.confirm({
    content: `请确认是否删除？`,
    onCancel() {},
    onOk() {
      handleDelete(row.Guid);
    },
    title: `Topic:${row.TopicName}`,
  });
}
function handleDelete(id: string) {
  topicDelete(id)
    .then(() => {
      message.success('操作成功');
      handleSuccess();
    })
    .catch(() => {})
    .finally(() => {});
}
async function fetch() {
  consumerData.value = await getConsumerData();
  isRunning.value = consumerData.value.IsRunning;
  baseFormApi.setValues(consumerData.value);
}
onMounted(() => {
  fetch();
});
</script>

<template>
  <Page>
    <Card>
      <template #title>
        <div class="flex flex-wrap">
          <span class="mr-2 hidden md:block">Consumer Configurations</span>
          <Switch
            v-model:checked="isRunning"
            checked-children="Running"
            un-checked-children="Closed"
            @change="showOptModal()"
          />
        </div>
      </template>
      <template #extra>
        <a-button
          :loading="submitLoading"
          type="primary"
          @click="baseFormApi.submitForm()"
        >
          Save
        </a-button>
      </template>

      <Form />
    </Card>
    <Card>
      <template #title>Topic List </template>
      <template #extra>
        <a-button type="primary" @click="handleAddTopic()">
          New Topic
        </a-button>
      </template>
      <Grid>
        <template #apiname_edit="{ row }">
          <a-select
            v-model:value="row.APIID"
            :options="apiOptions"
            allow-clear
          />
        </template>
        <template #action="{ row }">
          <template v-if="hasEditStatus(row)">
            <a-button type="link" @click="saveRowEvent(row)">Save</a-button>
            <a-button type="link" @click="cancelRowEvent(row)">Cancel</a-button>
          </template>
          <template v-else>
            <a-button type="link" @click="editRowEvent(row)">Edit</a-button>
            <a-button danger type="link" @click="showDeleteModal(row)">
              Delete
            </a-button>
          </template>
        </template>
      </Grid>
    </Card>
    <!-- <TopicModal /> -->
  </Page>
</template>
