<script setup lang="ts">
import type { VxeGridListeners, VxeGridProps } from '#/adapter';

import { onMounted, reactive } from 'vue';
import { useRouter } from 'vue-router';

import { useAccess } from '@vben/access';
import { Page, useVbenModal } from '@vben/common-ui';
import { AntClear, AntDelete } from '@vben/icons';

import { message, Modal, Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import {
  getStockTakingList,
  stockTakingDelete,
} from '#/api/bondedwarehouse/stock';
import { getOptions } from '#/api/common';

import createModal from './createModal.vue';

const { hasAccessByCodes } = useAccess();
const router = useRouter();
const [CreateModal, createModalApi] = useVbenModal({
  connectedComponent: createModal,
});
const searchField: any = reactive({
  TakingNo: undefined,
});
const gridOptions = reactive<VxeGridProps>({
  id: 'BWStockTaking',
  columns: [
    {
      fixed: 'left',
      title: '#',
      type: 'seq',
      width: 60,
    },
    {
      field: 'TakingNo',
      title: '盘点作业单',
      minWidth: 150,
      fixed: 'left',
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'IsClosed',
      title: '审核状态',
      width: 88,
      filters: [
        { label: '已审核', value: true },
        { label: '未审核', value: false },
      ],
      filterMultiple: false,
      slots: { default: 'isClosed' },
    },
    {
      field: 'OwnerShortName',
      title: '货主',
      minWidth: 160,
      filters: [],
    },
    {
      field: 'WarehouseName',
      title: '仓库',
      minWidth: 120,
      filters: [],
    },
    {
      field: 'PreTakingDate',
      title: '计划盘点日期',
      width: 120,
      formatter: 'formatDate',
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'IsBright',
      title: '是否明盘',
      width: 100,
      filters: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      filterMultiple: false,
      formatter({ cellValue }) {
        return cellValue ? '是' : '否';
      },
    },
    {
      field: 'TakingStatus',
      title: '初盘状态',
      minWidth: 88,
      filters: [
        { label: '已完成', value: 1 },
        { label: '未完成', value: 0 },
      ],
      filterMultiple: false,
      slots: { default: 'takingStatus' },
    },
    {
      field: 'ReTakingStatus',
      title: '复盘状态',
      minWidth: 88,
      filters: [
        { label: '已完成', value: 1 },
        { label: '未完成', value: 0 },
      ],
      filterMultiple: false,
      slots: { default: 'reTakingStatus' },
    },
    {
      field: 'CreateBy',
      title: '创建人',
      width: 88,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      visible: false,
    },
    {
      field: 'CreateDate',
      title: '创建日期',
      minWidth: 150,
      formatter: 'formatDateTime',
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'CloseBy',
      title: '审核人',
      minWidth: 88,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      visible: false,
    },
    {
      field: 'CloseDate',
      title: '审核日期',
      minWidth: 150,
      formatter: 'formatDateTime',
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'Remark',
      title: '备注',
      minWidth: 240,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'Delete',
      align: 'center',
      slots: { default: 'action' },
      title: '删除',
      width: 60,
    },
  ],
  filterConfig: {
    remote: true,
  },
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: ({ filters, page, sorts }) => {
        const queryParams: any = Object.assign({}, page);
        // 处理排序
        if (sorts.length === 0) {
          queryParams.sort = '_Identify desc';
        } else {
          const sortItem = sorts.map((item) => {
            const newItem = `${item.field} ${item.order}`;
            return newItem;
          });
          queryParams.sort = sortItem.join(',');
        }
        // 处理筛选
        Object.getOwnPropertyNames(searchField).forEach((key) => {
          searchField[key] = undefined;
        });
        filters.forEach(({ field, values }) => {
          queryParams[field] = values.join(',');
          if (Object.prototype.hasOwnProperty.call(searchField, field)) {
            const jsonObject = JSON.parse(values.toString());
            searchField[field] = jsonObject.text;
          }
        });
        return getStockTakingList(queryParams);
      },
    },
    seq: true,
  },
  size: 'mini',
  sortConfig: {
    remote: true,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
});

const gridEvents: VxeGridListeners = {
  cellDblclick({ row }) {
    if (hasAccessByCodes(['BWStockTaking:Detail'])) {
      router.push({
        name: 'BWStockTakingDetail',
        params: { id: row.Guid },
      });
    }
  },
};
const [Grid, gridApi] = useVbenVxeGrid({ gridEvents, gridOptions });

function searchEvent(field: string) {
  const column = gridApi.grid.getColumnByField(field);
  if (column) {
    // 修改第一个选项为勾选状态
    const option = column.filters[0];
    if (!option) {
      return;
    }

    if (searchField[field]) {
      option.data = { type: '1', text: searchField[field] };
      option.value = JSON.stringify(option.data);
      option.checked = true;
    } else {
      option.data = { type: '0', text: undefined };
      option.checked = false;
    }

    // 修改条件之后，需要手动调用 updateData 处理表格数据
    gridApi.grid.updateData();
    gridApi.grid.commitProxy('query');
  }
}
function openCreateModal() {
  createModalApi.open();
}
function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
  gridApi.grid.commitProxy('query');
}
function handleSuccess() {
  gridApi.grid.commitProxy('query');
}
function handleDelete(id: string) {
  stockTakingDelete(id)
    .then(() => {
      message.success('操作成功');
      handleSuccess();
    })
    .catch(() => {})
    .finally(() => {});
}

function showDeleteModal(id: string, name: string) {
  Modal.confirm({
    content: `请确认是否删除？`,
    /* icon: createVNode(ExclamationCircleOutlined), */
    onCancel() {},
    onOk() {
      handleDelete(id);
    },
    title: `盘点作业单号: ${name}`,
    okButtonProps: { danger: true },
  });
}
async function fetch() {
  const options = await getOptions('Owner&Warehouse');
  const fieldList = ['OwnerShortName', 'WarehouseName'];
  fieldList.forEach((field) => {
    gridApi.grid.setFilter(
      field,
      options.filter((item: any) => {
        return item.type === field;
      }),
    );
  });
}
onMounted(() => {
  setTimeout(() => {
    fetch();
  }, 300);
});
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar_left>
        <a-button class="mr-1" @click="clearFilterAndSort()">
          <AntClear class="size-5" />
        </a-button>
        <div class="hidden pl-1 md:block">
          <a-input-search
            v-model:value="searchField.TakingNo"
            allow-clear
            class="mr-2 w-60"
            placeholder="盘点作业单号"
            @search="searchEvent('TakingNo')"
          />
        </div>
      </template>
      <template #takingStatus="{ row }">
        <Tag :color="row.TakingStatus === 1 ? 'green' : 'blue'">
          {{ row.TakingStatus === 1 ? '已完成' : '未完成' }}
        </Tag>
      </template>
      <template #reTakingStatus="{ row }">
        <Tag :color="row.ReTakingStatus === 1 ? 'green' : 'blue'">
          {{ row.ReTakingStatus === 1 ? '已完成' : '未完成' }}
        </Tag>
      </template>
      <template #isClosed="{ row }">
        <Tag :color="row.IsClosed ? 'green' : 'red'">
          {{ row.IsClosed ? '已审核' : '未审核' }}
        </Tag>
      </template>
      <template #toolbar-tools>
        <a-button
          v-access:code="'BWStockTaking:Create'"
          class="mr-3"
          type="primary"
          @click="openCreateModal()"
        >
          创建盘点作业单
        </a-button>
      </template>
      <template #action="{ row }">
        <a-button
          :disabled="!hasAccessByCodes(['BWStockTaking:Delete'])"
          danger
          size="small"
          type="link"
          @click="showDeleteModal(row.Guid, row.TakingNo)"
        >
          <AntDelete class="size-5" />
        </a-button>
      </template>
    </Grid>
    <CreateModal @success="handleSuccess" />
  </Page>
</template>
