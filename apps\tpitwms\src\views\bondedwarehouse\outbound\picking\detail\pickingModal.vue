<script lang="ts" setup>
import type { VxeGridProps } from '#/adapter';

import { reactive, ref } from 'vue';

import { /* useVbenDrawer, */ useVbenModal } from '@vben/common-ui';

import {
  Col,
  Descriptions,
  InputNumber,
  message,
  Modal,
  Row,
  Tag,
} from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import {
  bwOutPickingItemAdd,
  bwOutPickingItemRemove,
  bwOutPickingOutboundUnBinding,
  getOutPickingOPList,
} from '#/api/bondedwarehouse/outbound';

const emit = defineEmits(['success']);

const data = ref();
const isUpdate = ref(false);
const boundOutbound = ref('');
const palletTypeOption = ref([]);
const packTypeOption = ref([]);
const pickingData = ref([]) as any;
const handlingUnit: any = reactive({
  palletType: '',
  packagingMaterial: '',
});

const [TrayModal, trayModalApi] = useVbenModal({
  class: 'w-[1080px]',
  fullscreenButton: false,
  title: '创建出库托盘',
  onConfirm: async () => {
    await handleCreateTray();
  },
});

const [BaseModal, modalApi] = useVbenModal({
  class: 'w-full h-full',
  destroyOnClose: true,
  fullscreenButton: true,
  closeOnClickModal: false,
  fullscreen: true,
  zIndex: 900,
  /* showConfirmButton: false, */
  footer: false,
  /* onCancel() {
    modalApi.close();
  }, */
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      boundOutbound.value = '';
      isUpdate.value = false;
      data.value = modalApi.getData<Record<string, any>>();
      fetch();
    } else {
      emit('success', isUpdate.value);
    }
  },
  title: '出库拣货作业',
});
const packGridOptions = reactive<VxeGridProps>({
  id: 'OutPickingModalPack',
  columns: [
    {
      fixed: 'left',
      title: '#',
      type: 'seq',
      width: 60,
    },
    {
      field: 'PalletNo',
      title: '入库托盘',
      width: 88,
    },
    {
      field: 'MatCode',
      title: '物料编号',
      width: 80,
    },
    {
      field: 'BatchNo',
      title: '批号',
      width: 80,
    },
    { field: 'UnrestrictedQty', title: '非限库存', width: 80 },
    { field: 'MissingRemaining', title: '贴标少', width: 64 },
    { field: 'AvailableQty', title: '可拣(非限-贴标少)', width: 120 },
    { field: 'CIQRemaining', title: '含CIQ', width: 80 },
    { field: 'ShelveRemaining', title: '含搁置', width: 80 },
    { field: 'ShelveReason', title: '搁置原因', minWidth: 100 },
    {
      field: 'PickingQty',
      title: '本次拣货数量',
      /* align: 'left', */
      minWidth: 100,
      editRender: { name: 'AInput', autofocus: '' },
      slots: { edit: 'pickingqty_edit' },
    },
  ],
  editConfig: {
    trigger: 'dblclick',
    mode: 'cell',
    showIcon: true,
    showStatus: true,
  },
  keepSource: true,
  size: 'mini',
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    enabled: false,
  },
  toolbarConfig: {
    enabled: false,
    /* custom: false,
    refresh: false,
    slots: {
      buttons: 'toolbar_left',
    }, */
  },
  menuConfig: {
    trigger: 'cell',
    body: {
      options: [
        [
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuCopy',
            disabled: false,
            name: '复制单元格',
            prefixConfig: {
              icon: 'vxe-icon-copy',
            },
            visible: true,
          },
        ],
      ],
    },
    className: 'font-medium shadow rounded-md',
  },
});
const [PackGrid, packGridApi] = useVbenVxeGrid({
  gridOptions: packGridOptions,
});
const obdGridOptions = reactive<VxeGridProps>({
  id: 'OutPickingModalOBD',
  columns: [
    {
      field: 'DeliveryDocumentItem',
      title: '项号',
      minWidth: 64,
    },
    {
      field: 'Material',
      title: '产品',
      minWidth: 80,
    },
    {
      field: 'DeliveryQuantity',
      title: '指令数量',
      minWidth: 80,
    },
    {
      field: 'PickingQty',
      title: '已拣货',
      minWidth: 80,
    },
  ],
  size: 'mini',
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    enabled: false,
  },
  toolbarConfig: {
    custom: false,
    refresh: false,
    slots: {
      buttons: 'toolbar_left',
    },
  },
  menuConfig: {
    trigger: 'cell',
    body: {
      options: [
        [
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuCopy',
            disabled: false,
            name: '复制单元格',
            prefixConfig: {
              icon: 'vxe-icon-copy',
            },
            visible: true,
          },
        ],
      ],
    },
    className: 'font-medium shadow rounded-md',
  },
});
const [OBDGrid, obdGridApi] = useVbenVxeGrid({ gridOptions: obdGridOptions });
const gridOptions = reactive<VxeGridProps>({
  id: 'OutPickingModalTally',
  checkboxConfig: {
    highlight: true,
    range: true,
    checkMethod: ({ row }: any) => {
      return (
        !(
          row.LockedByMovement ||
          row.LockedByTaking ||
          row.LockedByAPI ||
          row.UnrestrictedQty === 0
        ) &&
        (row.BatchStatus === 0 || row.SAPBatch === '')
      );
    },
  },
  columns: [
    { type: 'checkbox', width: 40 },
    /* {
      field: 'BLNo',
      title: '出库提单',
      minWidth: 160,
      filters: [{ data: null }],
      filterRender: { name: 'FilterInput' },
    }, */
    { field: 'AreaName', title: '库区', width: 100 },
    {
      field: 'PalletNo',
      title: '入库托盘',
      minWidth: 100,
      filters: [{ data: null }],
      filterRender: { name: 'FilterInput' },
    },
    {
      field: 'MatCode',
      title: '物料编号',
      minWidth: 88,
      filters: [{ data: null }],
      filterRender: { name: 'FilterInput' },
    },
    {
      field: 'BatchNo',
      title: '批次',
      minWidth: 88,
      filters: [{ data: null }],
      filterRender: { name: 'FilterInput' },
    },
    {
      field: 'BatchStatus',
      title: '批次状态',
      width: 88,
      slots: { default: 'batchstatus' },
      filters: [
        { label: '正常', value: 0 },
        { label: '限制', value: 1 },
        { label: '不存在', value: -1 },
      ],
    },
    {
      field: 'ActualExpiration',
      title: '实物效期',
      width: 88,
      formatter: 'formatDate',
    },
    { field: 'UnrestrictedQty', title: '非限制库存', width: 100 },
    { field: 'MissingRemaining', title: '贴标少', width: 80 },
    { field: 'CIQRemaining', title: '含CIQ', width: 80 },
    { field: 'ShelveRemaining', title: '含搁置', width: 80 },
    { field: 'ShelveReason', title: '搁置原因', width: 100 },
    {
      field: 'oBrandName',
      title: '品牌',
      minWidth: 88,
    },
    {
      field: 'oCName',
      title: '中文品名',
      minWidth: 240,
      filters: [{ data: null }],
      filterRender: { name: 'FilterInput' },
    },
  ],
  filterConfig: {
    remote: false,
  },
  height: 'auto',
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    enabled: false,
  },
  rowStyle({ row }) {
    return obdGridApi.grid
      .getData()
      .some((obj: any) => obj.Material === row.MatCode)
      ? {
          backgroundColor: '#ebfbee',
          color: '#222',
        }
      : null;
  },
  /* scrollY: { enabled: true, gt: 0 }, */
  size: 'mini',
  sortConfig: {
    remote: false,
  },
  toolbarConfig: {
    custom: false,
    refresh: false,
    slots: {
      buttons: 'toolbar_left',
    },
  },
  menuConfig: {
    trigger: 'cell',
    body: {
      options: [
        [
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuCopy',
            disabled: false,
            name: '复制单元格',
            prefixConfig: {
              icon: 'vxe-icon-copy',
            },
            visible: true,
          },
        ],
      ],
    },
    className: 'font-medium shadow rounded-md',
  },
});
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });
const itemGridOptions = reactive<VxeGridProps>({
  id: 'OutPickingModalItem',
  checkboxConfig: {
    highlight: true,
    range: true,
    checkMethod: ({ row }) => {
      return !row.PackagingStatus;
    },
  },
  columns: [
    { type: 'checkbox', width: 40 },
    {
      field: 'PackagingStatus',
      title: '打包状态',
      minWidth: 88,
      slots: { default: 'packstatus' },
    },
    {
      field: 'NewPalletNo',
      title: '出库托盘号',
      minWidth: 144,
    },
    {
      field: 'NewSSCC',
      title: '出库SSCC',
      minWidth: 144,
    },
    {
      field: 'PalletType',
      title: '托盘规格',
      minWidth: 88,
    },
    {
      field: 'PackagingMaterial',
      title: '包装材料',
      minWidth: 88,
    },
    {
      field: 'GoodsNum',
      title: '产品数量',
      minWidth: 88,
    },
    {
      field: 'MissingQty',
      title: '贴标少',
      minWidth: 72,
    },
    {
      field: 'PickingQty',
      title: '拣货数量',
      minWidth: 88,
    },
    {
      field: 'PickingBy',
      title: '拣货人',
      minWidth: 80,
    },
    {
      field: 'PickingDate',
      title: '拣货日期',
      minWidth: 136,
    },
  ],

  filterConfig: {
    remote: false,
  },
  height: 'auto',
  keepSource: true,
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    enabled: false,
  },
  /* scrollY: { enabled: true, gt: 0 }, */
  size: 'mini',
  sortConfig: {
    remote: false,
  },
  toolbarConfig: {
    custom: false,
    refresh: false,
    slots: {
      buttons: 'toolbar_left',
    },
  },
});

const [ItemGrid, itemGridApi] = useVbenVxeGrid({
  gridOptions: itemGridOptions,
});

function handleAdd() {
  const checkedRecords = gridApi.grid
    .getCheckboxRecords()
    .map((item: any) => item.Guid);
  if (checkedRecords.length === 0) {
    message.info(`请勾选需要创托盘的理货明细`);
    return;
  }
  const dt = obdGridApi.grid.getData();
  let matCheck = true;
  gridApi.grid.getCheckboxRecords().forEach((item) => {
    item.PickingQty = item.AvailableQty;
    if (!dt.some((obj) => obj.Material === item.MatCode)) {
      matCheck = false;
    }
  });
  if (!matCheck) {
    message.info(`未匹配到出库指令，请留意！`);
  }
  pickingData.value = gridApi.grid.getCheckboxRecords();
  packGridOptions.data = pickingData.value;
  packGridApi.setGridOptions(packGridOptions);
  trayModalApi.open();
  /* modalApi.setState({ confirmLoading: true });
  modalApi.setState({ loading: true }); */

  /*  bwOutOrderItemAdd(data.value.id, checkedRecords)
    .then(() => {

    })
    .catch(() => {})
    .finally(() => {
      modalApi.setState({ loading: false });
      modalApi.setState({ confirmLoading: false });
    }); */
}

function checkPickingQty(row: any) {
  if (row.PickingQty > row.AvailableQty) {
    row.PickingQty = row.AvailableQty;
    message.info('拣货数量不能大于可拣数量');
  }
}
function handleCreateTray() {
  let matCheck = true;
  const dt: any = packGridApi.grid.getData();
  const values = [] as any;
  const ids = [] as any;
  dt.forEach((item: any) => {
    if (!item.PickingQty || item.PickingQty === 0) {
      message.error(`请填入拣货数量！`);
      matCheck = false;
      return;
    }
    if (item.PickingQty <= 0) {
      message.error(`拣货数量需大于0！`);
      matCheck = false;
      return;
    }
    ids.push(item.Guid);
    values.push({ Guid: item.Guid, PickingQty: item.PickingQty });
  });
  if (!matCheck) {
    return;
  }
  trayModalApi.setState({ confirmLoading: true });
  trayModalApi.setState({ loading: true });
  bwOutPickingItemAdd(data.value.id, ids, values, handlingUnit)
    .then(() => {
      fetch();
      message.success('操作成功');
      trayModalApi.close();
    })
    .catch(() => {})
    .finally(() => {
      trayModalApi.setState({ loading: false });
      trayModalApi.setState({ confirmLoading: false });
    });
}
function showRemoveModal() {
  const checkedRecords = itemGridApi.grid
    .getCheckboxRecords()
    .map((item: any) => item.NewPalletID);
  if (checkedRecords.length === 0) {
    message.info(`请勾选需要撤销的拣货明细`);
    return;
  }
  Modal.confirm({
    content: `是否撤销勾选托盘的拣货操作？`,
    /* icon: createVNode(ExclamationCircleOutlined), */
    onCancel() {},
    onOk() {
      handleRemove(checkedRecords);
    },
    title: `操作确认`,
    okButtonProps: { danger: true },
  });
}
function handleRemove(checkedRecords: Array<string>) {
  modalApi.setState({ loading: true });
  bwOutPickingItemRemove(data.value.id, checkedRecords)
    .then(() => {
      fetch();
      message.success('操作成功');
    })
    .catch(() => {})
    .finally(() => {
      modalApi.setState({ loading: false });
    });
}

function showUnboundModal(obd: string) {
  Modal.confirm({
    content: `请确认是否解除绑定？`,
    /* icon: createVNode(ExclamationCircleOutlined), */
    onCancel() {},
    onOk() {
      handleUnBinding();
    },
    title: `Outbound: ${obd}`,
    okButtonProps: { danger: true },
  });
}

function handleUnBinding() {
  modalApi.setState({ loading: true });
  bwOutPickingOutboundUnBinding(data.value.id)
    .then(() => {
      isUpdate.value = true;
      modalApi.close();
      message.success('操作成功');
    })
    .catch(() => {
      modalApi.setState({ loading: false });
    })
    .finally(() => {});
}
async function fetch() {
  modalApi.setState({ loading: true });
  getOutPickingOPList(data.value.id)
    .then((res) => {
      boundOutbound.value = res.bound;
      obdGridOptions.data = res.obd;
      gridOptions.data = res.tally;
      itemGridOptions.data = res.picking;
      obdGridApi.setGridOptions(obdGridOptions);
      gridApi.setGridOptions(gridOptions);
      itemGridApi.setGridOptions(itemGridOptions);
      palletTypeOption.value = res.pallet;
      packTypeOption.value = res.pack;
      palletTypeOption.value.forEach((item: any) => {
        if (item?.d) {
          handlingUnit.palletType = item.value;
        }
      });
      packTypeOption.value.forEach((item: any) => {
        if (item?.d) {
          handlingUnit.packagingMaterial = item.value;
        }
      });
    })
    .catch(() => {})
    .finally(() => {
      modalApi.setState({ loading: false });
    });
}
</script>
<template>
  <BaseModal>
    <TrayModal>
      <Descriptions
        size="small"
        layout="vertical"
        bordered
        :column="2"
        class="p-2"
      >
        <Descriptions.Item label="包装材料">
          <a-select
            style="width: 100%"
            v-model:value="handlingUnit.packagingMaterial"
            :options="packTypeOption"
          />
        </Descriptions.Item>
        <Descriptions.Item label="托盘规格">
          <a-select
            style="width: 100%"
            v-model:value="handlingUnit.palletType"
            :options="palletTypeOption"
          />
        </Descriptions.Item>
      </Descriptions>
      <PackGrid>
        <template #pickingqty_edit="{ row }">
          <InputNumber
            v-model:value="row.PickingQty"
            :min="1"
            @change="checkPickingQty(row)"
          />
        </template>
      </PackGrid>
    </TrayModal>
    <Row :gutter="12" class="h-full">
      <Col :span="6" class="h-full">
        <OBDGrid>
          <template #toolbar_left>
            <div class="hidden pl-1 text-base font-medium md:block">
              <span class="mr-2">{{ `出库指令 ${boundOutbound}` }}</span>
            </div>
          </template>
          <template #toolbar-tools>
            <a-button
              v-if="boundOutbound"
              size="small"
              danger
              @click="showUnboundModal(boundOutbound)"
            >
              取消绑定
            </a-button>
          </template>
        </OBDGrid>
      </Col>
      <Col :span="18" class="h-full">
        <Row :gutter="12" class="h-1/2">
          <Col :span="24" class="h-full">
            <Grid>
              <template #toolbar_left>
                <div class="hidden pl-1 text-base font-medium md:block">
                  <span class="mr-2">出库理货明细</span>
                </div>
              </template>
              <template #toolbar-tools>
                <a-button size="small" type="primary" @click="handleAdd()">
                  创建出库托盘
                </a-button>
              </template>
              <!-- <template #batchmanage="{ row }">
                <Tag :color="row.isBatchManagementRequired ? 'green' : 'red'">
                  {{ row.isBatchManagementRequired ? '是' : '否' }}
                </Tag>
              </template> -->
              <template #batchstatus="{ row }">
                <Tag
                  :color="
                    row.BatchStatus === 0
                      ? 'green'
                      : row.BatchStatus === 1
                        ? 'orange'
                        : 'red'
                  "
                >
                  {{
                    row.BatchStatus === 0
                      ? '正常'
                      : row.BatchStatus === 1
                        ? '限制'
                        : '不存在'
                  }}
                </Tag>
              </template>
            </Grid>
          </Col>
        </Row>
        <Row :gutter="12" class="h-1/2">
          <Col :span="24" class="h-full">
            <ItemGrid>
              <template #toolbar_left>
                <div class="hidden pl-1 text-base font-medium md:block">
                  <span class="mr-2">已拣货明细</span>
                </div>
              </template>
              <template #toolbar-tools>
                <a-button
                  size="small"
                  danger
                  type="primary"
                  @click="showRemoveModal()"
                >
                  撤销拣货
                </a-button>
              </template>
              <template #packstatus="{ row }">
                <Tag :color="row.PackagingStatus ? 'green' : 'red'">
                  {{ row.PackagingStatus ? '已打包' : '待打包' }}
                </Tag>
              </template>
            </ItemGrid>
          </Col>
        </Row>
      </Col>
    </Row>
  </BaseModal>
</template>
