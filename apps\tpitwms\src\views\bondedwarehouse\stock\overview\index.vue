<script setup lang="ts">
import type { VxeGridListeners, VxeGridProps } from '#/adapter';

import { onMounted, reactive, ref } from 'vue';

import { Page, useVbenDrawer } from '@vben/common-ui';
import { AntClear } from '@vben/icons';
import { downloadFileFromBlob } from '@vben/utils';

import { DatePicker, message, Modal, Tag } from 'ant-design-vue';
import dayjs, { Dayjs } from 'dayjs';

import { useVbenVxeGrid } from '#/adapter';
import {
  getStockOverviewList,
  stockOverviewSnapshot,
} from '#/api/bondedwarehouse/stock';
import { addExportRecord, exportFile, getOptions } from '#/api/common';
import batchDrawer from '#/views/sapneo/master/batch/batchDrawer.vue';
import skuDrawer from '#/views/sapneo/master/product/skuDrawer.vue';

const balanceDate = ref<Dayjs>();
const [SkuDrawer, skuDrawerApi] = useVbenDrawer({
  connectedComponent: skuDrawer,
});
const [BatchDrawer, batchDrawerApi] = useVbenDrawer({
  connectedComponent: batchDrawer,
});
function openSkuDrawer(id: string) {
  skuDrawerApi.setData({ id });
  skuDrawerApi.open();
}
function openBatchDrawer(sku: string, batch: string) {
  batchDrawerApi.setData({ sku, batch });
  batchDrawerApi.open();
}
const searchField: any = reactive({
  MatCode: undefined,
});
const inventory = ref(0);
const loadingRef = ref(false);
const gridQueryParams = ref({}) as any;
const gridOptions = reactive<VxeGridProps>({
  id: 'BWStockOverview',
  columns: [
    {
      title: '#',
      type: 'seq',
      width: 60,
      fixed: 'left',
    },
    {
      field: 'OwnerShortName',
      title: '货主',
      width: 120,
      filters: [],
    },
    { field: 'Plant', title: '工厂编码', width: 100 },
    {
      field: 'StorageLocation',
      title: 'SAP位置',
      width: 112,
      filters: [],
    },
    {
      field: 'StockType',
      title: '库存类型',
      width: 88,
      filters: [
        { label: '非限制', value: 'UNR' },
        { label: '冻结', value: 'BLK' },
      ],
      filterMultiple: false,
      slots: { default: 'stocktype' },
    },
    {
      field: 'MatCode',
      title: '物料编号',
      width: 100,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      slots: { default: 'material' },
    },
    {
      field: 'MaterialType',
      title: '物料类型',
      width: 100,
      filters: [],
    },
    {
      field: 'isBatchManagementRequired',
      title: '批次管理',
      width: 88,
      filters: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      filterMultiple: false,
      slots: { default: 'batchmanage' },
    },
    {
      field: 'SAPBatch',
      title: 'SAP批次',
      width: 88,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      slots: { default: 'batch' },
    },
    {
      field: 'BatchStatus',
      title: '批次状态',
      width: 88,
      slots: { default: 'batchstatus' },
      filters: [
        { label: '正常', value: 0 },
        { label: '限制', value: 1 },
        { label: '不存在', value: -1 },
      ],
    },
    {
      field: 'Inventory',
      title: '库存数量',
      width: 128,
      filters: [{ data: { type: '0', num1: undefined, num2: undefined } }],
      filterRender: { name: 'FilterNumberRange' },
    },
    {
      field: 'logisticDescription',
      title: '中文描述',
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      minWidth: 200,
    },
    {
      field: 'rootDescription',
      title: '英文描述',
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      minWidth: 200,
    },
    {
      field: 'BalanceDate',
      title: '快照日期',
      width: 88,
      /* formatter: 'formatterDate', */
    },
  ],
  filterConfig: {
    remote: true,
  },
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: ({ filters, page }) => {
        const queryParams: any = Object.assign(
          { BalanceDate: balanceDate.value?.format('YYYY-MM-DD') },
          page,
        );
        // 处理筛选
        Object.getOwnPropertyNames(searchField).forEach((key) => {
          searchField[key] = undefined;
        });
        filters.forEach(({ field, values }) => {
          queryParams[field] = values.join(',');
          if (Object.prototype.hasOwnProperty.call(searchField, field)) {
            const jsonObject = JSON.parse(values.toString());
            searchField[field] = jsonObject.text;
          }
        });
        gridQueryParams.value = queryParams;
        return getStockOverviewList(queryParams)
          .then((res) => {
            inventory.value = res.Inventory;
            return res;
          })
          .catch(() => {})
          .finally(() => {});
      },
    },
    seq: true,
  },
  scrollX: { enabled: true, gt: 0 },
  scrollY: { enabled: true, gt: 0 },
  showFooter: true,
  size: 'mini',
  sortConfig: {
    remote: true,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
  footerRowStyle: { 'font-weight': 'bold ', backgroundColor: '#ebfbee' },
  footerMethod({ columns }) {
    return [
      columns.map((column, columnIndex) => {
        if (columnIndex === 0) {
          return `合计`;
        }
        if (['Inventory'].includes(column.field)) {
          return inventory.value;
        }

        return null;
      }),
    ];
  },
  menuConfig: {
    trigger: 'cell',
    body: {
      options: [
        [
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuCopy',
            disabled: false,
            name: '复制单元格',
            prefixConfig: {
              icon: 'vxe-icon-copy',
            },
            visible: true,
          },
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuRefresh',
            disabled: false,
            name: '刷新',
            prefixConfig: { icon: 'vxe-icon-refresh' },
            visible: true,
          },
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuExportWMS',
            disabled: false,
            name: '导出xlsx',
            prefixConfig: { icon: 'vxe-icon-download' },
            visible: true,
          },
        ],
      ],
    },
    className: 'font-medium shadow rounded-md',
  },
});
const gridEvents: VxeGridListeners = {
  menuClick({ menu }) {
    switch (menu.code) {
      /* case 'menuExportSAP': {
        handleExport('SAP');
        break;
      } */
      case 'menuExportWMS': {
        handleExport('WMS');
        break;
      }
      default: {
        break;
      }
    }
  },
};
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions, gridEvents });
function searchEvent(field: string) {
  const column = gridApi.grid.getColumnByField(field);
  if (column) {
    // 修改第一个选项为勾选状态
    const option = column.filters[0];
    if (!option) {
      return;
    }

    if (searchField[field]) {
      option.data = { type: '1', text: searchField[field] };
      option.value = JSON.stringify(option.data);
      option.checked = true;
    } else {
      option.data = { type: '0', text: undefined };
      option.checked = false;
    }

    // 修改条件之后，需要手动调用 updateData 处理表格数据
    gridApi.grid.updateData();
    gridApi.grid.commitProxy('query');
  }
}
function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
  gridApi.grid.commitProxy('query');
}
function handleSearch() {
  if (!balanceDate.value) {
    balanceDate.value = dayjs();
  }
  gridApi.grid.commitProxy('query');
}
function handleExport(expTyp: string) {
  addExportRecord({
    typ: `StockOverView${expTyp}`,
    params: gridQueryParams.value,
    columns: gridApi.grid
      .getColumns()
      .filter((item) => item.field && item.title)
      .map((item) => ({
        field: item.field,
        title: item.title,
      })),
  })
    .then((res) => {
      loadingRef.value = true;
      exportFile(res)
        .then((res) => {
          downloadFileFromBlob({
            source: res.data,
            fileName: `库存快照_${expTyp}_${balanceDate.value?.format('YYYY-MM-DD')}.xlsx`,
          });
        })
        .catch(() => {})
        .finally(() => {
          loadingRef.value = false;
        });
    })
    .catch(() => {})
    .finally(() => {});
}
function handleUpload() {
  stockOverviewSnapshot()
    .then(() => {
      message.success('回传任务创建成功，预计将在1分钟后完成数据回传');
    })
    .catch(() => {})
    .finally(() => {});
}

function showUploadModal() {
  Modal.confirm({
    content: `请确认上传最新日期的库存快照？`,
    onCancel() {},
    onOk() {
      handleUpload();
    },
    title: `操作确认`,
    okButtonProps: { danger: true },
  });
}
async function fetch() {
  const options = await getOptions('StockOverView');
  const fieldList = ['StorageLocation', 'MaterialType', 'OwnerShortName'];
  fieldList.forEach((field) => {
    gridApi.grid.setFilter(
      field,
      options.filter((item: any) => {
        return item.type === field;
      }),
    );
  });
}
onMounted(() => {
  balanceDate.value = dayjs();
  setTimeout(() => {
    fetch();
  }, 300);
});
</script>

<template>
  <Page auto-content-height v-loading="loadingRef">
    <Grid>
      <template #toolbar_left>
        <a-button class="mr-1" @click="clearFilterAndSort()">
          <AntClear class="size-5" />
        </a-button>
        <div class="hidden pl-1 md:block">
          <DatePicker
            v-model:value="balanceDate"
            class="mr-2 w-48"
            allow-clear
            placeholder="快照日期"
            @change="handleSearch"
          />
          <a-input-search
            v-model:value="searchField.MatCode"
            allow-clear
            class="mr-2 w-48"
            placeholder="物料编号"
            @search="searchEvent('MatCode')"
          />
        </div>
      </template>
      <template #toolbar-tools>
        <a-button
          v-access:code="'StockOverview:Upload'"
          type="dashed"
          danger
          @click="showUploadModal"
        >
          手动上传
        </a-button>
      </template>
      <template #material="{ row }">
        <a class="text-blue-500" @click="openSkuDrawer(row.MatCode)">
          {{ row.MatCode }}
        </a>
      </template>
      <template #batch="{ row }">
        <a
          class="text-blue-500"
          @click="openBatchDrawer(row.MatCode, row.SAPBatch)"
        >
          {{ row.SAPBatch }}
        </a>
      </template>
      <template #batchmanage="{ row }">
        <Tag :color="row.isBatchManagementRequired ? 'green' : 'red'">
          {{ row.isBatchManagementRequired ? '是' : '否' }}
        </Tag>
      </template>
      <template #batchstatus="{ row }">
        <Tag
          :color="
            row.BatchStatus === 0
              ? 'green'
              : row.BatchStatus === 1
                ? 'orange'
                : 'red'
          "
        >
          {{
            row.BatchStatus === 0
              ? '正常'
              : row.BatchStatus === 1
                ? '限制'
                : '不存在'
          }}
        </Tag>
      </template>
      <template #stocktype="{ row }">
        <Tag :color="row.StockType === 'UNR' ? 'green' : 'red'">
          {{ row.StockType === 'UNR' ? '非限制' : '冻结' }}
        </Tag>
      </template>
    </Grid>
    <SkuDrawer />
    <BatchDrawer />
  </Page>
</template>
