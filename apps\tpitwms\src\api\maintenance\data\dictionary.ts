import { requestClient, sleep } from '#/api/request';

/**
 * 系统维护 选项信息相关API
 */
export async function getDictionaryList(obj: object) {
  await sleep(100);
  return requestClient.get('/maintenance/data/dictionary/getList', {
    params: obj,
  });
}
export async function dictionaryDelete(id: string) {
  await sleep(100);
  return requestClient.post('/maintenance/data/dictionary/del', { id });
}

export async function getDictionaryData(id: string) {
  await sleep(100);
  return requestClient.get('/maintenance/data/dictionary/getData', {
    params: { id },
  });
}

export async function dictionaryUpdate(params: object) {
  await sleep(100);
  return requestClient.post('/maintenance/data/dictionary/update', params);
}
