import { requestClient, sleep } from '#/api/request';

/**
 * LIS 发票关系相关接口
 */
export async function getTaskList(obj: object) {
  await sleep(100);
  return requestClient.get('/lis/hbl_invoice/task/getList', { params: obj });
}

export async function getTaskData(id: string) {
  return requestClient.get('/lis/hbl_invoice/task/getData', {
    params: { id },
  });
}

export async function getTaskListByHBL(hbl: string) {
  return requestClient.get('/lis/hbl_invoice/task/getListByHBL', {
    params: { hbl },
  });
}

export async function getTaskRecord(id: string) {
  return requestClient.get('/lis/hbl_invoice/task/getRecord', {
    params: { id },
  });
}

export async function bwInOrderLISInvoiceReBuild(id: string) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/inbound/order/lisinvoice/rebuild',
    { id },
  );
}
