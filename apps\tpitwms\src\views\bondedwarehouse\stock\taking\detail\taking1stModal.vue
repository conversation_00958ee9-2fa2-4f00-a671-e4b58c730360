<script lang="ts" setup>
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Descriptions, InputNumber, message, Modal } from 'ant-design-vue';

import { useVbenForm } from '#/adapter';
import { stockTakingItem1stUpdate } from '#/api/bondedwarehouse/stock';

const emit = defineEmits(['success']);

const data = ref();
const itemData = ref({}) as any;
const submitData = ref({}) as any;

const [Form, formApi] = useVbenForm({
  handleSubmit: onSubmit,
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
    labelClass: 'w-24',
  },
  schema: [
    {
      component: 'InputNumber',
      componentProps: {
        autocomplete: 'off',
        min: 0,
      },
      fieldName: 'TakingQty',
      label: '盘点库存',
      rules: 'required',
    },
  ],
  showDefaultActions: false,
  wrapperClass: 'grid-cols-1',
});

const [BaseModal, modalApi] = useVbenModal({
  class: 'w-[640px]',
  draggable: true,
  fullscreenButton: false,
  closeOnClickModal: false,
  destroyOnClose: true,
  zIndex: 900,
  confirmText: '确认',
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await formApi.submitForm();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      /* modalApi.setState({ showConfirmButton: false }); */
      submitData.value = {};
      data.value = modalApi.getData<Record<string, any>>();
      itemData.value = data.value.row;
      formApi.resetForm();
      fetch();
    }
  },
  title: '初次盘点',
});

async function onSubmit(values: Record<string, any>) {
  const check = await formApi.validate();
  if (check.valid) {
    submitData.value = values;
    handleSubmit();
  }
}
function handleSubmit() {
  Modal.destroyAll();
  modalApi.setState({ confirmLoading: true });
  modalApi.setState({ loading: true });
  stockTakingItem1stUpdate(
    Object.assign({ id: itemData.value.Guid }, submitData.value),
  )
    .then(() => {
      message.success('操作成功');
      handleClose();
    })
    .catch(() => {})
    .finally(() => {
      modalApi.setState({ loading: false });
      modalApi.setState({ confirmLoading: false });
    });
}

function handleClose() {
  modalApi.close();
  emit('success');
}
async function fetch() {
  /* await bwInOrderItemGetData(data.value.id)
    .then((res) => {
      modalApi.setState({ showConfirmButton: true });
      itemData.value = res;
    })
    .catch(() => {})
    .finally(() => {}); */
}
</script>
<template>
  <BaseModal>
    <Descriptions :column="{ xs: 1, sm: 2 }" bordered size="small">
      <Descriptions.Item label="库区">
        {{ itemData.AreaName }}
      </Descriptions.Item>
      <Descriptions.Item label="货位">
        {{ itemData.BIN }}
      </Descriptions.Item>
      <Descriptions.Item label="HBL">{{ itemData.HBL }}</Descriptions.Item>
      <Descriptions.Item label="托盘号">
        {{ itemData.PalletNo }}
      </Descriptions.Item>
      <Descriptions.Item label="物料编号">
        {{ itemData.MatCode }}
      </Descriptions.Item>
      <Descriptions.Item label="批号">{{ itemData.BatchNo }}</Descriptions.Item>
      <Descriptions.Item label="库存状态">
        {{ itemData.StockStatus }}
      </Descriptions.Item>
      <Descriptions.Item label="账面库存" v-if="data.isBright">
        {{ itemData.StockQty }}
      </Descriptions.Item>
    </Descriptions>
    <Form class="mt-4">
      <template #TakingQty="slotProps">
        <InputNumber v-bind="slotProps">
          <template #addonAfter>
            <span v-if="data.isBright && slotProps.value">{{
              `差异：${Number(slotProps.value) - Number(itemData.StockQty)}`
            }}</span>
          </template>
        </InputNumber>
      </template>
    </Form>
  </BaseModal>
</template>
