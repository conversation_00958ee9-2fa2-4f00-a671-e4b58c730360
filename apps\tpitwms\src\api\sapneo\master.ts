import { requestClient, sleep } from '#/api/request';

/**
 * SAP NEO 主数据相关API
 */
export async function getProductList(obj: object) {
  await sleep(100);
  return requestClient.get('/sapneo/master/product/getList', { params: obj });
}

export async function getProductData(id: string) {
  await sleep(100);
  return requestClient.get('/sapneo/master/product/getData', {
    params: { id },
  });
}

export async function getBatchList(obj: object) {
  await sleep(100);
  return requestClient.get('/sapneo/master/batch/getList', { params: obj });
}

export async function getBatchMappingList(obj: object) {
  await sleep(100);
  return requestClient.get('/sapneo/master/batch/mapping/getList', {
    params: obj,
  });
}

export async function getBatchData(sku: string, batch: string) {
  await sleep(100);
  return requestClient.get('/sapneo/master/batch/getData', {
    params: { sku, batch },
  });
}

export async function getCustomerList(obj: object) {
  await sleep(100);
  return requestClient.get('/sapneo/master/customer/getList', { params: obj });
}

export async function getCustomerData(id: string) {
  await sleep(100);
  return requestClient.get('/sapneo/master/customer/getData', {
    params: { id },
  });
}

export async function getBatchStatus(sku: string, batch: string) {
  await sleep(100);
  return requestClient.get('/sapneo/master/batch/getStatus', {
    params: { sku, batch },
  });
}
