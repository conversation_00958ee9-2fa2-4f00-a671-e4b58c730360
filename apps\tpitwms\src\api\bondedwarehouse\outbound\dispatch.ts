import { requestClient, sleep } from '#/api/request';

/**
 * 装车发货 相关API
 */

export async function bwOutDispatchGetDoc() {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/outbound/dispatch/getDoc');
}

export async function bwOutDispatchCreate(params: object) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/outbound/dispatch/create',
    params,
  );
}

export async function bwOutDispatchDelete(id: string) {
  await sleep(100);
  return requestClient.post('/bondedwarehouse/outbound/dispatch/del', { id });
}

export async function getOutDispatchList(obj: object) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/outbound/dispatch/getList', {
    params: obj,
  });
}

export async function getOutDispatchHead(id: string) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/outbound/dispatch/head/getData', {
    params: { id },
  });
}

export async function getOutDispatchItem(id: string) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/outbound/dispatch/item/getList', {
    params: { id },
  });
}

export async function bwOutDispatchHeadUpdate(params: object) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/outbound/dispatch/head/update',
    params,
  );
}

export async function getOutDispatchUnLoadingList(obj: object) {
  await sleep(100);
  return requestClient.get(
    '/bondedwarehouse/outbound/dispatch/unLoading/getList',
    {
      params: obj,
    },
  );
}

export async function getOutDispatchUnLoadingItem(id: string) {
  await sleep(100);
  return requestClient.get(
    '/bondedwarehouse/outbound/dispatch/unLoading/getItem',
    {
      params: { id },
    },
  );
}

export async function bwOutDispatchItemLoading(id: string, ids: Array<string>) {
  await sleep(100);
  return requestClient.post('/bondedwarehouse/outbound/dispatch/item/loading', {
    id,
    ids,
  });
}

export async function getOutDispatchLoadingPallet(id: string) {
  await sleep(100);
  return requestClient.get(
    '/bondedwarehouse/outbound/dispatch/loading/getPallet',
    {
      params: { id },
    },
  );
}

export async function bwOutDispatchItemRemove(id: string, ids: Array<string>) {
  await sleep(100);
  return requestClient.post('/bondedwarehouse/outbound/dispatch/item/remove', {
    id,
    ids,
  });
}

export async function bwOutDispatchStatusCheck(id: string) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/outbound/dispatch/check', {
    params: { id },
  });
}

export async function bwOutDispatchDelivery(params: object) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/outbound/dispatch/delivery',
    params,
  );
}
