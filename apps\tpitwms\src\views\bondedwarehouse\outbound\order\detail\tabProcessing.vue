<script setup lang="ts">
import type { VxeGridListeners, VxeGridProps } from '#/adapter';

import { defineProps, reactive, ref } from 'vue';

import { useVbenDrawer, useVbenModal, VbenLoading } from '@vben/common-ui';
import { AntClear } from '@vben/icons';

import {
  DatePicker,
  InputNumber,
  message,
  Popconfirm,
  Tag,
} from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import {
  bwOutProcessingItemStatusUpdate,
  bwOutProcessingItemUpdate,
  getOutProcessingItem,
} from '#/api/bondedwarehouse/outbound';
import materialDrawer from '#/views/maintenance/data/material/materialDrawer.vue';
import batchDrawer from '#/views/sapneo/master/batch/batchDrawer.vue';
import skuDrawer from '#/views/sapneo/master/product/skuDrawer.vue';

import processingMultiModal from './processingMultiModal.vue';

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
  bl: {
    type: String,
    default: '',
  },
});

const loadingRef = ref(false);

const selectOptions = ref([]) as any;

const [SkuDrawer, skuDrawerApi] = useVbenDrawer({
  connectedComponent: skuDrawer,
});
const [BatchDrawer, batchDrawerApi] = useVbenDrawer({
  connectedComponent: batchDrawer,
});
const [MaterialDrawer, materialDrawerApi] = useVbenDrawer({
  // 连接抽离的组件
  connectedComponent: materialDrawer,
});

const [ProcessingMultiModal, ProcessingMultiModalApi] = useVbenModal({
  connectedComponent: processingMultiModal,
});

function openSkuDrawer(id: string) {
  skuDrawerApi.setData({ id });
  skuDrawerApi.open();
}
function openMaterialDrawer(id: string) {
  materialDrawerApi.setData({ id });
  materialDrawerApi.open();
}
function openBatchDrawer(sku: string, batch: string) {
  batchDrawerApi.setData({ sku, batch });
  batchDrawerApi.open();
}

function openProcessingMultiModal(ids: Array<String>) {
  ProcessingMultiModalApi.setData({ ids, options: selectOptions.value });
  ProcessingMultiModalApi.open();
}

/* const [ProcessingTypeForm, processingTypeFormApi] = useVbenForm({
  handleSubmit: handleUpdateProcessingType,
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
    labelClass: 'w-24',
  },
  schema: [
    {
      component: 'Select',
      componentProps: {
        placeholder: '请选择',
      },
      fieldName: 'ProcessingType',
      label: '加工类型',
      rules: 'required',
    },
  ],
  showDefaultActions: false,
}); */

const gridOptions = reactive<VxeGridProps>({
  id: 'BWOutProcessingItem',
  checkboxConfig: { highlight: true, range: true },
  cellStyle({ row, column }) {
    if (['RetentionQty'].includes(column.field) && row.RetentionQty > 0) {
      return {
        backgroundColor: '#fff9db',
      };
    }
    return null;
  },
  columns: [
    { type: 'checkbox', width: 48, fixed: 'left' },
    {
      fixed: 'left',
      title: '#',
      type: 'seq',
      width: 60,
    },
    {
      field: 'MatCode',
      title: '物料编号',
      width: 88,
      fixed: 'left',
      filters: [{ data: { vals: [], sVal: '' } }],
      filterRender: {
        name: 'FilterContent',
      },
      slots: { default: 'material' },
    },
    {
      field: 'BatchNo',
      fixed: 'left',
      title: '批号',
      width: 80,
      filters: [{ data: { vals: [], sVal: '' } }],
      filterRender: {
        name: 'FilterContent',
      },
      slots: { default: 'batchno' },
    },
    {
      field: 'ProcessingStatus',
      fixed: 'left',
      title: '加工状态',
      width: 88,
      filters: [
        { label: '待加工', value: false },
        { label: '已加工', value: true },
      ],
      filterMultiple: false,
      slots: { default: 'processingStatus' },
    },
    {
      field: 'GoodsNum',
      title: '产品数量',
      width: 100,
    },
    {
      field: 'RetentionQty',
      title: '留样数量',
      width: 96,
      /* editRender: { name: 'AInput' },
      slots: {
        edit: 'retention_edit',
      }, */
    },
    {
      field: 'ProcessingMethod',
      title: '加工方式',
      width: 172,
      editRender: {
        name: 'ASelect',
        autofocus: '',
      },
      slots: {
        edit: 'processingMethod_edit',
      },
    },
    {
      field: 'ProcessingAreaName',
      title: '加工位置',
      width: 128,
      editRender: {
        name: 'ASelect',
        autofocus: '',
      },
      slots: {
        edit: 'processingLocation_edit',
      },
    },
    {
      field: 'ProcessingDate',
      title: '贴标日期',
      width: 128,
      formatter: 'formatDate',
      editRender: {
        name: 'ADatePicker',
      },
      slots: { edit: 'processingDate_edit' },
    },
    {
      field: 'ProcessingBy',
      title: '操作人',
      width: 88,
      editRender: { name: 'AInput', autofocus: '' },
      slots: {
        edit: 'processingBy_edit',
      },
    },
    {
      field: 'Remark',
      title: '加工备注',
      width: 100,
      editRender: { name: 'AInput', autofocus: '' },
      slots: {
        edit: 'remark_edit',
      },
    },
    {
      field: 'NumberOfProcessings',
      title: '贴标总数',
      width: 100,
    },
    /* {
      field: 'NumberOfPackages',
      title: '分装包数',
      width: 88,
    }, */
    {
      field: 'PCB',
      title: 'PCB',
      width: 60,
    },
    {
      field: 'SPCB',
      title: 'SPCB',
      width: 60,
    },
    {
      field: 'ExtLabNum',
      title: '外标数',
      width: 64,
    },
    {
      field: 'InLabNum',
      title: '内标数',
      width: 64,
    },
    {
      field: 'SealingStickerNum',
      title: '封口贴数',
      width: 72,
    },
    {
      field: 'QRCodeNum',
      title: '二维码贴数',
      width: 88,
    },
    {
      field: 'PlasticPackagingNum',
      title: '塑封数',
      width: 64,
    },
    {
      field: 'OutBoxLabelNum',
      title: '外箱贴数',
      width: 72,
    },
    {
      field: 'PackingMethod',
      title: '包装方式',
      width: 160,
    },
    {
      field: 'PackageType',
      title: '包装大类',
      width: 128,
    },
    {
      field: 'LabType',
      title: '标签类型',
      width: 88,
    },
    {
      field: 'LabColor',
      title: '标签颜色',
      width: 88,
    },
    {
      field: 'LabMaterial',
      title: '标签材质',
      width: 108,
    },
    {
      field: 'LabSize',
      title: '标签尺寸',
      width: 128,
    },
    {
      field: 'PlaSize',
      title: '塑封尺寸',
      width: 128,
    },
    {
      field: 'Special',
      title: '特殊操作',
      width: 128,
    },
    {
      field: 'PalletNo',
      title: '入库托盘号',
      width: 88,
    },
    /* {
      field: 'SSCC',
      title: 'SSCC',
      width: 144,
    }, */
    /* {
      field: 'AreaName',
      title: '来源库区',
      width: 88,
    },
    {
      field: 'BIN',
      title: '来源货位',
      width: 96,
    }, */
    {
      field: 'OrderPickUpAreaName',
      title: '备货库区',
      width: 88,
    },
    {
      field: 'Invoice',
      title: '发票号',
      width: 96,
    },
    {
      field: 'oDivision',
      title: 'Division',
      width: 80,
    },
    {
      field: 'oBrandName',
      title: '品牌',
      width: 80,
    },
    {
      field: 'oOriginName',
      title: '原产国',
      width: 64,
    },
    {
      field: 'oMatType',
      title: '物料类型',
      width: 88,
    },
    {
      field: 'oBarCode',
      title: '条码',
      width: 128,
    },
    {
      field: 'oCName',
      title: '中文品名',
      minWidth: 220,
    },
  ],
  editConfig: {
    trigger: 'dblclick',
    mode: 'row',
    showIcon: true,
    showStatus: true,
  },
  filterConfig: {
    remote: false,
  },
  footerRowStyle: { 'font-weight': 'bold ' },
  footerMethod({ columns, data }) {
    return [
      columns.map((column, columnIndex) => {
        if (columnIndex === 0) {
          return `合计`;
        }
        if (columnIndex === 1) {
          return `${data.length} 项`;
        }
        if (['GoodsNum', 'RetentionQty'].includes(column.field)) {
          return sumNum(data, column.field);
        }
        if (['ProcessingStatus'].includes(column.field)) {
          const status = [
            ...new Set(
              data
                .map((item) => item.ProcessingStatus)
                .filter((item) => item !== ''),
            ),
          ];
          if (status.length === 1) {
            return `${status[0] ? '已加工' : '待加工'}`;
          } else if (status.length > 1) {
            return `部分加工`;
          }
        }
        if (['PalletNo'].includes(column.field)) {
          const traynos = [
            ...new Set(
              data.map((item) => item.PalletNo).filter((item) => item !== ''),
            ),
          ];
          return `托盘数: ${traynos.length}`;
        }
        return null;
      }),
    ];
  },
  height: 'auto',
  keepSource: true,
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    ajax: {
      query: async () => {
        const res = await getOutProcessingItem(props.id);
        selectOptions.value = res.options;
        return res.data;
      },
    },
    seq: true,
  },
  showFooter: true,
  size: 'mini',
  sortConfig: {
    remote: false,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
  scrollX: { enabled: true, gt: 0 },
  scrollY: { enabled: true, mode: 'wheel', gt: 30, oSize: 0 },
  menuConfig: {
    trigger: 'cell',
    body: {
      options: [
        [
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuCopy',
            disabled: false,
            name: '复制单元格',
            prefixConfig: {
              icon: 'vxe-icon-copy',
            },
            visible: true,
          },
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuRefresh',
            disabled: false,
            name: '刷新',
            prefixConfig: { icon: 'vxe-icon-refresh' },
            visible: true,
          },
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuExport',
            disabled: false,
            name: '导出明细',
            prefixConfig: { icon: 'vxe-icon-download' },
            visible: true,
          },
        ],
      ],
    },
    className: 'font-medium shadow rounded-md',
  },
});
const gridEvents: VxeGridListeners = {
  editClosed({ row, rowIndex }) {
    handleUpdateByRow(row, rowIndex);
  },
  menuClick({ menu }) {
    switch (menu.code) {
      case 'menuExport': {
        handleExport();
        break;
      }
      default: {
        break;
      }
    }
  },
};

const [Grid, gridApi] = useVbenVxeGrid({ gridOptions, gridEvents });

function handleExport() {
  gridApi.grid.exportData({
    filename: `出库加工明细_${props.bl}`,
    sheetName: 'Sheet1',
    type: 'xlsx',
    isFooter: false,
    columnFilterMethod: ({ column }) => {
      return column.field !== undefined;
    },
  });
}

function sumNum(list: any[], field: string) {
  let count = 0;
  list.forEach((item) => {
    count += Number(item[field]);
  });
  return count;
}

function handleSuccess() {
  gridApi.grid.commitProxy('query');
}

function handleUpdateByRow(row: any, rowIndex: number) {
  if (gridApi.grid.isUpdateByRow(row)) {
    gridApi.setLoading(true);
    bwOutProcessingItemUpdate(row)
      .then(() => {
        message.success(`序号${rowIndex + 1} 保存成功！ `);
        handleSuccess();
      })
      .catch(() => {
        gridApi.grid.revertData(row);
      })
      .finally(() => {
        gridApi.setLoading(false);
      });
  }
}
function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
}
function handleMultiUpdate() {
  const checkedRecords = gridApi.grid
    .getCheckboxRecords()
    .map((item: any) => item.Guid);
  if (checkedRecords.length === 0) {
    message.info(`请勾选产品明细`);
    return;
  }
  openProcessingMultiModal(checkedRecords);
}
function handleMultiOperate(typ: number) {
  const checkedRecords = gridApi.grid
    .getCheckboxRecords()
    .map((item: any) => item.Guid);
  if (checkedRecords.length === 0) {
    message.info(`请勾选产品明细`);
    return;
  }
  loadingRef.value = true;
  bwOutProcessingItemStatusUpdate(props.id, checkedRecords, typ)
    .then(() => {
      message.success('操作成功！');
      handleSuccess();
    })
    .catch(() => {})
    .finally(() => {
      loadingRef.value = false;
    });
}
</script>

<template>
  <div class="flex h-full flex-col">
    <Grid>
      <template #toolbar_left>
        <div class="hidden pl-1 text-base font-medium md:block">
          <span class="mr-2">出库加工</span>
        </div>
        <a-button class="mr-1" size="small" @click="clearFilterAndSort()">
          <AntClear class="size-5" />
        </a-button>
      </template>
      <template #toolbar-tools>
        <a-button
          class="mr-2"
          size="small"
          type="primary"
          @click="handleMultiOperate(1)"
        >
          确认加工
        </a-button>
        <a-button class="mr-2" size="small" @click="handleMultiOperate(0)">
          撤销加工
        </a-button>
        <a-button size="small" @click="handleMultiUpdate()">
          批量设置
        </a-button>
      </template>
      <template #processingStatus="{ row }">
        <Tag :color="row.ProcessingStatus ? 'green' : 'red'">
          {{ row.ProcessingStatus ? '已加工' : '待加工' }}
        </Tag>
      </template>
      <template #material="{ row }">
        <Popconfirm
          cancel-text="产品主数据"
          ok-text="基础物料"
          title="数据展示"
          @cancel="openSkuDrawer(row.MatCode)"
          @confirm="openMaterialDrawer(row.MatCode)"
        >
          <a class="text-blue-500" @click.prevent>
            {{ row.MatCode }}
          </a>
        </Popconfirm>
      </template>
      <template #batchno="{ row }">
        <a
          v-if="row.BatchNo"
          class="text-blue-500"
          @click="openBatchDrawer(row.MatCode, row.BatchNo)"
        >
          {{ row.BatchNo }}
        </a>
      </template>
      <template #retention_edit="{ row }">
        <InputNumber
          v-model:value="row.RetentionQty"
          :disabled="row.ProcessingStatus"
          :min="0"
          size="small"
        />
      </template>
      <template #processingMethod_edit="{ row }">
        <a-select
          class="w-full"
          v-model:value="row.ProcessingMethod"
          :disabled="row.ProcessingStatus"
          :max-tag-count="1"
          :options="
            selectOptions.filter((item: any) => {
              return item.type === 'ProcessingMethod';
            })
          "
          allow-clear
          mode="tags"
          size="small"
        />
      </template>
      <template #processingLocation_edit="{ row }">
        <a-select
          class="w-full"
          v-model:value="row.ProcessingAreaID"
          :disabled="row.ProcessingStatus"
          :options="
            selectOptions.filter((item: any) => {
              return item.type === 'ProcessingArea';
            })
          "
          allow-clear
          size="small"
          @change="
            (value: string, option: any) => {
              if (value) {
                row.ProcessingAreaName = option.label;
              } else {
                row.ProcessingAreaName = undefined;
              }
            }
          "
        />
      </template>
      <template #processingDate_edit="{ row }">
        <DatePicker
          v-model:value="row.ProcessingDate"
          :disabled="row.ProcessingStatus"
          :show-time="false"
          size="small"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </template>
      <template #processingBy_edit="{ row }">
        <a-input
          v-model:value="row.ProcessingBy"
          :disabled="row.ProcessingStatus"
          :maxlength="16"
          autocomplete="off"
          size="small"
        />
      </template>
      <template #remark_edit="{ row }">
        <a-input
          v-model:value="row.Remark"
          :disabled="row.ProcessingStatus"
          :maxlength="128"
          autocomplete="off"
          size="small"
        />
      </template>
      <!-- <template #processingType_edit="{ row }">
        <a-select
          v-model:value="row.ProcessingType"
          :disabled="row.OrderPickUpStatus !== 0"
          :options="processingTypeOptions"
          allow-clear
        />
      </template> -->
    </Grid>
    <MaterialDrawer @success="handleSuccess" />
    <SkuDrawer />
    <BatchDrawer />
    <ProcessingMultiModal @success="handleSuccess" />
    <VbenLoading :spinning="loadingRef" />
  </div>
</template>
