<script setup lang="ts">
import type { VxeGridListeners, VxeGridProps } from '#/adapter';

import { defineProps, reactive, ref } from 'vue';

import { useVbenDrawer, useVbenModal, VbenLoading } from '@vben/common-ui';
import { AntClear, AntSandBox } from '@vben/icons';

import { message, Modal, Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import {
  bwOutTallyItemCancel,
  getOutTallyItem,
} from '#/api/bondedwarehouse/outbound';
import batchDrawer from '#/views/sapneo/master/batch/batchDrawer.vue';
import skuDrawer from '#/views/sapneo/master/product/skuDrawer.vue';

import tallyModal from './tallyModal.vue';

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
  bl: {
    type: String,
    default: '',
  },
});

const loadingRef = ref(false);

const selectOptions = ref([]) as any;

const [SkuDrawer, skuDrawerApi] = useVbenDrawer({
  connectedComponent: skuDrawer,
});
const [BatchDrawer, batchDrawerApi] = useVbenDrawer({
  connectedComponent: batchDrawer,
});

const [TallyModal, TallyModalApi] = useVbenModal({
  connectedComponent: tallyModal,
});

function openSkuDrawer(id: string) {
  skuDrawerApi.setData({ id });
  skuDrawerApi.open();
}
function openBatchDrawer(sku: string, batch: string) {
  batchDrawerApi.setData({ sku, batch });
  batchDrawerApi.open();
}
function openTallyModal(id: string) {
  TallyModalApi.setData({ id });
  TallyModalApi.open();
}

const gridOptions = reactive<VxeGridProps>({
  id: 'BWOutTallyItem',
  checkboxConfig: {
    highlight: true,
    range: true,
    checkMethod: ({ row }: any) => {
      return !row.IsBatchDiff;
    },
  },
  cellStyle({ row, column }) {
    if (['RetentionQty'].includes(column.field) && row.RetentionQty > 0) {
      return {
        backgroundColor: '#fff9db',
      };
    }
    return null;
  },
  columns: [
    { type: 'checkbox', width: 48, fixed: 'left' },
    {
      fixed: 'left',
      title: '#',
      type: 'seq',
      width: 60,
    },
    { width: 56, fixed: 'left', title: '理货', slots: { default: 'opt' } },
    {
      field: 'MatCode',
      title: '物料编号',
      width: 88,
      fixed: 'left',
      filters: [{ data: { vals: [], sVal: '' } }],
      filterRender: {
        name: 'FilterContent',
      },
      slots: { default: 'material' },
    },
    {
      field: 'BatchNo',
      fixed: 'left',
      title: '批号',
      width: 80,
      filters: [{ data: { vals: [], sVal: '' } }],
      filterRender: {
        name: 'FilterContent',
      },
      slots: { default: 'batchno' },
    },
    {
      field: 'TallyStatus',
      fixed: 'left',
      title: '理货状态',
      width: 88,
      filters: [
        { label: '待理货', value: false },
        { label: '已理货', value: true },
      ],
      filterMultiple: false,
      slots: { default: 'tallyStatus' },
    },
    {
      field: 'GoodsNum',
      title: '产品数量',
      width: 100,
    },
    {
      field: 'RetentionQty',
      title: '留样数量',
      width: 88,
    },
    {
      field: 'TallyBoxNum',
      title: '理货箱数',
      width: 100,
    },
    {
      field: 'TallyQty',
      title: '实物数量',
      width: 100,
    },
    {
      field: 'MissingQty',
      title: '贴标少',
      width: 64,
    },
    {
      field: 'CIQQty',
      title: 'CIQ',
      width: 64,
    },
    {
      field: 'ShelveQty',
      title: '搁置',
      width: 64,
    },
    {
      field: 'ShelveReason',
      title: '搁置原因',
      width: 160,
    },
    {
      field: 'TallyBatch',
      title: '实物批号',
      width: 80,
      formatter: 'formatDateTime',
    },
    {
      field: 'TallyExpiration',
      title: '实物效期',
      width: 88,
    },
    {
      field: 'IsBatchDiff',
      title: '批次差异',
      width: 88,
      filters: [
        { label: '无差异', value: false },
        { label: '有差异', value: true },
      ],
      filterMultiple: false,
      slots: { default: 'isBatchDiff' },
    },

    {
      field: 'TallyDate',
      title: '理货时间',
      width: 136,
      formatter: 'formatDateTime',
    },
    {
      field: 'TallyBy',
      title: '操作人',
      width: 88,
    },
    {
      field: 'PalletNo',
      title: '入库托盘号',
      width: 88,
    },
    {
      field: 'OrderPickUpAreaName',
      title: '备货库区',
      width: 88,
    },
    {
      field: 'Invoice',
      title: '发票号',
      width: 96,
    },
    {
      field: 'oDivision',
      title: 'Division',
      width: 80,
    },
    {
      field: 'oBrandName',
      title: '品牌',
      width: 80,
    },
    {
      field: 'oOriginName',
      title: '原产国',
      width: 64,
    },
    {
      field: 'oMatType',
      title: '物料类型',
      width: 88,
    },
    {
      field: 'oBarCode',
      title: '条码',
      width: 128,
    },
    {
      field: 'oCName',
      title: '中文品名',
      minWidth: 220,
    },
  ],
  filterConfig: {
    remote: false,
  },
  footerRowStyle: { 'font-weight': 'bold ' },
  footerMethod({ columns, data }) {
    return [
      columns.map((column, columnIndex) => {
        if (columnIndex === 0) {
          return `合计`;
        }
        if (columnIndex === 1) {
          return `${data.length} 项`;
        }
        if (
          [
            'CIQQty',
            'GoodsNum',
            'MissingQty',
            'RetentionQty',
            'ShelveQty',
            /* 'TallyBoxNum', */
            'TallyQty',
          ].includes(column.field)
        ) {
          return sumNum(data, column.field);
        }
        if (['TallyStatus'].includes(column.field)) {
          const status = [
            ...new Set(
              data
                .map((item) => item.TallyStatus)
                .filter((item) => item !== ''),
            ),
          ];
          if (status.length === 1) {
            return `${status[0] ? '已理货' : '待理货'}`;
          } else if (status.length > 1) {
            return `部分加工`;
          }
        }
        if (['PalletNo'].includes(column.field)) {
          const traynos = [
            ...new Set(
              data.map((item) => item.PalletNo).filter((item) => item !== ''),
            ),
          ];
          return `托盘数: ${traynos.length}`;
        }
        return null;
      }),
    ];
  },
  height: 'auto',
  keepSource: true,
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    ajax: {
      query: async () => {
        const res = await getOutTallyItem(props.id);
        selectOptions.value = res.options;
        return res.data;
      },
    },
    seq: true,
  },
  showFooter: true,
  size: 'mini',
  sortConfig: {
    remote: false,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
  scrollX: { enabled: true, gt: 0 },
  scrollY: { enabled: true, mode: 'wheel', gt: 30, oSize: 0 },
  menuConfig: {
    trigger: 'cell',
    body: {
      options: [
        [
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuCopy',
            disabled: false,
            name: '复制单元格',
            prefixConfig: {
              icon: 'vxe-icon-copy',
            },
            visible: true,
          },
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuRefresh',
            disabled: false,
            name: '刷新',
            prefixConfig: { icon: 'vxe-icon-refresh' },
            visible: true,
          },
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuExport',
            disabled: false,
            name: '导出明细',
            prefixConfig: { icon: 'vxe-icon-download' },
            visible: true,
          },
        ],
      ],
    },
    className: 'font-medium shadow rounded-md',
  },
});
const gridEvents: VxeGridListeners = {
  menuClick({ menu }) {
    switch (menu.code) {
      case 'menuExport': {
        handleExport();
        break;
      }
      default: {
        break;
      }
    }
  },
};

const [Grid, gridApi] = useVbenVxeGrid({ gridOptions, gridEvents });

function handleExport() {
  gridApi.grid.exportData({
    filename: `出库理货明细_${props.bl}`,
    sheetName: 'Sheet1',
    type: 'xlsx',
    isFooter: false,
    columnFilterMethod: ({ column }) => {
      return column.field !== undefined;
    },
  });
}

function sumNum(list: any[], field: string) {
  let count = 0;
  list.forEach((item) => {
    count += Number(item[field]);
  });
  return count;
}

function handleSuccess() {
  gridApi.grid.commitProxy('query');
}

function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
}
function handleCancel() {
  Modal.confirm({
    content: `确认是否撤销理货，如已存在后续操作，将无法撤销`,
    onCancel() {},
    onOk() {
      operate();
    },
    title: `撤销理货`,
    okButtonProps: { danger: true },
  });
}
function operate() {
  const checkedRecords = gridApi.grid
    .getCheckboxRecords()
    .map((item: any) => item.Guid);
  if (checkedRecords.length === 0) {
    message.info(`请勾选产品明细`);
    return;
  }
  loadingRef.value = true;
  bwOutTallyItemCancel(checkedRecords)
    .then(() => {
      message.success('操作成功！');
      handleSuccess();
    })
    .catch(() => {})
    .finally(() => {
      loadingRef.value = false;
    });
}
</script>

<template>
  <div class="flex h-full flex-col">
    <Grid>
      <template #toolbar_left>
        <div class="hidden pl-1 text-base font-medium md:block">
          <span class="mr-2">出库理货</span>
        </div>
        <a-button class="mr-1" size="small" @click="clearFilterAndSort()">
          <AntClear class="size-5" />
        </a-button>
      </template>
      <template #toolbar-tools>
        <a-button danger class="mr-2" size="small" @click="handleCancel()">
          撤销理货
        </a-button>
      </template>
      <template #opt="{ row }">
        <a-button
          :disabled="row.TallyStatus"
          class="!pl-1 !pr-1"
          size="small"
          type="link"
          @click="openTallyModal(row.Guid)"
        >
          <AntSandBox class="size-6 pt-1" />
        </a-button>
      </template>
      <template #tallyStatus="{ row }">
        <Tag :color="row.TallyStatus ? 'green' : 'red'">
          {{ row.TallyStatus ? '已理货' : '待理货' }}
        </Tag>
      </template>
      <template #isBatchDiff="{ row }">
        <Tag v-if="row.TallyStatus" :color="row.IsBatchDiff ? 'red' : 'green'">
          {{ row.IsBatchDiff ? '有差异' : '无差异' }}
        </Tag>
      </template>
      <template #material="{ row }">
        <a
          v-if="row.MatCode"
          class="text-blue-500"
          @click="openSkuDrawer(row.MatCode)"
        >
          {{ row.MatCode }}
        </a>
      </template>
      <template #batchno="{ row }">
        <a
          v-if="row.BatchNo"
          class="text-blue-500"
          @click="openBatchDrawer(row.MatCode, row.BatchNo)"
        >
          {{ row.BatchNo }}
        </a>
      </template>
    </Grid>
    <SkuDrawer />
    <BatchDrawer />
    <TallyModal @success="handleSuccess" />
    <VbenLoading :spinning="loadingRef" />
  </div>
</template>
