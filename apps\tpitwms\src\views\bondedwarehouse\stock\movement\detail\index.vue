<script lang="ts" setup>
import { onActivated, onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';

import { Page, useVbenModal, VbenButton, VbenLoading } from '@vben/common-ui';
import { useTabs } from '@vben/hooks';
import { AntCaretDown, AntCaretUp } from '@vben/icons';
import { useTabbarStore } from '@vben/stores';

import { Badge, Card, Descriptions, TabPane, Tabs } from 'ant-design-vue';

import { getStockMovementHead } from '#/api/bondedwarehouse/stock';

import docCloseModal from './docCloseModal.vue';
import TabFlow from './tabFlow.vue';
/* import TabCSAR from './tabCSAR.vue';
import TabInbound from './tabInbound.vue';
import TabReceiving from './tabReceiving.vue'; */
import TabItem from './tabItem.vue';

const headData = ref({}) as any;
const loadingRef = ref(false);
const init = ref(false);
const showHead = ref(true);
const activeKey = ref('Item');
const route = useRoute();
const { setTabTitle } = useTabs();
const id = route.params?.id?.toString() ?? '';

/* function handleSuccess() {
  fetch();
} */
const [DocCloseModal, DocCloseModalApi] = useVbenModal({
  connectedComponent: docCloseModal,
});
function openDocCloseModal() {
  DocCloseModalApi.setData({ id });
  DocCloseModalApi.open();
}
/* function showCloseModal() {
  Modal.confirm({
    content: `请确认是否执行，确认后单据将被关闭，无法撤销`,
    onCancel() {},
    onOk() {
      handleClose();
    },
    title: `${headData.value.DocNo} 调整确认`,
  });
} */
/* function handleClose() { */
/* loadingRef.value = true;
  stockMovementExecute({id,})
    .then(() => {
      message.success('操作成功');
      fetch();
    })
    .catch(() => {})
    .finally(() => {
      loadingRef.value = false;
    }); */
/* } */
async function fetch() {
  const res = await getStockMovementHead(id);
  headData.value = res;
  const tabbarStore = useTabbarStore();
  const currentTab = tabbarStore.getTabs.find(
    (item) => item.path === route.path,
  );
  if (currentTab?.meta.title === '库存调整操作') {
    setTabTitle(`库存调整-${res.DocNo}`);
  }
  init.value = false;
}
onMounted(() => {
  init.value = true;
  showHead.value = true;
  fetch();
});
onActivated(() => {
  const tabbarStore = useTabbarStore();
  const currentTab = tabbarStore.getTabs.find(
    (item) => item.path === route.path,
  );
  if (!currentTab?.meta.newTabTitle && !init.value) {
    fetch();
  }
});
</script>

<template>
  <Page auto-content-height>
    <VbenLoading :spinning="loadingRef" />
    <div class="flex h-full flex-col">
      <Card
        :body-style="{ padding: '1px 1px 0px 1px' }"
        :bordered="false"
        size="small"
      >
        <template #title>
          <!-- <div class="hidden md:block"> -->
          {{ `单据信息` }}
          <!-- </div> -->
        </template>
        <!--  v-if="headData.CreateBy !== 'System'" -->
        <template #extra>
          <div class="flex">
            <VbenButton
              v-access:code="'BWStockMovement:Execute'"
              :disabled="headData.IsClosed"
              class="mr-2"
              size="sm"
              variant="info"
              @click="openDocCloseModal()"
            >
              执行调整
            </VbenButton>
            <AntCaretDown
              v-if="showHead"
              class="size-6"
              @click="showHead = !showHead"
            />
            <AntCaretUp v-else class="size-6" @click="showHead = !showHead" />
          </div>
        </template>
        <Descriptions
          v-show="showHead"
          :column="{ xs: 1, sm: 2, md: 3, lg: 4 }"
          bordered
          size="small"
        >
          <Descriptions.Item label="库存调整单号">
            {{ headData.DocNo }}
          </Descriptions.Item>
          <Descriptions.Item label="调整执行状态">
            <Badge
              :status="headData.IsClosed ? 'success' : 'processing'"
              :text="`${headData.IsClosed ? '已执行' : '未执行'}`"
            />
          </Descriptions.Item>
          <Descriptions.Item :span="2" label="备注">
            {{ headData.Remark }}
          </Descriptions.Item>
          <Descriptions.Item label="执行人">
            {{ headData.CloseBy }}
          </Descriptions.Item>
          <Descriptions.Item label="调整日期">
            {{ headData.CloseDate }}
          </Descriptions.Item>
          <Descriptions.Item label="记账日期">
            {{ headData.PostingDate }}
          </Descriptions.Item>
          <Descriptions.Item label="SAP调整单据">
            {{ headData.SAPDocNo }}
          </Descriptions.Item>
        </Descriptions>
      </Card>
      <div class="inorder-tab min-h-0 flex-1">
        <Tabs v-model:active-key="activeKey" class="h-full" tab-position="left">
          <TabPane key="Item" tab="明细">
            <div v-if="activeKey === 'Item'" class="h-full">
              <TabItem
                :id="id"
                :doc="headData.DocNo"
                :is-closed="headData.IsClosed"
                :is-locked="headData.IsLocked"
              />
            </div>
          </TabPane>
          <TabPane key="Record" tab="记录">
            <div v-if="activeKey === 'Record'" class="h-full">
              <TabFlow
                :id="id"
                :doc="headData.DocNo"
                :is-closed="headData.IsClosed"
              />
            </div>
          </TabPane>
          <!-- <TabPane key="SAP" tab="SAP">
            <div v-if="activeKey === 'SAP'">222</div>
          </TabPane> -->
        </Tabs>
      </div>
    </div>
    <DocCloseModal @success="fetch()" />
  </Page>
</template>

<style scoped lang="less">
::v-deep(.inorder-tab) {
  background-color: #fff;

  .ant-tabs-tabpane .ant-tabs-tabpane-active {
    padding-left: 0 !important;
  }

  .ant-tabs-left > .ant-tabs-nav .ant-tabs-tab {
    padding: 8px 12px;
  }

  .ant-tabs-tab-active {
    background-color: #e7f5ff;
  }

  .ant-tabs-content-holder > .ant-tabs-content > .ant-tabs-tabpane {
    padding-left: 0;
  }
  .ant-tabs-content-left {
    height: 100%;
  }
}
</style>
