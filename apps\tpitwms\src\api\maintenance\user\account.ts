import { requestClient, sleep } from '#/api/request';

/**
 * 系统维护 用户相关API
 */
export async function getAccountList(obj: object) {
  await sleep(100);
  return requestClient.get('/maintenance/user/account/getList', {
    params: obj,
  });
}

export async function getAccountData(id: string) {
  await sleep(100);
  return requestClient.get('/maintenance/user/account/getData', {
    params: { id },
  });
}

export async function accountUpdate(params: object) {
  await sleep(100);
  return requestClient.post('/maintenance/user/account/update', params);
}

export async function accountDelete(id: string) {
  await sleep(100);
  return requestClient.post('/maintenance/user/account/del', { id });
}
