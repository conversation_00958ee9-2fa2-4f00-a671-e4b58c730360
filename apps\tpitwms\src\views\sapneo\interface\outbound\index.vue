<script setup lang="ts">
import type { VxeGridListeners, VxeGridProps } from '#/adapter';

import { reactive, ref } from 'vue';

import { Page, useVbenDrawer } from '@vben/common-ui';
import { AntClear, AntDelete } from '@vben/icons';

/* import { RestOutlined } from '@ant-design/icons-vue'; */
import { message, Modal, Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import { getOutboundItem, getOutboundList, outboundDelete } from '#/api/sapneo';

import customerDrawer from '../../master/customer/customerDrawer.vue';
import skuDrawer from '../../master/product/skuDrawer.vue';
import recycleDrawer from './recycleDrawer.vue';

const [CustomerDrawer, customerDrawerApi] = useVbenDrawer({
  connectedComponent: customerDrawer,
});
const [SkuDrawer, skuDrawerApi] = useVbenDrawer({
  connectedComponent: skuDrawer,
});

const recycleDrawerRef = ref();

function openCustomerDrawer(id: string) {
  customerDrawerApi.setData({ id });
  customerDrawerApi.open();
}
function openSkuDrawer(id: string) {
  skuDrawerApi.setData({ id });
  skuDrawerApi.open();
}
const searchField: any = reactive({
  DeliveryDocument: undefined,
});
const itemGridOptions = reactive<VxeGridProps>({
  columns: [
    { type: 'seq', width: 48 },
    {
      field: 'DeliveryDocumentItem',
      title: '项号',
      width: 88,
    },
    { field: 'Plant', title: '工厂编码', width: 72 },
    {
      field: 'StorageLocation',
      title: '库存地点',
      width: 72,
    },

    {
      field: 'Material',
      title: '产品编号',
      width: 100,
      slots: { default: 'skucode' },
    },
    {
      field: 'MaterialIsBatchManaged',
      title: '批次管理',
      width: 72,
      align: 'center',
      slots: { default: 'batchmanage' },
    },
    { field: 'DeliveryQuantity', title: '指令数量', minWidth: 72 },
    { field: 'PickingQty', title: '拣货数量', minWidth: 72 },
    { field: 'DeliveryQuantityUnit', title: '单位', width: 48 },
    { field: 'ItemGrossWeight', title: '毛重', width: 80 },
    { field: 'ItemNetWeight', title: '净重', width: 80 },
    { field: 'ItemWeightUnit', title: '重量单位', width: 72 },
    { field: 'ItemVolume', title: '体积', width: 80 },
    { field: 'ItemVolumeUnit', title: '体积单位', width: 72 },
    { field: 'DistributionChannel', title: '分销渠道', width: 72 },
    { field: 'Division', title: 'Division', width: 72 },
    { field: 'GoodsMovementType', title: '移动类型', width: 72 },
    { field: 'ReferenceSDDocument', title: '销售订单', width: 88 },
    { field: 'ReferenceSDDocumentItem', title: '销售项号', width: 72 },
    {
      field: 'ZZ1_Customer_referenc1_DLI',
      title: '采购订单',
      minWidth: 108,
    },
    {
      field: 'ZZ1_Customer_PO_Type_DLI',
      title: '直流订单',
      width: 72,
    },
  ],
  customConfig: {
    storage: false,
  },
  menuConfig: {
    body: {
      options: [
        [
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuCopy',
            disabled: false,
            name: '复制单元格',
            prefixConfig: {
              icon: 'vxe-icon-copy',
            },
            visible: true,
          },
        ],
      ],
    },
    className: 'font-medium shadow rounded-md',
  },
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    enabled: false,
  },
  rowConfig: {
    useKey: true,
    keyField: 'Guid',
  },
  rowStyle({ row }) {
    if (row.Qty === 0) {
      return {
        backgroundColor: '#fff4e6',
        color: '#222',
      };
    }
  },
  size: 'mini',
  toolbarConfig: {
    enabled: false,
  },
});
const [ItemGrid, itemGridApi] = useVbenVxeGrid({
  gridOptions: itemGridOptions,
});
const gridOptions = reactive<VxeGridProps>({
  id: 'NEOOutboundDelivery',
  columns: [
    {
      title: '#',
      type: 'seq',
      width: 60,
    },
    { type: 'expand', width: 48, slots: { content: 'expand_content' } },
    {
      field: 'DeliveryDocument',
      title: 'Outbound',
      minWidth: 100,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'DeliveryDocumentType',
      title: '单据类型',
      minWidth: 96,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'SO',
      title: '销售订单',
      minWidth: 88,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'SOType',
      title: '销售类型',
      minWidth: 88,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'SoldToParty',
      title: '售往',
      minWidth: 88,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      slots: { default: 'soldto' },
      visible: false,
    },
    {
      field: 'Urgent',
      title: '紧急程度',
      minWidth: 88,
      filters: [
        { label: '紧急', value: true },
        { label: '普通', value: false },
      ],
      filterMultiple: false,
      slots: { default: 'urgent' },
    },
    {
      field: 'ShipToParty',
      title: '运往',
      minWidth: 88,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      slots: { default: 'shipto' },
    },
    {
      field: 'BusinessPartnerName1',
      title: '收货人',
      minWidth: 136,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'ShippingPoint',
      title: '装运点',
      minWidth: 64,
      visible: false,
    },
    {
      field: 'PickingDate',
      title: '计划拣货日期',
      minWidth: 136,
      formatter: 'formatDateTime',
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
      visible: false,
    },
    {
      field: 'LoadingDate',
      title: '计划装载日期',
      minWidth: 136,
      formatter: 'formatDateTime',
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
      visible: false,
    },
    {
      field: 'TransportationPlanningDate',
      title: '计划运输日期',
      minWidth: 136,
      formatter: 'formatDateTime',
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
      visible: false,
    },
    {
      field: 'DeliveryDate',
      title: '计划交付日期',
      minWidth: 136,
      formatter: 'formatDateTime',
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'TotalQty',
      title: '总件数',
      minWidth: 88,
    },
    {
      field: 'ItemsCount',
      title: 'Item项',
      minWidth: 60,
    },
    {
      field: 'WMSDoc',
      title: 'WMS拣货订单',
      minWidth: 136,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'PGIStatus',
      title: 'PGI状态',
      minWidth: 88,
      filters: [
        { label: '未关闭', value: false },
        { label: '已关闭', value: true },
      ],
      filterMultiple: false,
      slots: { default: 'pgiStatus' },
    },
    {
      field: 'PGIDate',
      title: 'PGI日期',
      minWidth: 132,
      formatter: 'formatDateTime',
      sortable: true,
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'CreationDate',
      title: 'S创建日期',
      minWidth: 132,
      formatter: 'formatDateTime',
      sortable: true,
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'CreateDate',
      title: 'W创建日期',
      minWidth: 132,
      formatter: 'formatDateTime',
      sortable: true,
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'UpdateDate',
      title: 'W更新日期',
      minWidth: 132,
      formatter: 'formatDateTime',
      sortable: true,
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
      visible: false,
    },
    {
      field: 'Delete',
      title: '操作',
      width: 80,
      slots: { default: 'action' },
    },
  ],
  expandConfig: {
    accordion: true,
  },
  filterConfig: {
    remote: true,
  },
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: ({ filters, page, sorts }) => {
        const queryParams: any = Object.assign({}, page);
        // 处理排序条件
        if (sorts.length === 0) {
          queryParams.sort = 'CreateDate desc';
        } else {
          const sortItem = sorts.map((item) => {
            const newItem = `${item.field} ${item.order}`;
            return newItem;
          });
          queryParams.sort = sortItem.join(',');
        }
        // 处理筛选
        Object.getOwnPropertyNames(searchField).forEach((key) => {
          searchField[key] = undefined;
        });
        filters.forEach(({ field, values }) => {
          queryParams[field] = values.join(',');
          if (Object.prototype.hasOwnProperty.call(searchField, field)) {
            const jsonObject = JSON.parse(values.toString());
            searchField[field] = jsonObject.text;
          }
        });
        return getOutboundList(queryParams);
      },
    },
    seq: true,
  },
  size: 'mini',
  sortConfig: {
    remote: true,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
});
const gridEvents: VxeGridListeners = {
  async toggleRowExpand({ expanded, row }) {
    await handleItemData([]);
    if (expanded) {
      handleItemLoading(true);
      const itemData = await getOutboundItem(row.Guid);
      await handleHeadRow(row);
      await handleItemData(itemData);
      handleItemLoading(false);
      itemGridApi.grid?.setAllTreeExpand(true);
    }
  },
};
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions, gridEvents });

function handleHeadRow(row: any) {
  gridApi.grid.setCurrentRow(row);
}
function handleItemData(data: any) {
  itemGridApi.setGridOptions({
    data,
  });
}
function handleItemLoading(isLoading: boolean) {
  itemGridApi.setLoading(isLoading);
}
function searchEvent(field: string) {
  const column = gridApi.grid.getColumnByField(field);
  if (column) {
    // 修改第一个选项为勾选状态
    const option = column.filters[0];
    if (!option) {
      return;
    }

    if (searchField[field]) {
      option.data = { type: '1', text: searchField[field] };
      option.value = JSON.stringify(option.data);
      option.checked = true;
    } else {
      option.data = { type: '0', text: undefined };
      option.checked = false;
    }

    // 修改条件之后，需要手动调用 updateData 处理表格数据
    gridApi.grid.updateData();
    gridApi.grid.commitProxy('query');
  }
}
function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
  gridApi.grid.commitProxy('query');
}

function handleDelete(id: string) {
  outboundDelete(id)
    .then(() => {
      message.success('操作成功');
      gridApi.grid?.commitProxy('query');
    })
    .catch(() => {})
    .finally(() => {});
}

function showDeleteModal(id: string, name: string) {
  Modal.confirm({
    content: `请确认是否移除？`,
    onCancel() {},
    onOk() {
      handleDelete(id);
    },
    title: `Outbound: ${name}`,
    okButtonProps: { danger: true },
  });
}
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar_left>
        <a-button class="mr-1" @click="clearFilterAndSort()">
          <AntClear class="size-5" />
        </a-button>
        <div class="hidden pl-1 md:block">
          <a-input-search
            v-model:value="searchField.DeliveryDocument"
            allow-clear
            class="mr-2 w-60"
            placeholder="Outbound"
            @search="searchEvent('DeliveryDocument')"
          />
        </div>
      </template>
      <template #toolbar-tools>
        <a-button class="mr-1" @click="recycleDrawerRef.open()">
          <!-- <AntRest class="size-5" /> -->
          回收站
        </a-button>
      </template>
      <template #expand_content>
        <div>
          <ItemGrid>
            <template #skucode="{ row }">
              <a
                v-if="row.Material"
                class="text-blue-500"
                @click="openSkuDrawer(row.Material)"
              >
                {{ row.Material }}
              </a>
            </template>
            <template #batchmanage="{ row }">
              <Tag :color="row.MaterialIsBatchManaged ? 'green' : 'red'">
                {{ row.MaterialIsBatchManaged ? '是' : '否' }}
              </Tag>
            </template>
          </ItemGrid>
        </div>
      </template>
      <template #urgent="{ row }">
        <Tag :color="row.Urgent ? 'orange' : 'blue'">
          {{ row.Urgent ? '紧急' : '普通' }}
        </Tag>
      </template>
      <template #soldto="{ row }">
        <a
          v-if="row.SoldToParty"
          class="text-blue-500"
          @click="openCustomerDrawer(row.SoldToParty)"
        >
          {{ row.SoldToParty }}
        </a>
      </template>
      <template #shipto="{ row }">
        <a
          v-if="row.ShipToParty"
          class="text-blue-500"
          @click="openCustomerDrawer(row.ShipToParty)"
        >
          {{ row.ShipToParty }}
        </a>
      </template>
      <template #pgiStatus="{ row }">
        <Tag :color="row.PGIStatus ? 'green' : 'blue'">
          {{ row.PGIStatus ? '已关闭' : '未关闭' }}
        </Tag>
      </template>
      <template #action="{ row }">
        <a-button
          :disabled="row.PGIStatus || !!row.WMSID"
          danger
          size="small"
          type="link"
          @click="showDeleteModal(row.Guid, row.DeliveryDocument)"
        >
          <AntDelete class="size-5" />
        </a-button>
      </template>
    </Grid>
    <CustomerDrawer />
    <SkuDrawer />
    <recycleDrawer
      ref="recycleDrawerRef"
      @success="gridApi.grid?.commitProxy('query')"
    />
  </Page>
</template>
