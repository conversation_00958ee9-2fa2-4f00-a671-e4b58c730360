<script setup lang="ts">
import type { VxeGridListeners, VxeGridProps } from '#/adapter';

import { reactive, ref } from 'vue';

import { Page, useVbenDrawer } from '@vben/common-ui';
import { AntClear } from '@vben/icons';
import { downloadFileFromBlob } from '@vben/utils';

import { useVbenVxeGrid } from '#/adapter';
import { addExportRecord, exportFile } from '#/api/common';
import { getMaterialList } from '#/api/maintenance/data';

import materialDrawer from './materialDrawer.vue';

const gridQueryParams = ref({}) as any;
const loadingRef = ref(false);
const [MaterialDrawer, materialDrawerApi] = useVbenDrawer({
  // 连接抽离的组件
  connectedComponent: materialDrawer,
});
const searchField: any = reactive({
  MatCode: undefined,
  BarCode: undefined,
});
const gridOptions = reactive<VxeGridProps>({
  id: 'DataMaterial',
  columns: [
    {
      title: '#',
      type: 'seq',
      width: 60,
    },
    {
      field: 'MatCode',
      title: '物料编号',
      minWidth: 88,
      filters: [{ data: null }],
      filterRender: { name: 'FilterInput' },
    },
    {
      field: 'BarCode',
      title: '物料条码',
      width: 112,
      filters: [{ data: null }],
      filterRender: { name: 'FilterInput' },
    },
    {
      field: 'MatType',
      title: '物料类型',
      minWidth: 88,
      filters: [{ data: null }],
      filterRender: { name: 'FilterInput' },
    },
    {
      field: 'BrandName',
      title: '品牌',
      minWidth: 100,
    },
    {
      field: 'Specifaction',
      title: '规格',
      minWidth: 88,
      formatter({ row }) {
        return `${row.GS1NetContentMetric}${row.GS1NetContentMetricUnit}`;
      },
    },
    { field: 'PCB', title: 'PCB', minWidth: 64 },
    { field: 'SPCB', title: 'SPCB', minWidth: 64 },
    { field: 'layerSupportQuantity', title: '层规数量', minWidth: 72 },
    { field: 'palletSupportQuantity', title: '托规数量', minWidth: 72 },
    { field: 'PCLInWMS', title: '实际层规', minWidth: 72, visible: false },
    { field: 'PCPInWMS', title: '实际托规', minWidth: 72, visible: false },
    { field: 'Origin', title: '原产国', minWidth: 72, visible: false },
    {
      field: 'ExtLabNum',
      title: '外标数量',
      minWidth: 80,
      visible: false,
    },
    {
      field: 'InLabNum',
      title: '内标数量',
      minWidth: 80,
      visible: false,
    },
    {
      field: 'SealingStickerNum',
      title: '封口贴数量',
      minWidth: 80,
      visible: false,
    },
    {
      field: 'QRCodeNum',
      title: '二维码数量',
      minWidth: 80,
      visible: false,
    },
    {
      field: 'OutBoxLabelNum',
      title: '外箱贴数量',
      minWidth: 80,
      visible: false,
    },
    {
      field: 'PlasticPackagingNum',
      title: '塑封数量',
      minWidth: 80,
      visible: false,
    },
    {
      field: 'CName',
      title: '中文品名',
      minWidth: 240,
      filters: [{ data: null }],
      filterRender: { name: 'FilterInput' },
    },
    {
      field: 'EName',
      title: '英文品名',
      minWidth: 240,
      filters: [{ data: null }],
      filterRender: { name: 'FilterInput' },
    },
    { field: 'MatUnit', title: '单位', minWidth: 64 },
    { field: 'Remark', title: '备注', minWidth: 100 },
    /* {
      field: 'CreateBy',
      title: '创建人',
      width: 120,
    }, */
    { field: 'IsCareful', title: '精细理货', width: 80, visible: false },
    {
      field: 'CreateDate',
      filterRender: { name: 'FilterDateRange' },
      filters: [{ data: { date: [], type: '0' } }],
      formatter: 'formatDateTime',
      sortable: true,
      title: '创建日期',
      width: 132,
    },
    {
      field: 'UpdateDate',
      filterRender: { name: 'FilterDateRange' },
      filters: [{ data: { date: [], type: '0' } }],
      formatter: 'formatDateTime',
      sortable: true,
      title: '更新日期',
      width: 132,
    },
  ],
  filterConfig: {
    remote: true,
  },
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: ({ filters, page, sorts }) => {
        const queryParams: any = Object.assign({}, page);
        // 处理排序
        if (sorts.length === 0) {
          queryParams.sort = '_Identify desc';
        } else {
          const sortItem = sorts.map((item) => {
            const newItem = `${item.field} ${item.order}`;
            return newItem;
          });
          queryParams.sort = sortItem.join(',');
        }
        // 处理筛选
        Object.getOwnPropertyNames(searchField).forEach((key) => {
          searchField[key] = undefined;
        });
        filters.forEach(({ field, values }) => {
          queryParams[field] = values.join(',');
          if (Object.prototype.hasOwnProperty.call(searchField, field)) {
            searchField[field] = values.toString();
          }
        });
        return getMaterialList(queryParams);
      },
    },
    seq: true,
  },
  scrollY: { enabled: true, gt: 0 },
  size: 'mini',
  sortConfig: {
    remote: true,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
  menuConfig: {
    trigger: 'cell',
    body: {
      options: [
        [
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuCopy',
            disabled: false,
            name: '复制单元格',
            prefixConfig: {
              icon: 'vxe-icon-copy',
            },
            visible: true,
          },
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuRefresh',
            disabled: false,
            name: '刷新',
            prefixConfig: { icon: 'vxe-icon-refresh' },
            visible: true,
          },
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuExport',
            disabled: false,
            name: '导出明细',
            prefixConfig: { icon: 'vxe-icon-download' },
            visible: true,
          },
        ],
      ],
    },
    className: 'font-medium shadow rounded-md',
  },
});

const gridEvents: VxeGridListeners = {
  cellDblclick({ row }) {
    openMaterialDrawer(row.MatCode);
  },
  menuClick({ menu }) {
    switch (menu.code) {
      case 'menuExport': {
        handleExport();
        break;
      }
      default: {
        break;
      }
    }
  },
};
function handleExport() {
  addExportRecord({
    typ: 'BaseMaterial',
    params: gridQueryParams.value,
    columns: gridApi.grid
      .getColumns()
      .filter((item) => item.field && item.title)
      .map((item) => ({
        field: item.field,
        title: item.title,
      })),
  })
    .then((res) => {
      loadingRef.value = true;
      exportFile(res)
        .then((res) => {
          downloadFileFromBlob({
            source: res.data,
            fileName: `基础物料_${Date.now()}.xlsx`,
          });
        })
        .catch(() => {})
        .finally(() => {
          loadingRef.value = false;
        });
    })
    .catch(() => {})
    .finally(() => {});
}
const [Grid, gridApi] = useVbenVxeGrid({ gridEvents, gridOptions });

function searchEvent(field: string, type: string) {
  const column = gridApi.grid.getColumnByField(field);
  if (column) {
    // 修改第一个选项为勾选状态
    const option = column.filters[0];
    if (!option) {
      return;
    }
    if (searchField[field]) {
      option.data = searchField[field];
      option.value = option.data;
      option.checked = true;
    } else {
      if (type) {
        option.data = undefined;
        option.checked = false;
      }
    }
    // 修改条件之后，需要手动调用 updateData 处理表格数据
    gridApi.grid.updateData();
    gridApi.grid.commitProxy('query');
  }
}
function openMaterialDrawer(id: string) {
  materialDrawerApi.setData({ id });
  materialDrawerApi.open();
}
function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
  gridApi.grid.commitProxy('query');
}
function handleSuccess() {
  gridApi.grid.commitProxy('query');
}
</script>

<template>
  <Page auto-content-height v-loading="loadingRef">
    <Grid>
      <template #toolbar_left>
        <a-button class="mr-1" @click="clearFilterAndSort()">
          <AntClear class="size-5" />
        </a-button>
        <div class="hidden pl-1 md:block">
          <a-input-search
            v-model:value="searchField.MatCode"
            allow-clear
            class="mr-2 w-60"
            placeholder="物料编码"
            @search="searchEvent('MatCode', 'Input')"
          />
          <a-input-search
            v-model:value="searchField.BarCode"
            allow-clear
            class="mr-2 w-60"
            placeholder="物料条码"
            @search="searchEvent('BarCode', 'Input')"
          />
        </div>
      </template>
      <template #toolbar-tools> </template>
    </Grid>
    <MaterialDrawer @success="handleSuccess" />
  </Page>
</template>
