<script setup lang="ts">
import type { VxeGridListeners, VxeGridProps } from '#/adapter';

import { defineProps, reactive } from 'vue';

import { useVbenDrawer, useVbenModal } from '@vben/common-ui';
import { AntClear, AntSandBox } from '@vben/icons';

import { DatePicker, message, Modal, Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import {
  bwInReturnInspectionCancel,
  getInReturnInspectionItem,
} from '#/api/bondedwarehouse/inbound';
import batchDrawer from '#/views/sapneo/master/batch/batchDrawer.vue';
import skuDrawer from '#/views/sapneo/master/product/skuDrawer.vue';

import inspectionModal from './InspectionModal.vue';
import splitModal from './SplitModal.vue';

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
  doc: {
    type: String,
    default: '',
  },
  inStatus: {
    type: Boolean,
    default: false,
  },
  isClosed: {
    type: Boolean,
    default: false,
  },
  owner: {
    type: String,
    default: '',
  },
});

const [InspectionModal, InspectionModalApi] = useVbenModal({
  connectedComponent: inspectionModal,
});
const [SplitModal, SplitModalApi] = useVbenModal({
  connectedComponent: splitModal,
});

const [SkuDrawer, skuDrawerApi] = useVbenDrawer({
  connectedComponent: skuDrawer,
});
const [BatchDrawer, batchDrawerApi] = useVbenDrawer({
  connectedComponent: batchDrawer,
});

function openInspectionModal(id: string) {
  InspectionModalApi.setData({ id });
  InspectionModalApi.open();
}
function openSplitModal() {
  SplitModalApi.setData({ id: props.id });
  SplitModalApi.open();
}
function openSkuDrawer(id: string) {
  skuDrawerApi.setData({ id });
  skuDrawerApi.open();
}
function openBatchDrawer(sku: string, batch: string) {
  batchDrawerApi.setData({ sku, batch });
  batchDrawerApi.open();
}
const gridOptions = reactive<VxeGridProps>({
  id: 'BWInOrderItem',
  checkboxConfig: { highlight: true, range: true },
  cellStyle({ row, column }) {
    if (
      ['MissingQty', 'ReceivedQty'].includes(column.field) &&
      row.ReceivedQty !== null
    ) {
      if (row.ReceivedQty > row.EstimatedReceivedQty) {
        return {
          backgroundColor: '#ffe3e3',
        };
      } else if (row.ReceivedQty < row.EstimatedReceivedQty) {
        return {
          backgroundColor: '#fff9db',
        };
      }
    }
    if (
      ['ReceivedBatch'].includes(column.field) &&
      row.ReceivedBatch &&
      row.InboundBatch !== row.ReceivedBatch
    ) {
      return {
        backgroundColor: '#fff9db',
      };
    }
    return null;
  },
  columns: [
    { type: 'checkbox', width: 48, fixed: 'left' },
    {
      fixed: 'left',
      title: '#',
      type: 'seq',
      width: 60,
    },
    { width: 56, fixed: 'left', title: '质检', slots: { default: 'opt' } },
    {
      field: 'MatCode',
      title: '物料编号',
      width: 88,
      fixed: 'left',
      filters: [{ data: null }],
      filterRender: { name: 'FilterInput' },
      slots: { default: 'material' },
    },
    {
      field: 'ReceivedQty',
      title: '收货数量',
      width: 80,
    },
    {
      field: 'InspectionStatus',
      title: '质检状态',
      width: 88,
      formatter({ cellValue }) {
        return cellValue ? '已质检' : '待质检';
      },
      filters: [
        { label: '已质检', value: true },
        { label: '待质检', value: false },
      ],
      filterMultiple: false,
      slots: { default: 'iStatus' },
    },
    {
      field: 'ReturnBatch',
      title: '实物批号',
      width: 80,
      slots: { default: 'batch' },
    },
    {
      field: 'ReturnExpirationDate',
      title: '实收效期',
      width: 88,
    },
    {
      field: 'InspectionQty',
      title: '质检数量',
      width: 80,
    },
    {
      field: 'IsNormal',
      title: '产品状态',
      width: 88,
      formatter({ cellValue }) {
        return cellValue ? '正常' : '异常';
      },
      filters: [
        { label: '正常', value: true },
        { label: '异常', value: false },
      ],
      filterMultiple: false,
      slots: { default: 'pStatus' },
    },
    { field: 'TargetLocation', title: '目标SAP位置', width: 100 },
    {
      field: 'InspectionBy',
      title: '质检人',
      width: 80,
    },
    {
      field: 'InspectionDate',
      title: '质检时间',
      width: 164,
      formatter: 'formatDateTime',
      slots: { edit: 'receiveddate_edit' },
    },
    {
      field: 'Remark',
      title: '质检备注',
      width: 180,
    },

    {
      field: 'iCName',
      title: '中文品名',
      width: 240,
    },
    { field: 'iBarCode', title: '条形码', width: 112 },
    { field: 'iMatType', title: '物料类型', width: 80 },
    { field: 'iDivision', title: 'Division', width: 80 },
    {
      field: 'iBrandName',
      title: '品牌',
      width: 80,
    },
    { field: 'iOriginName', title: '原产国', width: 80 },
    { field: 'iSpecifaction', title: '规格', width: 80 },
  ],

  filterConfig: {
    remote: false,
  },
  footerRowStyle: { 'font-weight': 'bold ' },
  footerMethod({ columns, data }) {
    return [
      columns.map((column, columnIndex) => {
        if (columnIndex === 0) {
          return `合计`;
        }
        if (columnIndex === 1) {
          return `${data.length} 项`;
        }
        if (['InspectionQty', 'ReceivedQty'].includes(column.field)) {
          return sumNum(data, column.field);
        }
        if (['ReceivingStatus'].includes(column.field)) {
          const status = [
            ...new Set(
              data
                .map((item) => item.ReceivingStatus)
                .filter((item) => item !== ''),
            ),
          ];
          if (status.length === 1) {
            return `${status[0] ? '已收货' : '待收货'}`;
          } else if (status.length > 1) {
            return `部分入库`;
          }
        }
        if (['PalletNo'].includes(column.field)) {
          const traynos = [
            ...new Set(
              data.map((item) => item.PalletNo).filter((item) => item !== ''),
            ),
          ];
          return `托盘数: ${traynos.length}`;
        }
        return null;
      }),
    ];
  },
  height: 'auto',
  keepSource: true,
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    ajax: {
      query: async () => {
        const res = await getInReturnInspectionItem(props.id);
        return res.data;
      },
    },
    seq: true,
  },
  showFooter: true,
  size: 'mini',
  sortConfig: {
    remote: false,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
  scrollX: { enabled: true, gt: 0 },
  scrollY: { enabled: true, mode: 'wheel', gt: 30, oSize: 0 },
  menuConfig: {
    trigger: 'cell',
    body: {
      options: [
        [
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuCopy',
            disabled: false,
            name: '复制单元格',
            prefixConfig: {
              icon: 'vxe-icon-copy',
            },
            visible: true,
          },
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuRefresh',
            disabled: false,
            name: '刷新',
            prefixConfig: { icon: 'vxe-icon-refresh' },
            visible: true,
          },
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuExport',
            disabled: false,
            name: '导出明细',
            prefixConfig: { icon: 'vxe-icon-download' },
            visible: true,
          },
        ],
      ],
    },
    className: 'font-medium shadow rounded-md',
  },
});
const gridEvents: VxeGridListeners = {
  menuClick({ menu }) {
    switch (menu.code) {
      case 'menuExport': {
        handleExport();
        break;
      }
      default: {
        break;
      }
    }
  },
};

const [Grid, gridApi] = useVbenVxeGrid({ gridOptions, gridEvents });

function handleExport() {
  gridApi.grid.exportData({
    filename: `退货质检明细_${props.doc}`,
    original: false,
    sheetName: 'Sheet1',
    type: 'xlsx',
    isFooter: false,
    columnFilterMethod: ({ column }) => {
      return column.field !== undefined;
    },
  });
}

function sumNum(list: any[], field: string) {
  let count = 0;
  list.forEach((item) => {
    count += Number(item[field]);
  });
  return count;
}
/* function openCreateModal() {
  createModalApi.open();
} */

function handleSuccess() {
  gridApi.grid.commitProxy('query');
}

function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
}
function handleCancel() {
  const checkedRecords = gridApi.grid
    .getCheckboxRecords()
    .map((item: any) => item.Guid);
  if (checkedRecords.length === 0) {
    message.info(`请勾选产品明细`);
    return;
  }
  Modal.confirm({
    content: `撤销将清除质检信息`,
    onCancel() {},
    onOk() {
      operation();
    },
    title: `撤销质检操作`,
    okButtonProps: { danger: true },
  });
}
function operation() {
  const checkedRecords = gridApi.grid
    .getCheckboxRecords()
    .map((item: any) => item.Guid);
  bwInReturnInspectionCancel(props.id, checkedRecords)
    .then(() => {
      handleSuccess();
    })
    .catch(() => {})
    .finally(() => {});
}
</script>

<template>
  <div class="flex h-full flex-col">
    <Grid>
      <template #toolbar_left>
        <div class="hidden pl-1 text-base font-medium md:block">
          <span class="mr-2">质检明细</span>
        </div>
        <a-button class="mr-1" size="small" @click="clearFilterAndSort()">
          <AntClear class="size-5" />
        </a-button>
        <div class="hidden md:block"></div>
      </template>
      <template #toolbar-tools>
        <a-button size="small" @click="handleCancel()">撤销质检</a-button>
      </template>
      <template #opt="{ row }">
        <a-button
          :disabled="row.InspectionStatus"
          class="!pl-1 !pr-1"
          size="small"
          type="link"
          @click="openInspectionModal(row.Guid)"
        >
          <AntSandBox class="size-6 pt-1" />
        </a-button>
      </template>
      <template #iStatus="{ row }">
        <Tag :color="row.InspectionStatus ? 'green' : 'blue'">
          {{ row.InspectionStatus ? '已质检' : '待质检' }}
        </Tag>
      </template>
      <template #pStatus="{ row }">
        <Tag
          v-if="row.InspectionStatus"
          :color="row.IsNormal ? 'green' : 'red'"
          class="mr-0"
        >
          {{ row.IsNormal ? '正常' : '异常' }}
        </Tag>
      </template>
      <template #material="{ row }">
        <a class="text-blue-500" @click="openSkuDrawer(row.MatCode)">
          {{ row.MatCode }}
        </a>
      </template>
      <template #batch="{ row }">
        <a
          v-if="row.ReturnBatch"
          class="text-blue-500"
          @click="openBatchDrawer(row.MatCode, row.ReturnBatch)"
        >
          {{ row.ReturnBatch }}
        </a>
      </template>
      <template #receiveddate_edit="{ row }">
        <DatePicker
          v-model:value="row.ReceivedDate"
          :disabled="row.InboundGRStatus || !row.ReceivingStatus"
          :show-time="true"
          size="small"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </template>
      <template #bottom>
        <div class="h-6 pt-1 text-sm font-bold">
          <div class="float-left hidden md:block"></div>
          <div class="float-right">
            <a-button size="small" @click="openSplitModal()">
              拆分记录
            </a-button>
          </div>
        </div>
      </template>
    </Grid>
    <InspectionModal @success="handleSuccess" />
    <SplitModal @success="handleSuccess" />
    <MaterialDrawer @success="handleSuccess" />
    <SkuDrawer />
    <BatchDrawer />
  </div>
</template>
