<script setup lang="ts">
import type { VxeGridListeners, VxeGridProps } from '#/adapter';

import { defineProps, reactive } from 'vue';

import { useVbenDrawer, useVbenModal } from '@vben/common-ui';
import { AntClear, AntSandBox } from '@vben/icons';

import {
  DatePicker,
  message,
  Modal,
  /* CheckableTag, */ Popconfirm,
  Tag,
} from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import {
  bwInReturnItemUpdate,
  bwInReturnReveivingCancel,
  getInReturnItem,
} from '#/api/bondedwarehouse/inbound';
import materialDrawer from '#/views/maintenance/data/material/materialDrawer.vue';
import skuDrawer from '#/views/sapneo/master/product/skuDrawer.vue';

import receivingModal from './ReceivingModal.vue';
import returnOBDImportModal from './ReturnOBDImportModal.vue';
import returnOBDRemoveDrawer from './ReturnOBDRemoveDrawer.vue';

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
  doc: {
    type: String,
    default: '',
  },
  inStatus: {
    type: Boolean,
    default: false,
  },
  isClosed: {
    type: Boolean,
    default: false,
  },
  owner: {
    type: String,
    default: '',
  },
});
const emit = defineEmits(['success']);
const [ReturnOBDImportModal, ReturnOBDImportModalApi] = useVbenModal({
  connectedComponent: returnOBDImportModal,
});

const [ReturnOBDRemoveDrawer, ReturnOBDRemoveDrawerApi] = useVbenDrawer({
  connectedComponent: returnOBDRemoveDrawer,
});
const [ReceivingModal, ReceivingModalApi] = useVbenModal({
  connectedComponent: receivingModal,
});

const [SkuDrawer, skuDrawerApi] = useVbenDrawer({
  connectedComponent: skuDrawer,
});
const [MaterialDrawer, materialDrawerApi] = useVbenDrawer({
  // 连接抽离的组件
  connectedComponent: materialDrawer,
});

function openReturnOBDImportModal() {
  ReturnOBDImportModalApi.setData(props);
  ReturnOBDImportModalApi.open();
}
function openReturnOBDRemoveDrawer() {
  ReturnOBDRemoveDrawerApi.setData(props);
  ReturnOBDRemoveDrawerApi.open();
}
function openReceivingModal(id: string) {
  ReceivingModalApi.setData({ id });
  ReceivingModalApi.open();
}
function openSkuDrawer(id: string) {
  skuDrawerApi.setData({ id });
  skuDrawerApi.open();
}
function openMaterialDrawer(id: string) {
  materialDrawerApi.setData({ id });
  materialDrawerApi.open();
}
const gridOptions = reactive<VxeGridProps>({
  id: 'BWInReturnItem',
  checkboxConfig: { highlight: true, range: true },
  cellStyle({ row, column }) {
    if (
      ['MissingQty', 'ReceivedQty'].includes(column.field) &&
      row.ReceivedQty !== null
    ) {
      if (row.ReceivedQty > row.EstimatedReceivedQty) {
        return {
          backgroundColor: '#ffe3e3',
        };
      } else if (row.ReceivedQty < row.EstimatedReceivedQty) {
        return {
          backgroundColor: '#fff9db',
        };
      }
    }
    if (
      ['ReceivedBatch'].includes(column.field) &&
      row.ReceivedBatch &&
      row.InboundBatch !== row.ReceivedBatch
    ) {
      return {
        backgroundColor: '#fff9db',
      };
    }
    return null;
  },
  columns: [
    { type: 'checkbox', width: 48, fixed: 'left' },
    {
      fixed: 'left',
      title: '#',
      type: 'seq',
      width: 60,
    },
    { width: 56, fixed: 'left', title: '收货', slots: { default: 'opt' } },
    {
      field: 'MatCode',
      title: '物料编号',
      width: 88,
      fixed: 'left',
      filters: [{ data: null }],
      filterRender: { name: 'FilterInput' },
      slots: { default: 'material' },
    },
    {
      field: 'ReturnPO',
      title: 'SAP销售订单',
      minWidth: 96,
    },
    {
      field: 'ReturnOBD',
      title: 'ReturnOBD',
      minWidth: 88,
      visible: false,
    },
    {
      field: 'ReturnOBDItem',
      title: 'Item',
      minWidth: 72,
      visible: false,
    },
    {
      field: 'EstimatedReceivedQty',
      title: '产品数量',
      minWidth: 80,
    },
    {
      field: 'ReceivingStatus',
      title: '收货状态',
      width: 88,
      formatter({ cellValue }) {
        return cellValue ? '已收货' : '待收货';
      },
      filters: [
        { label: '待收货', value: false },
        { label: '已收货', value: true },
      ],
      filterMultiple: false,
      slots: { default: 'rStatus' },
    },
    {
      field: 'ReturnGRStatus',
      title: 'GR',
      width: 60,
      formatter({ cellValue }) {
        return cellValue ? '是' : '否';
      },
      filters: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      filterMultiple: false,
      slots: { default: 'gStatus' },
    },
    {
      field: 'SSCC',
      title: 'SSCC',
      minWidth: 200,
      editRender: {
        name: 'AInput',
        autofocus: '',
        props: {
          size: 'small',
          autocomplete: 'off',
          maxlength: 20,
          disabled: props.isClosed,
        },
      },
    },
    {
      field: 'PalletNo',
      title: '入库托盘',
      minWidth: 88,
      /* editRender: { name: 'AInput', autofocus: '' },
      slots: { edit: 'palletno_edit' }, */
    },
    /* {
      field: 'ReceivedBoxNum',
      title: '收货箱数',
      width: 72,
    }, */
    {
      field: 'ReceivedQty',
      title: '收货数量',
      minWidth: 80,
    },
    {
      field: 'MissingQty',
      title: '少货',
      minWidth: 48,
    },
    {
      field: 'ReceivedBy',
      title: '收货人',
      width: 80,
    },
    {
      field: 'ReceivedDate',
      title: '收货时间',
      minWidth: 164,
      formatter: 'formatDateTime',
      slots: { edit: 'receiveddate_edit' },
      editRender: {
        name: 'ADatePicker',
      },
    },
    {
      field: 'Remark',
      title: '备注',
      width: 150,
      editRender: {
        name: 'AInput',
        autofocus: '',
        props: {
          disabled: props.isClosed,
          size: 'small',
          autocomplete: 'off',
          maxlength: 128,
        },
      },
    },
    {
      field: 'iCName',
      title: '中文品名',
      width: 240,
    },
    { field: 'iBarCode', title: '条形码', width: 112 },
    { field: 'iMatType', title: '物料类型', width: 80 },
    { field: 'iDivision', title: 'Division', width: 80 },
    {
      field: 'iBrandName',
      title: '品牌',
      width: 80,
    },
    { field: 'iOriginName', title: '原产国', width: 80 },
    { field: 'iSpecifaction', title: '规格', width: 80 },
    { field: 'iPCB', title: 'PCB', width: 72 },
  ],
  editConfig: {
    trigger: 'dblclick',
    mode: 'row',
    showIcon: true,
    showStatus: true,
    /* icon: 'fa fa-pencil', */
    /* beforeEditMethod: ({ row, column }) => {
      if (props.isClosed) {
        return false;
      }
      if (
        row.ReceivingStatus &&
        ['ReceivedBy', 'ReceivedDate', 'Remark'].includes(column.field)
      ) {
        return true;
      }
      if (!row.ReceivingStatus && ['Invoice', 'SSCC'].includes(column.field)) {
        return true;
      }
      return false;
    }, */
  },
  filterConfig: {
    remote: false,
  },
  footerRowStyle: { 'font-weight': 'bold ' },
  footerMethod({ columns, data }) {
    return [
      columns.map((column, columnIndex) => {
        if (columnIndex === 0) {
          return `合计`;
        }
        if (columnIndex === 1) {
          return `${data.length} 项`;
        }
        if (
          [
            'EstimatedReceivedQty',
            'MissingQty',
            'ReceivedBoxNum',
            'ReceivedQty',
          ].includes(column.field)
        ) {
          return sumNum(data, column.field);
        }
        if (['ReceivingStatus'].includes(column.field)) {
          const status = [
            ...new Set(
              data
                .map((item) => item.ReceivingStatus)
                .filter((item) => item !== ''),
            ),
          ];
          if (status.length === 1) {
            return `${status[0] ? '已收货' : '待收货'}`;
          } else if (status.length > 1) {
            return `部分入库`;
          }
        }
        if (['PalletNo'].includes(column.field)) {
          const traynos = [
            ...new Set(
              data.map((item) => item.PalletNo).filter((item) => item !== ''),
            ),
          ];
          return `托盘数: ${traynos.length}`;
        }
        return null;
      }),
    ];
  },
  height: 'auto',
  keepSource: true,
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    ajax: {
      query: async () => {
        const res = await getInReturnItem(props.id);
        return res.data;
      },
    },
    seq: true,
  },
  showFooter: true,
  size: 'mini',
  sortConfig: {
    remote: false,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
  scrollX: { enabled: true, gt: 0 },
  scrollY: { enabled: true, mode: 'wheel', gt: 30, oSize: 0 },
  menuConfig: {
    trigger: 'cell',
    body: {
      options: [
        [
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuCopy',
            disabled: false,
            name: '复制单元格',
            prefixConfig: {
              icon: 'vxe-icon-copy',
            },
            visible: true,
          },
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuRefresh',
            disabled: false,
            name: '刷新',
            prefixConfig: { icon: 'vxe-icon-refresh' },
            visible: true,
          },
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuExport',
            disabled: false,
            name: '导出明细',
            prefixConfig: { icon: 'vxe-icon-download' },
            visible: true,
          },
        ],
      ],
    },
    className: 'font-medium shadow rounded-md',
  },
});
const gridEvents: VxeGridListeners = {
  editClosed({ row, rowIndex }) {
    handleUpdateByRow(row, rowIndex);
  },
  menuClick({ menu }) {
    switch (menu.code) {
      case 'menuExport': {
        handleExport();
        break;
      }
      default: {
        break;
      }
    }
  },
};

const [Grid, gridApi] = useVbenVxeGrid({ gridOptions, gridEvents });

function handleExport() {
  gridApi.grid.exportData({
    filename: `退货订单明细_${props.doc}`,
    original: false,
    sheetName: 'Sheet1',
    type: 'xlsx',
    isFooter: false,
    columnFilterMethod: ({ column }) => {
      return column.field !== undefined;
    },
  });
}

function sumNum(list: any[], field: string) {
  let count = 0;
  list.forEach((item) => {
    count += Number(item[field]);
  });
  return count;
}
/* function openCreateModal() {
  createModalApi.open();
} */

function handleSuccess(isUpdate: boolean) {
  if (isUpdate) {
    emit('success');
  } else {
    gridApi.grid.commitProxy('query');
  }
}

function handleUpdateByRow(row: any, rowIndex: number) {
  if (gridApi.grid.isUpdateByRow(row)) {
    gridApi.setLoading(true);
    bwInReturnItemUpdate(row)
      .then(() => {
        message.success(`序号${rowIndex + 1} 保存成功！ `);
        handleSuccess(false);
      })
      .catch(() => {
        gridApi.grid.revertData(row);
      })
      .finally(() => {
        gridApi.setLoading(false);
      });
  }
}
function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
}
function operation() {
  const checkedRecords = gridApi.grid
    .getCheckboxRecords()
    .map((item: any) => item.Guid);
  bwInReturnReveivingCancel(props.id, checkedRecords)
    .then(() => {
      handleSuccess(false);
    })
    .catch(() => {})
    .finally(() => {});
}
function handleCancel() {
  const checkedRecords = gridApi.grid
    .getCheckboxRecords()
    .map((item: any) => item.Guid);
  if (checkedRecords.length === 0) {
    message.info(`请勾选产品明细`);
    return;
  }
  Modal.confirm({
    content: `撤销将清除收货信息；若已质检，无法直接撤销`,
    onCancel() {},
    onOk() {
      operation();
    },
    title: `撤销收货操作`,
    okButtonProps: { danger: true },
  });
}
</script>

<template>
  <div class="flex h-full flex-col">
    <Grid>
      <template #toolbar_left>
        <div class="hidden pl-1 text-base font-medium md:block">
          <span class="mr-2">产品明细</span>
        </div>
        <a-button class="mr-1" size="small" @click="clearFilterAndSort()">
          <AntClear class="size-5" />
        </a-button>
        <div class="hidden md:block">
          <a-button
            :disabled="props.isClosed"
            class="ml-2"
            size="small"
            type="primary"
            @click="openReturnOBDImportModal()"
          >
            Return OBD 绑定
          </a-button>
          <a-button
            :disabled="props.isClosed"
            class="ml-2"
            size="small"
            @click="openReturnOBDRemoveDrawer()"
          >
            Return OBD 移除
          </a-button>
        </div>
      </template>
      <template #toolbar-tools>
        <a-button size="small" @click="handleCancel()">撤销收货</a-button>
      </template>
      <template #opt="{ row }">
        <a-button
          :disabled="row.ReceivingStatus || !props.inStatus"
          class="!pl-1 !pr-1"
          size="small"
          type="link"
          @click="openReceivingModal(row.Guid)"
        >
          <AntSandBox class="size-6 pt-1" />
        </a-button>
      </template>
      <template #rStatus="{ row }">
        <Tag :color="row.ReceivingStatus ? 'green' : 'red'">
          {{ row.ReceivingStatus ? '已收货' : '待收货' }}
        </Tag>
      </template>
      <template #gStatus="{ row }">
        <Tag :color="row.ReturnGRStatus ? 'green' : 'red'" class="mr-0">
          {{ row.ReturnGRStatus ? '是' : '否' }}
        </Tag>
      </template>
      <template #material="{ row }">
        <Popconfirm
          cancel-text="产品主数据"
          ok-text="基础物料"
          title="数据展示"
          @cancel="openSkuDrawer(row.MatCode)"
          @confirm="openMaterialDrawer(row.MatCode)"
        >
          <a class="text-blue-500" @click.prevent>
            {{ row.MatCode }}
          </a>
        </Popconfirm>
      </template>
      <template #receiveddate_edit="{ row }">
        <DatePicker
          v-model:value="row.ReceivedDate"
          :disabled="row.ReturnGRStatus || !row.ReceivingStatus"
          :show-time="true"
          size="small"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </template>
    </Grid>
    <ReturnOBDImportModal @success="handleSuccess(true)" />
    <ReturnOBDRemoveDrawer @success="handleSuccess(true)" />
    <ReceivingModal @success="handleSuccess(false)" />
    <MaterialDrawer @success="handleSuccess(false)" />
    <SkuDrawer />
  </div>
</template>
