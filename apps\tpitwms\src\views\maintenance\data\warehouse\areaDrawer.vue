<script lang="ts" setup>
import { ref } from 'vue';

import { Fallback, useVbenDrawer } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm, z } from '#/adapter';
import { areaUpdate, getAreaData } from '#/api/maintenance/data';

const emit = defineEmits(['success']);

const data = ref();

const isNotFound = ref(false);
const [Form, baseFormApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  // 提交函数
  handleSubmit: onSubmit,
  // 垂直布局，label和input在不同行，值为vertical
  // 水平布局，label和input在同一行
  layout: 'horizontal',
  schema: [
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'IsProvisional',
      label: 'IsProvisional',
      dependencies: {
        show: false,
        triggerFields: ['A'],
      },
    },
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 16,
        placeholder: '请输入',
      },
      fieldName: 'WarehouseName',
      label: '仓库名称',
      rules: 'required',
      disabled: true,
    },
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 16,
        placeholder: '请输入',
      },
      fieldName: 'AreaTypeName',
      label: '库区类型',
      rules: 'required',
      disabled: true,
    },
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 16,
        placeholder: '请输入',
      },
      fieldName: 'AreaName',
      help: '创建后无法修改',
      label: '库区名称',
      rules: z.string().min(2, { message: '库区名称至少包含2个字符' }),
    },
    {
      component: 'RadioGroup',
      componentProps: {
        options: [
          {
            label: '启用',
            value: true,
          },
          {
            label: '停用',
            value: false,
          },
        ],
      },
      defaultValue: true,
      fieldName: 'IsActived',
      label: '启用状态',
      rules: 'required',
      dependencies: {
        disabled(values) {
          return values.IsProvisional;
        },
        triggerFields: ['IsProvisional'],
      },
    },
    {
      component: 'Switch',
      componentProps: {
        class: 'w-auto',
        checkedChildren: '是',
        unCheckedChildren: '否',
      },
      defaultValue: false,
      fieldName: 'IsDefault',
      label: '是否默认',
    },
    {
      component: 'Textarea',
      componentProps: {
        autocomplete: 'off',
        maxlength: 128,
        placeholder: '请输入',
      },
      fieldName: 'Remark',
      label: '备注',
    },
  ],
  showDefaultActions: false,
  wrapperClass: 'grid-cols-1',
});
const [Drawer, drawerApi] = useVbenDrawer({
  onCancel() {
    drawerApi.close();
  },
  onConfirm() {
    /* console.log(baseFormApi.validate()); */
    baseFormApi.submitForm();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      isNotFound.value = false;
      data.value = drawerApi.getData<Record<string, any>>();
      baseFormApi.updateSchema([
        {
          componentProps: {
            disabled: !(data.value.id === 'new'),
          },
          fieldName: 'AreaName',
        },
      ]);
      handleGetData(data.value.id);
    }
  },
});
function handleGetData(id: string) {
  drawerApi.setState({ loading: true });
  getAreaData(id)
    .then((res) => {
      if (data.value.id === 'new') {
        drawerApi.setState({ title: '新增库区' });
        baseFormApi.resetForm();
      } else {
        drawerApi.setState({ title: '编辑库区' });
        baseFormApi.setValues(res);
      }
      baseFormApi.setFieldValue(
        'WarehouseName',
        data.value.info?.WarehouseName,
      );
      baseFormApi.setFieldValue('AreaTypeName', data.value.info?.AreaTypeName);
    })
    .catch(() => {
      isNotFound.value = true;
    })
    .finally(() => {
      drawerApi.setState({ loading: false });
    });
}
async function onSubmit(values: Record<string, any>) {
  const check = await baseFormApi.validate();
  if (check.valid) {
    drawerApi.setState({ confirmLoading: true });
    drawerApi.setState({ loading: true });
    areaUpdate(Object.assign({ id: data.value.id }, values, data.value.info))
      .then(() => {
        message.success('操作成功');
        drawerApi.close();
        emit('success');
      })
      .catch(() => {})
      .finally(() => {
        drawerApi.setState({ loading: false });
        drawerApi.setState({ confirmLoading: false });
      });
  }
}
</script>
<template>
  <Drawer>
    <Fallback v-if="isNotFound" :show-back="false" status="404" />
    <Form v-else />
  </Drawer>
</template>

<style scoped lang="less">
::v-deep(.ant-input[disabled]) {
  color: rgb(0 0 0 / 70%);
}
::v-deep(input[disabled]) {
  color: rgb(0 0 0 / 70%) !important;
}
</style>
