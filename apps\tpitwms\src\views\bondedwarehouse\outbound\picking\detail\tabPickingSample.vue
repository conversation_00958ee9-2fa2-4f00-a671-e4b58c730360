<script setup lang="ts">
import type { VxeGridListeners, VxeGridProps } from '#/adapter';

import { defineProps, reactive, ref } from 'vue';
import { useRouter } from 'vue-router';

import { useVbenDrawer, useVbenModal, VbenLoading } from '@vben/common-ui';
import { AntClear } from '@vben/icons';

import { message, Modal, Popconfirm, Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import {
  bwOutPickingItemStatusUpdate,
  bwOutPickingItemUpdate,
  bwOutPickingSampleOutboundUnBinding,
  getOutPickingItem,
} from '#/api/bondedwarehouse/outbound';
import batchDrawer from '#/views/sapneo/master/batch/batchDrawer.vue';
import skuDrawer from '#/views/sapneo/master/product/skuDrawer.vue';

import pickingModal from './pickingModalSample.vue';
import sampleOBDImportModal from './sampleOBDImportModal.vue';

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
  isClosed: {
    type: Boolean,
    default: false,
  },
  doc: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['success']);
const loadingRef = ref(false);
const router = useRouter();
/* const allBrands = ref([]) as any; */
const allDivisions = ref([]) as any;
/* const processingTypeOptions = ref([]); */

const [SkuDrawer, skuDrawerApi] = useVbenDrawer({
  connectedComponent: skuDrawer,
});
const [BatchDrawer, batchDrawerApi] = useVbenDrawer({
  connectedComponent: batchDrawer,
});

const [PickingModal, pickingModalApi] = useVbenModal({
  connectedComponent: pickingModal,
});

const [SampleOBDImportModal, sampleOBDImportModalApi] = useVbenModal({
  connectedComponent: sampleOBDImportModal,
});
function openSkuDrawer(id: string) {
  skuDrawerApi.setData({ id });
  skuDrawerApi.open();
}

function openBatchDrawer(sku: string, batch: string) {
  batchDrawerApi.setData({ sku, batch });
  batchDrawerApi.open();
}
function openPickingModal() {
  pickingModalApi.setData({ id: props.id });
  pickingModalApi.open();
}
function openSampleOBDImportModal(row: any) {
  sampleOBDImportModalApi.setData(row);
  sampleOBDImportModalApi.open();
}

const gridOptions = reactive<VxeGridProps>({
  id: 'BWOutPickingItemSample',
  checkboxConfig: { highlight: true, range: true },
  columns: [
    { type: 'checkbox', width: 48, fixed: 'left' },
    {
      fixed: 'left',
      title: '#',
      type: 'seq',
      width: 60,
    },
    {
      field: 'MatCode',
      title: '物料编号',
      width: 88,
      fixed: 'left',
      filters: [{ data: { vals: [], sVal: '' } }],
      filterRender: {
        name: 'FilterContent',
      },
      slots: { default: 'material' },
    },
    {
      field: 'BatchNo',
      fixed: 'left',
      title: '拣货批号',
      width: 80,
      filters: [{ data: { vals: [], sVal: '' } }],
      filterRender: {
        name: 'FilterContent',
      },
      slots: { default: 'batchno' },
    },
    {
      field: 'PackagingStatus',
      title: '打包状态',
      width: 84,
      slots: { default: 'packstatus' },
    },
    {
      field: 'LoadingStatus',
      title: '装车状态',
      width: 84,
      slots: { default: 'loadstatus' },
    },
    {
      field: 'DeliveryStatus',
      title: '发货状态',
      width: 84,
      slots: { default: 'deliverystatus' },
    },
    {
      field: 'PickingQty',
      title: '拣货数量',
      width: 88,
    },
    {
      field: 'PickingDate',
      title: '拣货日期',
      width: 136,
    },
    {
      field: 'PickingBy',
      title: '拣货人',
      width: 80,
    },
    {
      field: 'SampleBoxNo',
      title: '装箱流水号',
      width: 88,
    },
    {
      field: 'YUB',
      title: 'SAP销售订单',
      width: 96,
    },
    {
      field: 'Outbound',
      title: 'Outbound',
      width: 150,
      editRender: { name: 'AInput', autofocus: '' },
      slots: { edit: 'obd_edit' },
    },
    /* {
      field: 'OriginalPalletNo',
      title: '入库托盘号',
      width: 88,
    }, */
    {
      field: 'OriginalSSCC',
      title: '入库SSCC',
      width: 140,
    },
    {
      field: 'NewPalletNo',
      title: '出库托盘号',
      width: 108,
    },
    {
      field: 'NewSSCC',
      title: '出库基准SSCC',
      width: 140,
    },
    {
      field: 'PalletType',
      title: '托盘规格',
      width: 80,
    },
    {
      field: 'LoadingDispatch',
      title: '派车单号',
      width: 160,
      slots: { default: 'dispatch' },
    },
  ],
  editConfig: {
    trigger: 'dblclick',
    mode: 'row',
    showIcon: true,
    showStatus: true,
  },
  filterConfig: {
    remote: false,
  },
  footerRowStyle: { 'font-weight': 'bold ' },
  footerMethod({ columns, data }) {
    return [
      columns.map((column, columnIndex) => {
        if (columnIndex === 0) {
          return `合计`;
        }
        if (columnIndex === 1) {
          return `${data.length} 项`;
        }
        if (['GoodsNum', 'PickingQty'].includes(column.field)) {
          return sumNum(data, column.field);
        }
        /* if (['PackagingStatus'].includes(column.field)) {
          const status = [
            ...new Set(
              data
                .map((item) => item.PackagingStatus)
                .filter((item) => item !== ''),
            ),
          ];
          if (status.length === 1) {
            return `${status[0] ? '已打包' : '待打包'}`;
          } else if (status.length > 1) {
            return `部分打包`;
          }
        } */
        /* if (['PalletNo'].includes(column.field)) {
          const traynos = [
            ...new Set(
              data.map((item) => item.PalletNo).filter((item) => item !== ''),
            ),
          ];
          return `托盘数: ${traynos.length}`;
        } */
        if (['oDivision'].includes(column.field)) {
          allDivisions.value = [
            ...new Set(
              data.map((item) => item.oDivision).filter((item) => item !== ''),
            ),
          ];
        }
        return null;
      }),
    ];
  },
  height: 'auto',
  keepSource: true,
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    ajax: {
      query: async (/* { $grid } */) => {
        const res = await getOutPickingItem(props.id);
        /* processingTypeOptions.value = res.options.filter((item: any) => {
          return item.type === 'ProcessingType';
        }); */
        /* allBrands.value = res.allBrands;
        const oBrandColumn = $grid.getColumnByField('oBrandName');
        if (oBrandColumn) {
          $grid.setFilter(oBrandColumn, allBrands.value);
        } */
        return res.data;
      },
    },
    seq: true,
  },
  showFooter: true,
  size: 'mini',
  sortConfig: {
    remote: false,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
  scrollX: { enabled: true, gt: 0 },
  scrollY: { enabled: true, mode: 'wheel', gt: 30, oSize: 0 },
  menuConfig: {
    trigger: 'cell',
    body: {
      options: [
        [
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuCopy',
            disabled: false,
            name: '复制单元格',
            prefixConfig: {
              icon: 'vxe-icon-copy',
            },
            visible: true,
          },
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuRefresh',
            disabled: false,
            name: '刷新',
            prefixConfig: { icon: 'vxe-icon-refresh' },
            visible: true,
          },
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuExport',
            disabled: false,
            name: '导出明细',
            prefixConfig: { icon: 'vxe-icon-download' },
            visible: true,
          },
        ],
      ],
    },
    className: 'font-medium shadow rounded-md',
  },
});
const gridEvents: VxeGridListeners = {
  editClosed({ row, rowIndex }) {
    handleUpdateByRow(row, rowIndex);
  },
  menuClick({ menu }) {
    switch (menu.code) {
      case 'menuExport': {
        handleExport();
        break;
      }
      default: {
        break;
      }
    }
  },
};

const [Grid, gridApi] = useVbenVxeGrid({ gridOptions, gridEvents });

function handleExport() {
  gridApi.grid.exportData({
    filename: `出库拣货明细_${props.doc}`,
    sheetName: 'Sheet1',
    type: 'xlsx',
    isFooter: false,
    columnFilterMethod: ({ column }) => {
      return column.field !== undefined;
    },
  });
}

function sumNum(list: any[], field: string) {
  let count = 0;
  list.forEach((item) => {
    count += Number(item[field]);
  });
  return count;
}

function handleSuccess(isUpdate: boolean) {
  if (isUpdate) {
    emit('success');
  } else {
    gridApi.grid.commitProxy('query');
  }
}
/* function handleUpdateHeader() {
  emit('success');
} */

function handleUpdateByRow(row: any, rowIndex: number) {
  if (gridApi.grid.isUpdateByRow(row)) {
    gridApi.setLoading(true);
    bwOutPickingItemUpdate(row)
      .then(() => {
        message.success(`序号${rowIndex + 1} 保存成功！ `);
        handleSuccess(false);
      })
      .catch(() => {
        gridApi.grid.revertData(row);
      })
      .finally(() => {
        gridApi.setLoading(false);
      });
  }
}
function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
}
function handleMultiOperate(typ: number) {
  const checkedRecords = gridApi.grid
    .getCheckboxRecords()
    .map((item: any) => item.Guid);
  if (checkedRecords.length === 0) {
    message.info(`请勾选产品明细`);
    return;
  }
  loadingRef.value = true;
  bwOutPickingItemStatusUpdate(props.id, checkedRecords, typ)
    .then(() => {
      message.success('操作成功！');
      handleSuccess(false);
    })
    .catch(() => {})
    .finally(() => {
      loadingRef.value = false;
    });
}
function unBinding(ids: any) {
  loadingRef.value = true;
  bwOutPickingSampleOutboundUnBinding(props.id, ids)
    .then(() => {
      message.success('操作成功！');
      handleSuccess(false);
    })
    .catch(() => {})
    .finally(() => {
      loadingRef.value = false;
    });
}
function handleRemoveOBD() {
  const checkedRecords = gridApi.grid
    .getCheckboxRecords()
    .map((item: any) => item.Guid);
  if (checkedRecords.length === 0) {
    message.info(`请勾选需要解除绑定的拣货明细`);
    return;
  }
  Modal.confirm({
    content: `请确认是否解除勾选留样拣货明细的Outbound绑定？未选中项若存在相同的OBD，将被同时解绑！`,
    /* icon: createVNode(ExclamationCircleOutlined), */
    onCancel() {},
    onOk() {
      unBinding(checkedRecords);
    },
    title: `解除OBD绑定`,
    okButtonProps: { danger: true },
  });
}
</script>

<template>
  <div class="flex h-full flex-col">
    <Grid>
      <template #toolbar_left>
        <div class="hidden pl-1 text-base font-medium md:block">
          <span class="mr-2">拣货明细</span>
        </div>
        <a-button class="mr-1" size="small" @click="clearFilterAndSort()">
          <AntClear class="size-5" />
        </a-button>
        <div class="hidden md:block">
          <a-button
            :disabled="props.isClosed"
            class="ml-2"
            size="small"
            type="primary"
            @click="openPickingModal()"
          >
            拣货作业
          </a-button>
        </div>
      </template>
      <template #toolbar-tools>
        <a-button
          :loading="loadingRef"
          :disabled="props.isClosed"
          class="mr-2"
          size="small"
          type="primary"
          @click="handleMultiOperate(1)"
        >
          确认打包
        </a-button>
        <a-button
          :loading="loadingRef"
          :disabled="props.isClosed"
          class="mr-2"
          size="small"
          @click="handleMultiOperate(0)"
        >
          撤销打包
        </a-button>
        <a-button
          :loading="loadingRef"
          :disabled="props.isClosed"
          danger
          size="small"
          @click="handleRemoveOBD()"
        >
          解除留样OBD绑定
        </a-button>
      </template>
      <template #deliverystatus="{ row }">
        <Tag :color="row.DeliveryStatus ? 'green' : 'red'">
          {{ row.DeliveryStatus ? '已发货' : '待发货' }}
        </Tag>
      </template>
      <template #packstatus="{ row }">
        <Tag :color="row.PackagingStatus ? 'green' : 'red'">
          {{ row.PackagingStatus ? '已打包' : '待打包' }}
        </Tag>
      </template>
      <template #loadstatus="{ row }">
        <Tag :color="row.LoadingStatus ? 'green' : 'red'">
          {{ row.LoadingStatus ? '已装车' : '待装车' }}
        </Tag>
      </template>
      <template #material="{ row }">
        <a
          v-if="row.MatCode"
          class="text-blue-500"
          @click="openSkuDrawer(row.MatCode)"
        >
          {{ row.MatCode }}
        </a>
      </template>
      <template #batchno="{ row }">
        <a
          v-if="row.BatchNo"
          class="text-blue-500"
          @click="openBatchDrawer(row.MatCode, row.BatchNo)"
        >
          {{ row.BatchNo }}
        </a>
      </template>
      <template #dispatch="{ row }">
        <Popconfirm
          cancel-text="取消"
          ok-text="查看派车单"
          title="跳转提示"
          @confirm="
            router.push({
              name: 'BWOutDispatchDetail',
              params: { id: row.LoadingDispatchID },
            })
          "
        >
          <a class="text-blue-500" @click.prevent>
            {{ row.LoadingDispatch }}
          </a>
        </Popconfirm>
      </template>
      <template #obd_edit="{ row }">
        <a-input-search
          size="small"
          :maxlength="16"
          :style="{ height: '28px' }"
          autocomplete="off"
          v-model:value="row.Outbound"
          enter-button
          readonly
          @search="openSampleOBDImportModal(row)"
          :disabled="row.DeliveryStatus || row.Outbound"
        />
      </template>
    </Grid>
    <PickingModal @success="handleSuccess" />
    <SampleOBDImportModal @success="handleSuccess" />
    <SkuDrawer />
    <BatchDrawer />
    <VbenLoading :spinning="loadingRef" />
  </div>
</template>
