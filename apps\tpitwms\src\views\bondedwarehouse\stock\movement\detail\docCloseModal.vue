<script lang="ts" setup>
import { ref } from 'vue';

import { useVbenModal, VbenLoading } from '@vben/common-ui';

import { Descriptions, message, Result, Tag } from 'ant-design-vue';
import dayjs from 'dayjs';

import { useVbenForm } from '#/adapter';
import {
  stockMovementExecute,
  stockMovementExecuteCheck,
} from '#/api/bondedwarehouse/stock';

const emit = defineEmits(['success']);

const data = ref();
const isTaskCreated = ref(false);
const loadingRef = ref(false);
/* const movementID = ref(undefined); */
const movementData = ref({}) as any;
const [Form, formApi] = useVbenForm({
  handleSubmit: onSubmit,
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
    labelClass: 'w-24',
  },
  schema: [
    {
      component: 'DatePicker',
      componentProps: {
        style: { width: '100%' },
        valueFormat: 'YYYY-MM-DD',
      },
      fieldName: 'PostingDate',
      label: '记账日期',
      rules: 'required',
      help: '默认当天，若无特殊情况无需修改！',
    },
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 128,
      },
      fieldName: 'Remark',
      label: '调整备注',
      rules: 'required',
      help: '请备注此单据的调整原因',
    },
  ],
  showDefaultActions: false,
  wrapperClass: 'grid-cols-1',
});

const [BaseModal, modalApi] = useVbenModal({
  class: 'w-[640px]',
  fullscreenButton: false,
  closeOnClickModal: false,
  zIndex: 900,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await formApi.submitForm();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      isTaskCreated.value = false;
      loadingRef.value = true;
      modalApi.setState({ showConfirmButton: false });
      movementData.value = {};
      data.value = modalApi.getData<Record<string, any>>();
      formApi.resetForm();
      formApi.setValues({ PostingDate: dayjs().format() });
      fetch();
    }
  },
  title: '库存调整单 执行确认',
});

async function onSubmit(values: Record<string, any>) {
  const check = await formApi.validate();
  if (check.valid) {
    modalApi.setState({ confirmLoading: true });
    modalApi.setState({ loading: true });
    stockMovementExecute({
      id: data.value.id,
      date: values.PostingDate,
      rmk: values.Remark,
    })
      .then((res) => {
        isTaskCreated.value = res;
        if (isTaskCreated.value) {
          modalApi.setState({ showCancelButton: false });
          modalApi.setState({ showConfirmButton: false });
          modalApi.close();
          message.success('调整单据执行成功！');
        } else {
          message.error('调整单据执行失败，请联系管理员！');
        }
      })
      .catch(() => {})
      .finally(() => {
        modalApi.setState({ loading: false });
        modalApi.setState({ confirmLoading: false });
        emit('success');
      });
  }
}

/* function handleClose() {
  modalApi.close();
  emit('success');
} */
async function fetch() {
  await stockMovementExecuteCheck(data.value.id)
    .then((res) => {
      if (res.IsClosed) {
        message.error('调整单据已关闭，请刷新查看！');
      } else {
        modalApi.setState({ showConfirmButton: true });
      }
      movementData.value = res;
      formApi.setValues({ Remark: res.Remark });
    })
    .catch(() => {})
    .finally(() => {
      loadingRef.value = false;
    });
}
</script>
<template>
  <BaseModal>
    <Result v-if="isTaskCreated" status="success" title="调整单据执行成功">
      <template #extra>
        <a-button @click="modalApi.close()">关闭</a-button>
      </template>
    </Result>
    <div v-else>
      <Form />
      <Descriptions :column="1" bordered size="small">
        <Descriptions.Item label="库存调整单号">
          {{ movementData.DocNo }}
        </Descriptions.Item>
        <Descriptions.Item label="调整类型">
          {{ movementData.MovementTypes }}
        </Descriptions.Item>
        <Descriptions.Item label="调整产品数量">
          {{ movementData.TotalMoveQty }}
        </Descriptions.Item>
        <Descriptions.Item label="是否需要回传">
          <Tag
            v-if="!loadingRef"
            :bordered="false"
            :color="movementData.APIExecuteStatus === 0 ? 'blue' : 'green'"
          >
            {{ movementData.APIExecuteStatus === 0 ? '无需回传' : '需要回传' }}
          </Tag>
        </Descriptions.Item>
      </Descriptions>
      <VbenLoading :spinning="loadingRef" />
    </div>
  </BaseModal>
</template>
