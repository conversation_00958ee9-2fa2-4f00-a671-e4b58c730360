import type { RequestResponse } from '@vben/request';

import { requestClient, sleep } from '#/api/request';

/**
 * Web通用API
 */

export async function getOptions(type: string) {
  return requestClient.get('/common/getOptions', { params: { type } });
}

export async function getWebColumn(id: string) {
  return requestClient.get('/common/getWebColumn', { params: { id } });
}

export async function setWebColumn(params: object) {
  return requestClient.post('/common/setWebColumn', params);
}

export async function downloadCheckFile(type: string, id: string) {
  return requestClient.get('/common/download/checkFile', {
    params: { type, id },
  });
}

export async function downloadGetFile(type: string, id: string) {
  return requestClient.get('/common/download/getFile', {
    params: { type, id },
    responseType: 'blob',
  });
}

export async function createFile(typ: string, id: string) {
  return requestClient.download<RequestResponse<Blob>>('/common/createFile', {
    params: { typ, id },
    responseReturn: 'raw',
  });
  /* return requestClient.post('/common/createFile', {
    typ,
    id,
  }); */
}

export async function addExportRecord(params: object) {
  return requestClient.post('/common/addExportRecord', params);
}

export async function exportFile(id: string) {
  return requestClient.download<RequestResponse<Blob>>('/common/exportFile', {
    params: { id },
    responseReturn: 'raw',
  });
}

/**
 * 下载文件，获取Blob
 * @returns Blob
 */
export async function downloadTemplateFile(typ: string) {
  return requestClient.download<Blob>('/common/download/template/file', {
    params: { typ },
  });
}

export async function getOverviewItems() {
  await sleep(100);
  return requestClient.get('/common/dashboard/getOverviewItems');
}

export async function getTrends4Received() {
  await sleep(100);
  return requestClient.get('/common/dashboard/getTrends4Received');
}

export async function getTrends4Event() {
  await sleep(100);
  return requestClient.get('/common/dashboard/getTrends4Event');
}
