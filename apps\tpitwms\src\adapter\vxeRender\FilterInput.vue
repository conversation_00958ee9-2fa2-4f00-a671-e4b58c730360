<script lang="ts" setup>
import type { PropType } from 'vue';

import type {
  VxeGlobalRendererHandles,
  VxeTableDefines,
} from '@vben/plugins/vxe-table';

import { ref, watch } from 'vue';

const props = defineProps({
  params: {
    default: () => ({}),
    type: Object as PropType<VxeGlobalRendererHandles.RenderTableFilterParams>,
  },
});

const currOption = ref<VxeTableDefines.FilterOption>();

const load = () => {
  const { params } = props;
  if (params) {
    const { column } = params;
    const option = column.filters[0];
    currOption.value = option;
  }
};

const changeOptionEvent = () => {
  const { params } = props;
  const option = currOption.value;
  if (params && option) {
    option.value = option.data;
    const { $panel } = params;
    const checked = !!option.data;
    $panel.changeOption(null, checked, option);
  }
};

const keyupEvent = (e: any) => {
  const { params } = props;
  if (params) {
    const { $panel } = params;
    $panel.confirmFilter(e);
  }
};

watch(() => {
  const { column } = props.params || {};
  return column;
}, load);

load();
</script>

<template>
  <div v-if="currOption" class="my-filter-input">
    <a-input
      v-model:value="currOption.data"
      mode="text"
      placeholder="请输入模糊查询内容"
      @input="changeOptionEvent"
      @press-enter="keyupEvent"
    />
  </div>
</template>

<style scoped>
.my-filter-input {
  padding: 10px;
}
</style>
