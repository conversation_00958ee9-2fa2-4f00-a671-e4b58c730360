<script lang="ts" setup>
import type { VxeGridListeners, VxeGridProps } from '#/adapter';

import { reactive, ref } from 'vue';
/* import { AntClear } from '@vben/icons'; */

import { useVbenDrawer, useVbenModal } from '@vben/common-ui';

import { message, Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import { bwOutPickingSampleOutboundBinding } from '#/api/bondedwarehouse/outbound';
import { getOutboundItem, getOutboundList } from '#/api/sapneo';
import skuDrawer from '#/views/sapneo/master/product/skuDrawer.vue';

const emit = defineEmits(['success']);
const [SkuDrawer, skuDrawerApi] = useVbenDrawer({
  connectedComponent: skuDrawer,
});
function openSkuDrawer(id: string) {
  skuDrawerApi.setData({ id });
  skuDrawerApi.open();
}
const data = ref();
const [Modal, modalApi] = useVbenModal({
  class: 'w-full h-full',
  fullscreenButton: true,
  closeOnClickModal: false,
  confirmText: '确认绑定',
  destroyOnClose: true,
  zIndex: 900,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await handleImport();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      data.value = modalApi.getData<Record<string, any>>();
      fetch();
    }
  },
  title: '选择绑定Outbound',
});
const gridOptions = reactive<VxeGridProps>({
  id: 'SampleOutboundImport',
  radioConfig: {
    checkMethod: ({ row }) => {
      return row.WMSID === null || row.WMSID === '';
    },
  },
  columns: [
    { type: 'radio', width: 40 },
    { type: 'expand', width: 48, slots: { content: 'expand_content' } },
    {
      field: 'DeliveryDocument',
      title: 'Outbound',
      minWidth: 100,
    },
    {
      field: 'DeliveryDocumentType',
      title: '单据类型',
      minWidth: 96,
    },
    {
      field: 'SO',
      title: '销售订单',
      minWidth: 88,
    },
    {
      field: 'BusinessPartnerName1',
      title: '收货人',
      minWidth: 136,
    },
    {
      field: 'TotalQty',
      title: '总件数',
      minWidth: 88,
    },
    {
      field: 'CreateDate',
      title: '创建日期',
      minWidth: 136,
      formatter: 'formatDateTime',
      sortable: true,
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
  ],
  expandConfig: {
    accordion: true,
  },
  filterConfig: {
    remote: true,
  },
  height: 'auto',
  proxyConfig: {
    autoLoad: false,
    ajax: {
      query: ({ page, sorts }) => {
        const queryParams: any = Object.assign(
          { inUsed: 0, YUB: data.value.YUB },
          page,
        );
        // 处理排序条件
        if (sorts.length === 0) {
          queryParams.sort = '_Identify desc';
        } else {
          const sortItem = sorts.map((item) => {
            const newItem = `${item.field} ${item.order}`;
            return newItem;
          });
          queryParams.sort = sortItem.join(',');
        }
        return getOutboundList(queryParams);
      },
    },
    seq: true,
  },
  size: 'mini',
  sortConfig: {
    remote: true,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
});
const itemGridOptions = reactive<VxeGridProps>({
  columns: [
    { type: 'seq', width: 48 },
    {
      field: 'DeliveryDocumentItem',
      title: '项号',
      width: 88,
    },
    { field: 'Plant', title: '工厂编码', width: 72 },
    {
      field: 'StorageLocation',
      title: '库存地点',
      width: 72,
    },

    {
      field: 'Material',
      title: '产品编号',
      width: 100,
      slots: { default: 'skucode' },
    },
    {
      field: 'MaterialIsBatchManaged',
      title: '批次管理',
      width: 72,
      align: 'center',
      slots: { default: 'batchmanage' },
    },
    { field: 'Batch', title: '批次', width: 64 },
    { field: 'DeliveryQuantity', title: '指令数量', minWidth: 72 },
    { field: 'PickUpQty', title: '拣货数量', minWidth: 72 },
    { field: 'DeliveryQuantityUnit', title: '单位', width: 48 },
    { field: 'ItemGrossWeight', title: '毛重', width: 80 },
    { field: 'ItemNetWeight', title: '净重', width: 80 },
    { field: 'ItemWeightUnit', title: '重量单位', width: 72 },
    { field: 'ItemVolume', title: '体积', width: 80 },
    { field: 'ItemVolumeUnit', title: '体积单位', width: 72 },
    { field: 'DistributionChannel', title: '分销渠道', width: 72 },
    { field: 'Division', title: 'Division', width: 72 },
    { field: 'GoodsMovementType', title: '移动类型', width: 72 },
    { field: 'ReferenceSDDocument', title: '销售订单', width: 88 },
    { field: 'ReferenceSDDocumentItem', title: '销售项号', width: 72 },
    {
      field: 'ZZ1_Customer_referenc1_DLI',
      title: '采购订单',
      minWidth: 108,
    },
    {
      field: 'ZZ1_Customer_PO_Type_DLI',
      title: '直流订单',
      width: 72,
    },
  ],
  customConfig: {
    storage: false,
  },
  menuConfig: {
    body: {
      options: [
        [
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuCopy',
            disabled: false,
            name: '复制单元格',
            prefixConfig: {
              icon: 'vxe-icon-copy',
            },
            visible: true,
          },
        ],
      ],
    },
    className: 'font-medium shadow rounded-md',
  },
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    enabled: false,
  },
  rowConfig: {
    useKey: true,
    keyField: 'Guid',
  },
  rowStyle({ row }) {
    if (row.Qty === 0) {
      return {
        backgroundColor: '#fff4e6',
        color: '#222',
      };
    }
  },
  size: 'mini',
  toolbarConfig: {
    enabled: false,
  },
  treeConfig: {
    transform: true,
    rowField: 'ItemNumber',
    parentField: 'ParentItem',
    showLine: true,
  },
});
const [ItemGrid, itemGridApi] = useVbenVxeGrid({
  gridOptions: itemGridOptions,
});
const gridEvents: VxeGridListeners = {
  async toggleRowExpand({ expanded, row }) {
    await handleItemData([]);
    if (expanded) {
      handleItemLoading(true);
      const itemData = await getOutboundItem(row.Guid);
      await handleHeadRow(row);
      await handleItemData(itemData);
      handleItemLoading(false);
      itemGridApi.grid?.setAllTreeExpand(true);
    }
  },
};
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions, gridEvents });
function handleHeadRow(row: any) {
  gridApi.grid.setCurrentRow(row);
}
function handleItemData(data: any) {
  itemGridApi.setGridOptions({
    data,
  });
}
function handleItemLoading(isLoading: boolean) {
  itemGridApi.setLoading(isLoading);
}
function handleImport() {
  const currRow = gridApi.grid.getRadioRecord();
  if (!currRow) {
    message.info(`请勾选需要绑定的Outbound`);
    return;
  }
  modalApi.setState({ confirmLoading: true });
  modalApi.setState({ loading: true });
  bwOutPickingSampleOutboundBinding(data.value.Guid, currRow.Guid)
    .then(() => {
      handleClose();
    })
    .catch(() => {})
    .finally(() => {
      modalApi.setState({ loading: false });
      modalApi.setState({ confirmLoading: false });
    });
}
function handleClose() {
  modalApi.close();
  emit('success');
}

function fetch() {
  setTimeout(() => {
    gridApi.grid.commitProxy('query');
  }, 200);
}
</script>
<template>
  <Modal>
    <Grid>
      <template #toolbar_left>
        <div class="hidden pl-1 text-base font-medium md:block">
          <span class="mr-2">{{ data.YUB }}</span>
        </div>
      </template>
      <template #toolbar-tools> </template>
      <template #expand_content>
        <div>
          <ItemGrid>
            <template #skucode="{ row }">
              <a
                v-if="row.Material"
                class="text-blue-500"
                @click="openSkuDrawer(row.Material)"
              >
                {{ row.Material }}
              </a>
            </template>
            <template #batchmanage="{ row }">
              <Tag :color="row.MaterialIsBatchManaged ? 'green' : 'red'">
                {{ row.MaterialIsBatchManaged ? '是' : '否' }}
              </Tag>
            </template>
          </ItemGrid>
        </div>
      </template>
    </Grid>
    <SkuDrawer />
  </Modal>
</template>
