<script lang="ts" setup>
import { onActivated, onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';

import { Page, useVbenDrawer, useVbenModal, VbenButton } from '@vben/common-ui';
import { useTabs } from '@vben/hooks';
import { AntCaretDown, AntCaretUp, AntFileText, AntSearch } from '@vben/icons';
import { useTabbarStore } from '@vben/stores';
import { downloadFileFromBlob } from '@vben/utils';

import {
  Card,
  Dropdown,
  Menu,
  message,
  Modal,
  TabPane,
  Tabs,
} from 'ant-design-vue';

import { useVbenForm } from '#/adapter';
import {
  bwInOrderClosing,
  bwInOrderHeadUpdate,
  getInOrderHead,
} from '#/api/bondedwarehouse/inbound';
import { createFile } from '#/api/common';

import inConfirmModal from './InConfirmModal.vue';
import invoiceDrawer from './invoiceDrawer.vue';
import TabCSAR from './tabCSAR.vue';
import TabInbound from './tabInbound.vue';
import TabReceiving from './tabReceiving.vue';

const loadingRef = ref(false);
const headData = ref({}) as any;
const init = ref(false);
const showForm = ref(true);
const submitLoading = ref(false);
const activeKey = ref('Receiving');
const route = useRoute();
const { setTabTitle } = useTabs();
const id = route.params?.id?.toString() ?? '';

const [InConfirmModal, InConfirmModalApi] = useVbenModal({
  connectedComponent: inConfirmModal,
});
const [InvoiceDrawer, InvoiceDrawerApi] = useVbenDrawer({
  connectedComponent: invoiceDrawer,
});
function openInConfirmModal() {
  InConfirmModalApi.setData({ id, hbl: headData.value.HBL });
  InConfirmModalApi.open();
}
function openInvoiceDrawer() {
  InvoiceDrawerApi.setData({ hbl: headData.value.HBL });
  InvoiceDrawerApi.open();
}
const [Form, baseFormApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'pb-1',
    labelClass: 'w-20',
  },
  compact: true,
  // 提交函数
  handleSubmit: onSubmit,
  layout: 'horizontal',
  schema: [
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'HBL',
      label: '入库提单',
      disabled: true,
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'OwnerShortName',
      label: '货主简称',
      disabled: true,
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'WarehouseName',
      label: '仓库',
      disabled: true,
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'InAreaName',
      label: '进仓区域',
      disabled: true,
    },
    {
      component: 'DatePicker',
      componentProps: {
        style: { width: '100%' },
        valueFormat: 'YYYY-MM-DD',
      },
      fieldName: 'InDate',
      label: '进仓日期',
      disabled: true,
    },
    {
      component: 'DatePicker',
      componentProps: {
        style: { width: '100%' },
        valueFormat: 'YYYY-MM-DD',
      },
      fieldName: 'PreInDate',
      label: '预计进仓',
    },
    {
      component: 'Input',
      componentProps: {
        maxlength: 32,
        autocomplete: 'off',
      },
      fieldName: 'ContractNo',
      label: '入库合同号',
    },
    {
      component: 'Input',
      componentProps: {
        maxlength: 32,
        autocomplete: 'off',
      },
      fieldName: 'JobNo',
      label: '业务编号',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        getPopupContainer: () => document.body,
      },
      fieldName: 'TrafMode',
      label: '运输方式',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        getPopupContainer: () => document.body,
      },
      fieldName: 'EntyPort',
      label: '入境口岸',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        getPopupContainer: () => document.body,
      },
      fieldName: 'WrapType',
      label: '包装类型',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        getPopupContainer: () => document.body,
      },
      fieldName: 'CargoType',
      label: '货物种类',
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: 0,
      },
      fieldName: 'TotalBoxNum',
      label: '箱数',
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: 0,
      },
      fieldName: 'TotalNetWeightInKg',
      label: '净重kg',
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: 0,
      },
      fieldName: 'TotalGrossWeightInKg',
      label: '毛重kg',
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: 0,
      },
      fieldName: 'TotalVolumeInM3',
      label: '体积m3',
    },
    {
      component: 'Input',
      componentProps: {
        maxlength: 128,
        autocomplete: 'off',
      },
      fieldName: 'Remark',
      label: '备注',
      formItemClass: 'col-span-1 md:col-span-2',
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'Invoices',
      label: '发票关联',
    },
    {
      component: 'DatePicker',
      componentProps: {
        style: { width: '100%' },
        valueFormat: 'YYYY-MM-DD',
      },
      fieldName: 'ETA',
      label: 'ETA',
    },
    {
      component: 'DatePicker',
      componentProps: {
        style: { width: '100%' },
        valueFormat: 'YYYY-MM-DD',
      },
      fieldName: 'ATA',
      label: 'ATA',
    },
    {
      component: 'DatePicker',
      componentProps: {
        style: { width: '100%' },
        valueFormat: 'YYYY-MM-DD',
      },
      fieldName: 'DeclareDate',
      label: '申报日期',
    },
    {
      component: 'Input',
      componentProps: {
        maxlength: 64,
        autocomplete: 'off',
      },
      fieldName: 'EntryID',
      label: '入库报关单',
      formItemClass: 'col-span-1 md:col-span-2',
    },
  ],
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2 md:grid-cols-4 xl:grid-cols-6',
});

async function onSubmit(values: Record<string, any>) {
  const check = await baseFormApi.validate();
  if (check.valid) {
    submitLoading.value = true;
    bwInOrderHeadUpdate(Object.assign({ id }, values))
      .then(() => {
        message.success('操作成功');
      })
      .catch(() => {})
      .finally(() => {
        setTimeout(() => {
          submitLoading.value = false;
        }, 500);
      });
  } else {
    message.info('必填项未填写完整，请检查！');
  }
}
function handleSave() {
  if (headData.value.IsClosed) {
    Modal.confirm({
      title: '确认保存',
      content: '当前订单已关闭，请确认是否保存修改？',
      okButtonProps: { danger: true },
      onOk() {
        baseFormApi.submitForm();
      },
    });
  } else {
    baseFormApi.submitForm();
  }
}

function handleClose(id: string) {
  loadingRef.value = true;
  bwInOrderClosing(id)
    .then(() => {
      message.success('操作成功');
    })
    .catch(() => {})
    .finally(() => {
      fetch();
      loadingRef.value = false;
    });
}
function showCloseModal(id: string, name: string) {
  Modal.confirm({
    content: `将自动上传理货报告至LIS系统，且无法再修改订单，请确认信息是否录入完整？`,
    onCancel() {},
    onOk() {
      handleClose(id);
    },
    title: `关闭入库订单，HBL: ${name}`,
    okButtonProps: { danger: true },
  });
}
function handleMenuClick(e: any) {
  let fileName = '';
  switch (e.key) {
    case 'InInspDoc': {
      if (!headData.value.ContractNo) {
        message.info(`请维护入库合同号！`);
        return;
      }
      fileName = `商检附表证书_${headData.value.ContractNo}.xls`;
      break;
    }
    case 'InLabelDoc': {
      fileName = `标签订单_${headData.value.HBL}.xls`;
      break;
    }
    case 'InPalletDoc': {
      fileName = `随托信息单_${headData.value.HBL}.xls`;
      break;
    }
    case 'InTallyDoc': {
      fileName = `理货报告_${headData.value.HBL}.zip`;
      break;
    }
    case 'InWriteDoc': {
      fileName = `手写单_${headData.value.HBL}.xls`;
      break;
    }
    default: {
      message.error(`请选择文件类型`);
      break;
    }
  }
  loadingRef.value = true;
  createFile(e.key, id)
    .then((res) => {
      downloadFileFromBlob({
        source: res.data,
        fileName,
      });
    })
    .catch(() => {
      /* message.error(`生成文件错误！`); */
    })
    .finally(() => {
      loadingRef.value = false;
    });
}
/* function handleSuccess() {
  fetch();
} */
async function fetch() {
  const res = await getInOrderHead(id);
  headData.value = res.head;
  const fieldList = ['TrafMode', 'EntyPort', 'WrapType', 'CargoType'];
  fieldList.forEach((field) => {
    baseFormApi.updateSchema([
      {
        componentProps: {
          options: res.options?.filter((item: any) => {
            return item.type === field;
          }),
        },
        fieldName: field,
      },
    ]);
  });
  const tabbarStore = useTabbarStore();
  const currentTab = tabbarStore.getTabs.find(
    (item) => item.path === route.path,
  );
  if (currentTab?.meta.title === '入库操作') {
    setTabTitle(`入库操作-${res.hbl}`);
  }

  baseFormApi.setValues(headData.value);
  init.value = false;
}
onMounted(() => {
  init.value = true;
  showForm.value = true;
  fetch();
});
onActivated(() => {
  const tabbarStore = useTabbarStore();
  const currentTab = tabbarStore.getTabs.find(
    (item) => item.path === route.path,
  );
  if (!currentTab?.meta.newTabTitle && !init.value) {
    fetch();
  }
});
</script>

<template>
  <Page auto-content-height v-loading="loadingRef">
    <div class="flex h-full flex-col">
      <Card :body-style="{ padding: '4px' }" :bordered="false" size="small">
        <template #title>
          <div class="hidden md:block">
            {{ `入库订单表头` }}
          </div>
        </template>
        <template #extra>
          <div class="flex">
            <Dropdown.Button :trigger="['click']" class="mr-2">
              <template #overlay>
                <Menu @click="handleMenuClick">
                  <Menu.Item key="InPalletDoc">随托信息单</Menu.Item>
                  <Menu.Item key="InWriteDoc">手写单</Menu.Item>
                  <Menu.Item key="InTallyDoc">理货报告</Menu.Item>
                  <Menu.Item key="InInspDoc">商检附表证书</Menu.Item>
                  <Menu.Item key="InLabelDoc">标签订单</Menu.Item>
                </Menu>
              </template>
              <template #icon><AntFileText class="size-5" /></template>
              <!-- <a-button style="margin-right: 8px">
                <AntFileText class="size-5" />文件
              </a-button> -->
              文件
            </Dropdown.Button>
            <a-button
              :danger="!headData.InStatus"
              :disabled="headData.InStatus"
              :type="headData.InStatus ? 'primary' : 'primary'"
              class="mr-2"
              @click="openInConfirmModal()"
            >
              进仓确认
            </a-button>
            <a-button
              :loading="submitLoading"
              :danger="headData.IsClosed"
              class="mr-2"
              type="primary"
              @click="handleSave"
            >
              保存
            </a-button>
            <VbenButton
              :disabled="headData.IsClosed"
              class="mr-2"
              size="sm"
              variant="info"
              @click="showCloseModal(id, headData.HBL)"
            >
              关单
            </VbenButton>
            <AntCaretDown
              v-if="showForm"
              class="size-6"
              @click="showForm = !showForm"
            />
            <AntCaretUp v-else class="size-6" @click="showForm = !showForm" />
          </div>

          <!-- <a-button type="link" @click="showForm = !showForm"> -->
          <!-- </a-button> -->
        </template>

        <Form v-show="showForm">
          <template #Invoices="slotProps">
            <a-input-search
              v-bind="slotProps"
              readonly="readonly"
              @search="openInvoiceDrawer()"
            >
              <template #enterButton>
                <a-button class="!pl-1 !pr-1" :disabled="!headData.HBL">
                  <AntSearch class="size-5" />
                </a-button>
              </template>
            </a-input-search>
            <!-- <a-input v-bind="slotProps" disabled /> -->
          </template>
        </Form>
      </Card>
      <div class="inorder-tab min-h-0 flex-1">
        <Tabs v-model:active-key="activeKey" class="h-full" tab-position="left">
          <TabPane key="Receiving" tab="收货">
            <div v-if="activeKey === 'Receiving'" class="h-full">
              <TabReceiving
                :id="id"
                :hbl="headData.HBL"
                :in-status="headData.InStatus"
                :is-closed="headData.IsClosed"
                :owner="headData.OwnerCode"
              />
            </div>
          </TabPane>
          <TabPane key="CSARQC" tab="CSAR">
            <div v-if="activeKey === 'CSARQC'" class="h-full">
              <TabCSAR
                :id="id"
                :hbl="headData.HBL"
                :is-closed="headData.IsClosed"
              />
            </div>
          </TabPane>
          <TabPane key="IBDGR" tab="IBD">
            <div v-if="activeKey === 'IBDGR'" class="h-full">
              <TabInbound
                :id="id"
                :hbl="headData.HBL"
                :is-closed="headData.IsClosed"
              />
            </div>
          </TabPane>
          <!-- <TabPane key="Shelve" tab="上架">
            <div v-if="activeKey === 'Shelve'">222</div>
          </TabPane> -->
        </Tabs>
      </div>
      <InConfirmModal @success="fetch()" />
      <InvoiceDrawer />
    </div>
  </Page>
</template>

<style scoped lang="less">
::v-deep(.inorder-tab) {
  background-color: #fff;

  .ant-tabs-tabpane .ant-tabs-tabpane-active {
    padding-left: 0 !important;
  }

  .ant-tabs-left > .ant-tabs-nav .ant-tabs-tab {
    padding: 8px 12px;
  }

  .ant-tabs-tab-active {
    background-color: #e7f5ff;
  }

  .ant-tabs-content-holder > .ant-tabs-content > .ant-tabs-tabpane {
    padding-left: 0;
  }
  .ant-tabs-content-left {
    height: 100%;
  }
}
</style>
