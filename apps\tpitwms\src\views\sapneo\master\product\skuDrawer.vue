<script lang="ts" setup>
import type { VxeGridProps } from '#/adapter';

import { reactive, ref } from 'vue';

import { Fallback, useVbenDrawer } from '@vben/common-ui';

import { Descriptions, RadioButton, RadioGroup, Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import { getProductData } from '#/api/sapneo';

const data = ref();
const dataType = ref('info');
const skuData = ref({}) as any;
const isNotFound = ref(false);
const [Drawer, drawerApi] = useVbenDrawer({
  class: 'w-[640px]',
  showConfirmButton: false,
  cancelText: '关闭',
  destroyOnClose: true,
  zIndex: 900,
  onCancel() {
    drawerApi.close();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      isNotFound.value = false;
      dataType.value = 'info';
      data.value = drawerApi.getData<Record<string, any>>();
      dataType.value = 'info';
      handleGetData(data.value.id);
    }
  },
});
const batchGridOptions = reactive<VxeGridProps>({
  columns: [
    { type: 'seq', width: 50 },
    {
      field: 'Material',
      title: '产品编号',
      width: 88,
    },
    {
      field: 'Batch',
      title: '批次',
      width: 88,
    },
    {
      field: 'MatlBatchIsInRstrcdUseStock',
      title: '批次状态',
      minWidth: 88,
      slots: { default: 'rstrcd' },
    },
    {
      field: 'ManufactureDate',
      title: '制造日期',
      width: 100,
      formatter: 'formatDate',
    },
    {
      field: 'ShelfLifeExpirationDate',
      title: '批次效期',
      width: 100,
      formatter: 'formatDate',
    },
    {
      field: 'RelatedBatches',
      title: '关联批次',
      minWidth: 136,
    },
  ],
  customConfig: {
    storage: false,
  },
  menuConfig: {
    body: {
      options: [
        [
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuCopy',
            disabled: false,
            name: '复制单元格',
            prefixConfig: {
              icon: 'vxe-icon-copy',
            },
            visible: true,
          },
        ],
      ],
    },
    className: 'font-medium shadow rounded-md',
  },
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    enabled: false,
  },
  size: 'small',
  toolbarConfig: {
    enabled: false,
  },
});
const tuColumns = [
  /* { type: 'seq', width: 50, fixed: 'left' }, */
  {
    field: 'tuLevel',
    title: '层级',
    width: 180,
    fixed: 'left',
  },
  {
    field: 'tuType',
    title: '类型',
    width: 72,
  },
  {
    field: 'tuGtinCode',
    title: 'Gtin代码',
    width: 136,
  },
  {
    field: 'nbOfCuContainedInLevel',
    title: '产品数量',
    width: 108,
  },
  {
    field: 'tuDirectlyBelowLevel',
    title: '下一级',
    width: 180,
  },
  {
    field: 'nbOfTuContainedInLevel',
    title: '下一级数量',
    width: 108,
  },
  {
    field: 'tuBarcoded',
    title: '含条码',
    width: 72,
    align: 'center',
    /* slots: { default: 'tuBarcoded' }, */
  },
  /* {
    field: 'depthWidthHeight',
    title: '长宽高-mm',
    minWidth: 150,
  }, */
  {
    field: 'tuNetWeightInG',
    title: '净重-g',
    width: 88,
  },
  {
    field: 'tuGrossWeightInG',
    title: '毛重-g',
    width: 88,
  },
  {
    field: 'tuDepthInMm',
    title: '长-mm',
    minWidth: 150,
  },
  {
    field: 'tuWidthInMm',
    title: '宽-mm',
    minWidth: 150,
  },
  {
    field: 'tuHeightInMm',
    title: '高-mm',
    minWidth: 150,
  },
  {
    field: 'tuVolumeInCm3',
    title: '体积-cm3',
    width: 88,
  },
];
const tuGridOptions = reactive<VxeGridProps>({
  columnConfig: {
    isCurrent: true,
  },
  columns: [],
  customConfig: {
    storage: false,
  },
  menuConfig: {
    body: {
      options: [
        [
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuCopy',
            disabled: false,
            name: '复制单元格',
            prefixConfig: {
              icon: 'vxe-icon-copy',
            },
            visible: true,
          },
        ],
      ],
    },
    className: 'font-medium shadow rounded-md',
  },
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    enabled: false,
  },
  rowConfig: {
    isCurrent: false,
  },
  stripe: false,
  size: 'small',
  showHeader: false,
  toolbarConfig: {
    enabled: false,
  },
});
const [TUGrid, tuGridApi] = useVbenVxeGrid({ gridOptions: tuGridOptions });
const [BatchGrid, batchGridApi] = useVbenVxeGrid({
  gridOptions: batchGridOptions,
});
const reverseTable = (tuData: any) => {
  const buildData = tuColumns.map((column: any) => {
    const item: any = { col0: column.title };
    tuData.forEach((row: any, index: number) => {
      item[`col${index + 1}`] = row[column.field];
    });
    return item;
  });
  const buildColumns: any = [
    {
      field: 'col0',
      fixed: 'left',
      width: 88,
    },
  ];
  tuData.forEach((_item: any, index: number) => {
    buildColumns.push({
      field: `col${index + 1}`,
      minWidth: 128,
    });
  });
  tuGridApi.grid.loadColumn(buildColumns);
  tuGridApi.grid.loadData(buildData);
};
function handleGetData(id: string) {
  drawerApi.setState({ loading: true });
  getProductData(id)
    .then((res) => {
      skuData.value = res.info;
      batchGridApi.grid.loadData(res.batch);
      reverseTable(res.tradeUnits);
    })
    .catch(() => {
      isNotFound.value = true;
    })
    .finally(() => {
      drawerApi.setState({ loading: false });
    });
}
</script>
<template>
  <Drawer title="产品信息">
    <Fallback v-if="isNotFound" :show-back="false" status="404" />
    <div v-else>
      <RadioGroup
        v-model:value="dataType"
        button-style="solid"
        class="mb-2"
        @change="1"
      >
        <RadioButton value="info">产品主数据</RadioButton>
        <RadioButton value="batch">批次信息</RadioButton>
        <RadioButton value="tradeUnits">贸易单位</RadioButton>
      </RadioGroup>
      <Descriptions
        v-show="dataType === 'info'"
        :column="{ xs: 1, sm: 2, md: 2 }"
        bordered
        size="small"
      >
        <Descriptions.Item :span="2" label="产品编号">
          {{ skuData.skuCode }}
        </Descriptions.Item>
        <Descriptions.Item :span="2" label="产品条码">
          {{ skuData.productStandardId }}
        </Descriptions.Item>
        <Descriptions.Item :span="2" label="产品结构代码">
          {{ skuData.productHierarchy }}
        </Descriptions.Item>
        <Descriptions.Item :span="2" label="中文品名">
          {{ skuData.logisticDescription }}
        </Descriptions.Item>
        <Descriptions.Item :span="2" label="英文品名">
          {{ skuData.rootDescription }}
        </Descriptions.Item>
        <Descriptions.Item :span="2" label="长宽高-mm">
          {{
            `${skuData.cuDepthInMm} * ${skuData.cuWidthInMm} * ${skuData.cuHeightInMm}`
          }}
        </Descriptions.Item>
        <Descriptions.Item label="毛重-g">
          {{ skuData.cuGrossWeightInG }}
        </Descriptions.Item>
        <Descriptions.Item label="体积-cm3">
          {{ skuData.cuVolumeInCm3 }}
        </Descriptions.Item>
        <Descriptions.Item label="产品类型">
          {{ skuData.materialType }}
        </Descriptions.Item>
        <Descriptions.Item label="物料类型">
          {{ skuData.MatType }}
        </Descriptions.Item>
        <Descriptions.Item label="分组">
          {{ skuData.materialGroup }}
        </Descriptions.Item>
        <Descriptions.Item label="品牌">
          {{
            skuData.BrandName
              ? `${skuData.signatureSapS4Code}-${skuData.BrandName}`
              : `${skuData.signatureSapS4Code}`
          }}
        </Descriptions.Item>
        <Descriptions.Item label="效期-天">
          {{ skuData.lifespanInDays }}
        </Descriptions.Item>
        <Descriptions.Item label="产品规格">
          {{ skuData.GS1NetContentMetric + skuData.GS1NetContentMetricUnit }}
        </Descriptions.Item>
        <Descriptions.Item label="防串货标识">
          <Tag
            :color="skuData.productCarriesAntidiversionMark ? 'green' : 'red'"
          >
            {{ skuData.productCarriesAntidiversionMark ? '是' : '否' }}
          </Tag>
        </Descriptions.Item>
        <Descriptions.Item label="批次管理">
          <Tag :color="skuData.isBatchManagementRequired ? 'green' : 'red'">
            {{ skuData.isBatchManagementRequired ? '是' : '否' }}
          </Tag>
        </Descriptions.Item>

        <Descriptions.Item label="PCB">
          {{ skuData.PCB }}
        </Descriptions.Item>
        <Descriptions.Item label="SPCB">
          {{ skuData.SPCB }}
        </Descriptions.Item>
        <Descriptions.Item label="层规件数">
          {{ skuData.layerSupportQuantity }}
        </Descriptions.Item>
        <Descriptions.Item label="托规件数">
          {{ skuData.palletSupportQuantity }}
        </Descriptions.Item>
        <!-- <Descriptions.Item :span="2" label="支持托盘类型">
          {{ skuData.supportTypeCode }}
        </Descriptions.Item>
        <Descriptions.Item :span="2" label="支持托盘尺寸(mm)">
          {{
            skuData.supportTypeCode
              ? `${skuData.palletSupportDepthInMm} * ${skuData.palletSupportWidthInMm} * ${skuData.palletSupportHeightInMm}`
              : ''
          }}
        </Descriptions.Item>
        <Descriptions.Item label="支持托盘重量(g)">
          {{ skuData.palletSupportWeightInG }}
        </Descriptions.Item>
        <Descriptions.Item label="最大负载重量(g)">
          {{ skuData.acceptablePalletLoadInG }}
        </Descriptions.Item> -->
        <!-- <Descriptions.Item :span="2" label="换1*1.2码托校验">
          {{ skuData.palletSupportMark ? '可正常码托' : '需注意码托方式' }}
        </Descriptions.Item> -->
        <Descriptions.Item label="危险品编号">
          {{ skuData.hazardousMaterialNumber }}
        </Descriptions.Item>
        <Descriptions.Item label="原产国">
          {{ skuData.countryOfOrigin }}
        </Descriptions.Item>
        <!-- <Descriptions.Item label="mmStatus">
          {{ skuData.mmStatus }}
        </Descriptions.Item> -->
        <!-- <Descriptions.Item label="是否危险品">
          <Tag :color="skuData.dangerousGood ? 'green' : 'red'">
            {{ skuData.dangerousGood ? '是' : '否' }}
          </Tag>
        </Descriptions.Item>
        <Descriptions.Item label="UN编码">
          {{ skuData.unNumber }}
        </Descriptions.Item>
        <Descriptions.Item label="运输危险类别">
          {{ skuData.transportationDangerousClass }}
        </Descriptions.Item> -->
        <Descriptions.Item label="创建日期">
          {{ skuData.CreateDate }}
        </Descriptions.Item>
        <Descriptions.Item label="更新日期">
          {{ skuData.UpdateDate }}
        </Descriptions.Item>
        <Descriptions.Item label="crossPlantStatus">
          {{ skuData.crossPlantStatus }}
        </Descriptions.Item>
        <Descriptions.Item label="productWithBulk">
          <Tag :color="skuData.productWithBulk ? 'green' : 'red'">
            {{ skuData.productWithBulk ? '是' : '否' }}
          </Tag>
        </Descriptions.Item>
        <Descriptions.Item label="designedFor">
          {{ skuData.designedFor }}
        </Descriptions.Item>
        <Descriptions.Item label="MAAG">
          {{ skuData.materialAccountAssignmentGroup }}
        </Descriptions.Item>
      </Descriptions>
      <TUGrid v-show="dataType === 'tradeUnits'" class="reverse-table" />
      <BatchGrid v-show="dataType === 'batch'">
        <template #rstrcd="{ row }">
          <Tag :color="row.MatlBatchIsInRstrcdUseStock ? 'red' : 'green'">
            {{ row.MatlBatchIsInRstrcdUseStock ? '受限制' : '非限制' }}
          </Tag>
        </template>
      </BatchGrid>
    </div>
  </Drawer>
</template>

<style lang="scss">
.reverse-table .vxe-body--row .vxe-body--column:first-child {
  background-color: #f8f8f9;
}
</style>
