<script setup lang="ts">
import type { VxeGridProps } from '#/adapter';

import { onMounted, reactive } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';
import { AntClear } from '@vben/icons';

import { Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import { getStockPLList } from '#/api/bondedwarehouse/stock';
import { getOptions } from '#/api/common';
import batchDrawer from '#/views/sapneo/master/batch/batchDrawer.vue';
import skuDrawer from '#/views/sapneo/master/product/skuDrawer.vue';

const searchField: any = reactive({
  TakingNo: undefined,
  /* MatCode: undefined, */
});
const [SkuDrawer, skuDrawerApi] = useVbenDrawer({
  connectedComponent: skuDrawer,
});
const [BatchDrawer, batchDrawerApi] = useVbenDrawer({
  connectedComponent: batchDrawer,
});
function openSkuDrawer(id: string) {
  skuDrawerApi.setData({ id });
  skuDrawerApi.open();
}
function openBatchDrawer(sku: string, batch: string) {
  batchDrawerApi.setData({ sku, batch });
  batchDrawerApi.open();
}

const gridOptions = reactive<VxeGridProps>({
  id: 'BWStockVerifyPL',
  checkboxConfig: { highlight: true, range: true },
  columns: [
    {
      fixed: 'left',
      title: '#',
      type: 'seq',
      width: 60,
    },
    {
      field: 'VerifyStatus',
      title: '核销状态',
      width: 88,
      align: 'center',
      filters: [
        { label: '待核销', value: 0 },
        { label: '已核销', value: 1 },
        { label: '撤销', value: -1 },
      ],
      filterMultiple: false,
      slots: { default: 'verifystatus' },
    },
    {
      field: 'TakingNo',
      title: '盘点作业单',
      width: 136,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'OwnerShortName',
      title: '货主',
      minWidth: 100,
      filters: [],
    },
    {
      field: 'WarehouseName',
      title: '仓库',
      width: 100,
      filters: [],
    },
    { field: 'AreaName', title: '库区', width: 88 },
    { field: 'BIN', title: '货位', width: 96 },
    { field: 'StockStatus', title: '库存状态', width: 80 },
    {
      field: 'HBL',
      title: 'HBL',
      width: 136,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      visible: false,
    },
    {
      field: 'PalletNo',
      title: '托盘号',
      width: 100,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'MatCode',
      title: '物料编号',
      width: 88,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      slots: { default: 'material' },
    },
    {
      field: 'BatchNo',
      title: '实物批号',
      width: 88,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      slots: { default: 'batch' },
    },
    {
      field: 'SAPBatch',
      title: 'SAP批次',
      width: 88,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'StorageLocation',
      title: 'SAP位置',
      width: 96,
    },
    { field: 'PLQty', title: '差异数量', minWidth: 88 },
    {
      field: 'StockType',
      title: '库存类型',
      minWidth: 88,
      filters: [
        { label: '非限制', value: 'UNR' },
        { label: '冻结', value: 'BLK' },
      ],
      filterMultiple: false,
      slots: { default: 'stocktype' },
    },
    /* { field: 'TakingBy2st', title: '复盘人', width: 72 }, */
    {
      field: 'TakingDate2st',
      title: '复盘日期',
      width: 88,
      formatter: 'formatDate',
    },
    /* {
      align: 'center',
      slots: { default: 'action' },
      title: '撤销',
      width: 60,
    }, */
  ],
  filterConfig: {
    remote: true,
  },
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: ({ filters, page, sorts }) => {
        const queryParams: any = Object.assign({}, page);
        // 处理排序
        if (sorts.length === 0) {
          queryParams.sort = '_Identify desc';
        } else {
          const sortItem = sorts.map((item) => {
            const newItem = `${item.field} ${item.order}`;
            return newItem;
          });
          queryParams.sort = sortItem.join(',');
        }
        // 处理筛选
        Object.getOwnPropertyNames(searchField).forEach((key) => {
          searchField[key] = undefined;
        });
        filters.forEach(({ field, values }) => {
          queryParams[field] = values.join(',');
          if (Object.prototype.hasOwnProperty.call(searchField, field)) {
            const jsonObject = JSON.parse(values.toString());
            searchField[field] = jsonObject.text;
          }
        });
        return getStockPLList(queryParams);
      },
    },
    seq: true,
  },
  showFooter: false,
  size: 'mini',
  sortConfig: {
    remote: true,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
  scrollX: { enabled: true, gt: 0 },
  scrollY: { enabled: true, mode: 'wheel', gt: 30, oSize: 0 },
});

const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });

/* function handleSuccess() {
  gridApi.grid.commitProxy('query');
} */
function searchEvent(field: string) {
  const column = gridApi.grid.getColumnByField(field);
  if (column) {
    // 修改第一个选项为勾选状态
    const option = column.filters[0];
    if (!option) {
      return;
    }

    if (searchField[field]) {
      option.data = { type: '1', text: searchField[field] };
      option.value = JSON.stringify(option.data);
      option.checked = true;
    } else {
      option.data = { type: '0', text: undefined };
      option.checked = false;
    }

    // 修改条件之后，需要手动调用 updateData 处理表格数据
    gridApi.grid.updateData();
    gridApi.grid.commitProxy('query');
  }
}
function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
  gridApi.grid.commitProxy('query');
}
async function fetch() {
  const options = await getOptions('Owner&Warehouse');
  const fieldList = ['OwnerShortName', 'WarehouseName'];
  fieldList.forEach((field) => {
    gridApi.grid.setFilter(
      field,
      options.filter((item: any) => {
        return item.type === field;
      }),
    );
  });
}
onMounted(() => {
  setTimeout(() => {
    fetch();
  }, 300);
});
</script>

<template>
  <div class="flex h-full flex-col">
    <Grid>
      <template #toolbar_left>
        <a-button class="mr-1" @click="clearFilterAndSort()">
          <AntClear class="size-5" />
        </a-button>
        <div class="hidden pl-1 md:block">
          <a-input-search
            v-model:value="searchField.TakingNo"
            allow-clear
            class="mr-2 w-60"
            placeholder="盘点作业单"
            @search="searchEvent('TakingNo')"
          />
          <!-- <a-input-search
            v-model:value="searchField.MatCode"
            allow-clear
            class="mr-2 w-48"
            placeholder="物料编号"
            @search="searchEvent('MatCode', 'Input')"
          /> -->
        </div>
      </template>
      <template #toolbar-tools> </template>
      <template #material="{ row }">
        <a
          v-if="row.MatCode"
          class="text-blue-500"
          @click="openSkuDrawer(row.MatCode)"
        >
          {{ row.MatCode }}
        </a>
      </template>
      <template #batch="{ row }">
        <a
          v-if="row.BatchNo"
          class="text-blue-500"
          @click="openBatchDrawer(row.MatCode, row.BatchNo)"
        >
          {{ row.BatchNo }}
        </a>
      </template>
      <template #stocktype="{ row }">
        <Tag :color="row.StockType === 'UNR' ? 'green' : 'red'">
          {{ row.StockType === 'UNR' ? '非限制' : '冻结' }}
        </Tag>
      </template>
      <template #verifystatus="{ row }">
        <Tag
          :color="
            row.VerifyStatus === 0
              ? 'blue'
              : row.VerifyStatus === 1
                ? 'green'
                : 'red'
          "
        >
          {{
            row.VerifyStatus === 0
              ? '待核销'
              : row.VerifyStatus === 1
                ? '已核销'
                : '撤销操作'
          }}
        </Tag>
      </template>
    </Grid>
    <SkuDrawer />
    <BatchDrawer />
  </div>
</template>
