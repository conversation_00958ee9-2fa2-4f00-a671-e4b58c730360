<script setup lang="ts">
import type { VxeGridListeners, VxeGridProps } from '#/adapter';

import { defineProps, reactive, ref } from 'vue';

import { useVbenDrawer, useVbenModal, VbenLoading } from '@vben/common-ui';
import { AntSandBox } from '@vben/icons';

import { message, Modal, Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import {
  getStockTakingItem2st,
  stockTakingItem2stCancel,
} from '#/api/bondedwarehouse/stock';
import batchDrawer from '#/views/sapneo/master/batch/batchDrawer.vue';
import skuDrawer from '#/views/sapneo/master/product/skuDrawer.vue';

import taking2stModal from './taking2stModal.vue';

/* import csarDrawer from './CSARDrawer.vue'; */

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
  doc: {
    type: String,
    default: '',
  },
  warehouse: {
    type: String,
    default: '',
  },
  owner: {
    type: String,
    default: '',
  },
  isClosed: {
    type: Boolean,
    default: false,
  },
  isBright: {
    type: Boolean,
    default: false,
  },
});
const loadingRef = ref(false);
const [Taking2stModal, Taking2stModalApi] = useVbenModal({
  connectedComponent: taking2stModal,
});
const [SkuDrawer, skuDrawerApi] = useVbenDrawer({
  connectedComponent: skuDrawer,
});
const [BatchDrawer, batchDrawerApi] = useVbenDrawer({
  connectedComponent: batchDrawer,
});
function openSkuDrawer(id: string) {
  skuDrawerApi.setData({ id });
  skuDrawerApi.open();
}
function openBatchDrawer(sku: string, batch: string) {
  batchDrawerApi.setData({ sku, batch });
  batchDrawerApi.open();
}

function openTaking2stModal(row: any) {
  Taking2stModalApi.setData({ row, isBright: props.isBright });
  Taking2stModalApi.open();
}
/* const [CSARDrawer, CSARDrawerApi] = useVbenDrawer({
  connectedComponent: csarDrawer,
});
function openCSARDrawer(id: string) {
  CSARDrawerApi.setData({ id });
  CSARDrawerApi.open();
} */

const gridOptions = reactive<VxeGridProps>({
  id: 'BWStockTaking2st',
  checkboxConfig: { highlight: true, range: true },
  columns: [
    { type: 'checkbox', width: 48, fixed: 'left' },
    {
      fixed: 'left',
      title: '#',
      type: 'seq',
      width: 60,
    },
    {
      field: 'ReTakingStatus',
      title: '复盘状态',
      width: 88,
      align: 'center',
      formatter({ cellValue }) {
        return cellValue ? '已盘点' : '待盘点';
      },
      slots: { default: 'status' },
    },
    { field: 'AreaName', title: '库区', width: 80 },
    { field: 'BIN', title: '货位', width: 96 },
    { field: 'StockStatus', title: '库存状态', width: 80 },
    { field: 'HBL', title: 'HBL', width: 136 },
    { field: 'PalletNo', title: '托盘号', width: 96 },
    {
      field: 'MatCode',
      title: '物料编号',
      width: 80,
      slots: { default: 'material' },
    },
    {
      field: 'BatchNo',
      title: '批号',
      width: 80,
      slots: { default: 'batch' },
    },
    {
      field: 'SAPBatch',
      title: 'SAP批次',
      width: 80,
    },
    {
      field: 'StorageLocation',
      title: 'SAP位置',
      width: 96,
    },
    { field: 'TakingDiffQty', title: '初盘差异', minWidth: 72 },
    { field: 'UnrestrictedQty', title: '账面非限制', minWidth: 84 },
    { field: 'BlockedQty', title: '账面冻结', minWidth: 72 },
    { field: 'ActualUnrestrictedQty', title: '实际非限制', minWidth: 84 },
    { field: 'ActualBlockedQty', title: '实际冻结', minWidth: 72 },
    { field: 'TakingBy1st', title: '操作人', width: 72 },
    {
      field: 'TakingDate1st',
      title: '操作日期',
      width: 88,
      formatter: 'formatDate',
    },
    {
      title: '操作',
      fixed: 'right',
      width: 96,
      slots: { default: 'taking' },
      align: 'center',
    },
    /* {
      field: 'GoodsMovementReasonCode',
      title: '调整原因',
      minWidth: 150,
    }, */
    /* {
      field: 'CreateBy',
      title: '添加人',
      minWidth: 100,
    },
    {
      field: 'CreateDate',
      title: '添加日期',
      minWidth: 150,
      sortable: true,
    }, */
  ],
  filterConfig: {
    remote: false,
  },
  footerRowStyle: { 'font-weight': 'bold ' },
  footerMethod({ columns, data }) {
    return [
      columns.map((column, columnIndex) => {
        if (columnIndex === 0) {
          return `合计`;
        }
        if (columnIndex === 1) {
          return `${data.length} 项`;
        }
        if (
          [
            'ActualBlockedQty',
            'ActualUnrestrictedQty',
            'BlockedQty',
            'UnrestrictedQty',
          ].includes(column.field)
        ) {
          return sumNum(data, column.field);
        }
        return null;
      }),
    ];
  },
  height: 'auto',
  keepSource: true,
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    ajax: {
      query: async () => {
        return await getStockTakingItem2st(props.id);
      },
    },
    seq: true,
  },
  showFooter: true,
  size: 'mini',
  sortConfig: {
    remote: false,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
  scrollX: { enabled: true, gt: 0 },
  scrollY: { enabled: true, mode: 'wheel', gt: 30, oSize: 0 },
  menuConfig: {
    trigger: 'cell',
    body: {
      options: [
        [
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuCopy',
            disabled: false,
            name: '复制单元格',
            prefixConfig: {
              icon: 'vxe-icon-copy',
            },
            visible: true,
          },
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuRefresh',
            disabled: false,
            name: '刷新',
            prefixConfig: { icon: 'vxe-icon-refresh' },
            visible: true,
          },
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuExport',
            disabled: false,
            name: '导出明细',
            prefixConfig: { icon: 'vxe-icon-download' },
            visible: true,
          },
        ],
      ],
    },
    className: 'font-medium shadow rounded-md',
  },
  /* rowStyle({ row }) {
    if (row.TakingStatus && row.IsDiff) {
      return {
        backgroundColor: '#ffe3e3',
        color: '#222',
      };
    }
  }, */
});
const gridEvents: VxeGridListeners = {
  /* cellDblclick({ row }) {
    openCSARDrawer(row.Guid);
  }, */
  menuClick({ menu }) {
    switch (menu.code) {
      case 'menuExport': {
        handleExport();
        break;
      }
      default: {
        break;
      }
    }
  },
};
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions, gridEvents });
function sumNum(list: any[], field: string) {
  let count = 0;
  list.forEach((item) => {
    count += Number(item[field]);
  });
  return count;
}
function handleExport() {
  gridApi.grid.exportData({
    filename: `复盘作业_${props.doc}`,
    sheetName: 'Sheet1',
    type: 'xlsx',
    isFooter: false,
    columnFilterMethod: ({ column }) => {
      return column.field !== undefined;
    },
  });
}
function handleSuccess() {
  gridApi.grid.commitProxy('query');
}
function showDeleteModal() {
  Modal.confirm({
    content: `请确认是否撤销复盘操作？`,
    /* icon: createVNode(ExclamationCircleOutlined), */
    onCancel() {},
    onOk() {
      handleCancel();
    },
    title: `操作确认`,
    okButtonProps: { danger: true },
  });
}
function handleCancel() {
  const checkedRecords = gridApi.grid
    .getCheckboxRecords()
    .map((item: any) => item.Guid);
  if (checkedRecords.length === 0) {
    message.info(`请勾选已盘点的作业明细`);
    return;
  }
  loadingRef.value = true;
  stockTakingItem2stCancel(props.id, checkedRecords)
    .then(() => {
      message.success('操作成功！');
      handleSuccess();
    })
    .catch(() => {})
    .finally(() => {
      loadingRef.value = false;
    });
}

/* function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
} */
</script>

<template>
  <div class="flex h-full flex-col">
    <Grid>
      <template #toolbar_left>
        <div class="hidden pl-1 text-base font-medium md:block">
          <span class="mr-2">复盘明细</span>
        </div>
        <!-- <a-button class="mr-1" size="small" @click="clearFilterAndSort()">
          <AntClear class="size-5" />
        </a-button> -->
      </template>
      <template #toolbar-tools>
        <a-button
          class="ml-2"
          size="small"
          @click="showDeleteModal()"
          :disabled="props.isClosed"
        >
          撤销复盘
        </a-button>
      </template>
      <template #status="{ row }">
        <Tag :color="row.ReTakingStatus ? 'green' : 'blue'">
          {{ row.ReTakingStatus ? '已盘点' : '待盘点' }}
        </Tag>
      </template>
      <template #material="{ row }">
        <a
          v-if="row.MatCode"
          class="text-blue-500"
          @click="openSkuDrawer(row.MatCode)"
        >
          {{ row.MatCode }}
        </a>
      </template>
      <template #batch="{ row }">
        <a
          v-if="row.BatchNo"
          class="text-blue-500"
          @click="openBatchDrawer(row.MatCode, row.BatchNo)"
        >
          {{ row.BatchNo }}
        </a>
      </template>
      <template #taking="{ row }">
        <a-button
          :disabled="row.ReTakingStatus"
          size="small"
          type="primary"
          @click="openTaking2stModal(row)"
        >
          <AntSandBox class="size-4" />
          复盘
        </a-button>
      </template>
    </Grid>
    <Taking2stModal @success="handleSuccess()" />
    <SkuDrawer />
    <BatchDrawer />
    <VbenLoading :spinning="loadingRef" />
  </div>
</template>
