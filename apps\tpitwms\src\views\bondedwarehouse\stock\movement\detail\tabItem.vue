<script setup lang="ts">
import type { VxeGridListeners, VxeGridProps } from '#/adapter';

import { defineProps, reactive } from 'vue';

import { useVbenDrawer, useVbenModal, VbenButton } from '@vben/common-ui';
import { AntClear, AntDelete } from '@vben/icons';

import { message, Modal, Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import {
  getStockMovementItem,
  stockMovementItemDelete,
} from '#/api/bondedwarehouse/stock';
import batchDrawer from '#/views/sapneo/master/batch/batchDrawer.vue';
import skuDrawer from '#/views/sapneo/master/product/skuDrawer.vue';

import blockItemImportModal from './blockItemImportModal.vue';
import itemImportModal from './itemImportModal.vue';

/* import csarDrawer from './CSARDrawer.vue'; */

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
  doc: {
    type: String,
    default: '',
  },
  isClosed: {
    type: Boolean,
    default: false,
  },
  isLocked: {
    type: Boolean,
    default: false,
  },
});

const [ItemImportModal, ItemImportModalApi] = useVbenModal({
  connectedComponent: itemImportModal,
});
const [BlockItemImportModal, BlockItemImportModalApi] = useVbenModal({
  connectedComponent: blockItemImportModal,
});
const [SkuDrawer, skuDrawerApi] = useVbenDrawer({
  connectedComponent: skuDrawer,
});
const [BatchDrawer, batchDrawerApi] = useVbenDrawer({
  connectedComponent: batchDrawer,
});
function openSkuDrawer(id: string) {
  skuDrawerApi.setData({ id });
  skuDrawerApi.open();
}
function openBatchDrawer(sku: string, batch: string) {
  batchDrawerApi.setData({ sku, batch });
  batchDrawerApi.open();
}
function openItemImportModal() {
  ItemImportModalApi.setData({ id: props.id });
  ItemImportModalApi.open();
}
function openBlockItemImportModal() {
  BlockItemImportModalApi.setData({ id: props.id });
  BlockItemImportModalApi.open();
}
/* const [CSARDrawer, CSARDrawerApi] = useVbenDrawer({
  connectedComponent: csarDrawer,
});
function openCSARDrawer(id: string) {
  CSARDrawerApi.setData({ id });
  CSARDrawerApi.open();
} */

const gridOptions = reactive<VxeGridProps>({
  id: 'BWStockMovementItem',
  checkboxConfig: { highlight: true, range: true },
  columns: [
    { type: 'checkbox', width: 48, fixed: 'left' },
    {
      fixed: 'left',
      title: '#',
      type: 'seq',
      width: 60,
    },
    { field: 'StockStatus', title: '库存状态', width: 80 },
    { field: 'PalletNo', title: '托盘号', width: 88 },
    { field: 'Plant', title: '工厂', width: 72 },
    { field: 'ToPlant', title: '目标工厂', width: 80, visible: false },
    {
      field: 'MatCode',
      title: '当前物料',
      width: 80,
      slots: { default: 'fromMaterial' },
    },
    {
      field: 'isBatchManagementRequired ',
      formatter({ cellValue }) {
        return cellValue ? '是' : '否';
      },
      title: '批次管理',
      width: 72,
      align: 'center',
      slots: { default: 'batchmanage' },
    },
    {
      field: 'ToMaterial',
      title: '目标物料',
      width: 80,
      slots: { default: 'toMaterial' },
    },
    {
      field: 'BatchNo',
      title: '当前批次',
      width: 80,
      slots: { default: 'fromBatch' },
    },
    {
      field: 'ToBatch',
      title: '目标批次',
      width: 80,
      slots: { default: 'toBatch' },
    },
    {
      field: 'FromSAPBatch',
      title: '当前SAP批次',
      width: 96,
    },
    {
      field: 'ToSAPBatch',
      title: '目标SAP批次',
      width: 96,
    },
    {
      field: 'StockType',
      title: '库存类型',
      width: 84,
      align: 'center',
      slots: { default: 'stocktype' },
    },
    {
      field: 'ToStockType',
      title: '目标类型',
      width: 84,
      align: 'center',
      slots: { default: 'tostocktype' },
    },
    {
      field: 'StorageLocation',
      title: 'SAP位置',
      minWidth: 88,
    },

    { field: 'ToLocation', title: '目标位置', minWidth: 88 },
    { field: 'GoodsMovementType', title: '调整类型', minWidth: 108 },
    { field: 'MoveQty', title: '调整件数', width: 96 },
    {
      field: 'GoodsMovementReasonCode',
      title: '调整原因',
      minWidth: 150,
    },
    {
      field: 'ActualExpiration',
      title: '当前效期',
      width: 88,
      formatter: 'formatDate',
      visible: false,
    },
    {
      field: 'ToExpiration',
      title: '目标效期',
      width: 88,
      formatter: 'formatDate',
      visible: false,
    },
    /* {
      field: 'CreateBy',
      title: '添加人',
      minWidth: 100,
    },
    {
      field: 'CreateDate',
      title: '添加日期',
      minWidth: 150,
      sortable: true,
    }, */
  ],
  filterConfig: {
    remote: false,
  },
  footerRowStyle: { 'font-weight': 'bold ' },
  footerMethod({ columns, data }) {
    return [
      columns.map((column, columnIndex) => {
        if (columnIndex === 0) {
          return `合计`;
        }
        if (columnIndex === 1) {
          return `${data.length} 项`;
        }
        if (['MoveQty'].includes(column.field)) {
          return sumNum(data, column.field);
        }
        return null;
      }),
    ];
  },
  height: 'auto',
  keepSource: true,
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    ajax: {
      query: async () => {
        return await getStockMovementItem(props.id);
      },
    },
    seq: true,
  },
  showFooter: true,
  size: 'mini',
  sortConfig: {
    remote: false,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
  scrollX: { enabled: true, gt: 0 },
  scrollY: { enabled: true, mode: 'wheel', gt: 30, oSize: 0 },
  menuConfig: {
    trigger: 'cell',
    body: {
      options: [
        [
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuCopy',
            disabled: false,
            name: '复制单元格',
            prefixConfig: {
              icon: 'vxe-icon-copy',
            },
            visible: true,
          },
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuRefresh',
            disabled: false,
            name: '刷新',
            prefixConfig: { icon: 'vxe-icon-refresh' },
            visible: true,
          },
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuExport',
            disabled: false,
            name: '导出明细',
            prefixConfig: { icon: 'vxe-icon-download' },
            visible: true,
          },
        ],
      ],
    },
    className: 'font-medium shadow rounded-md',
  },
});
const gridEvents: VxeGridListeners = {
  /* cellDblclick({ row }) {
    openCSARDrawer(row.Guid);
  }, */
  menuClick({ menu }) {
    switch (menu.code) {
      case 'menuExport': {
        handleExport();
        break;
      }
      default: {
        break;
      }
    }
  },
};
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions, gridEvents });
function sumNum(list: any[], field: string) {
  let count = 0;
  list.forEach((item) => {
    count += Number(item[field]);
  });
  return count;
}
function handleExport() {
  gridApi.grid.exportData({
    filename: `库存调整_${props.doc}`,
    sheetName: 'Sheet1',
    type: 'xlsx',
    isFooter: false,
    columnFilterMethod: ({ column }) => {
      return column.field !== undefined;
    },
  });
}
function handleSuccess() {
  gridApi.grid.commitProxy('query');
}
function handleRemoveCheck() {
  if (props.isClosed) {
    message.error(`调整单据已执行，无法移除明细！`);
    return;
  }
  const checkedRecords = gridApi.grid
    .getCheckboxRecords()
    .map((item: any) => item.Guid);
  if (checkedRecords.length === 0) {
    message.info(`请勾选需要移除的调整明细！`);
    return;
  }
  Modal.confirm({
    content: `请确认是否移除勾选的调整明细？`,
    /* icon: createVNode(ExclamationCircleOutlined), */
    onCancel() {},
    onOk() {
      handleDelete(checkedRecords);
    },
    title: `移除调整明细`,
    okButtonProps: { danger: true },
  });
}
function handleDelete(ids: Array<string>) {
  stockMovementItemDelete(props.id, ids)
    .then(() => {
      handleSuccess();
    })
    .catch(() => {})
    .finally(() => {});
}

function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
}
</script>

<template>
  <div class="flex h-full flex-col">
    <Grid>
      <template #toolbar_left>
        <div class="hidden pl-1 text-base font-medium md:block">
          <span class="mr-2">调整明细</span>
        </div>
        <a-button class="mr-1" size="small" @click="clearFilterAndSort()">
          <AntClear class="size-5" />
        </a-button>
        <a-button
          v-access:code="'BWStockMovement:ItemAdd'"
          v-if="!props.isLocked"
          :disabled="props.isClosed"
          class="ml-2"
          size="small"
          type="primary"
          @click="openItemImportModal()"
        >
          添加调整明细
        </a-button>
        <a-button
          v-access:code="'BWStockMovement:ItemAdd'"
          v-if="!props.isLocked"
          :disabled="props.isClosed"
          class="ml-2"
          size="small"
          @click="openBlockItemImportModal()"
        >
          批量解冻
        </a-button>
      </template>
      <template #toolbar-tools>
        <!-- <a-button danger size="small" type="primary"> -->
        <!--  </a-button> -->
        <VbenButton
          v-access:code="'BWStockMovement:ItemRemove'"
          v-if="!props.isLocked"
          :disabled="props.isClosed"
          class="h-7 w-7 rounded-full px-1"
          variant="destructive"
          @click="handleRemoveCheck()"
        >
          <AntDelete class="size-5" />
        </VbenButton>
      </template>
      <template #fromMaterial="{ row }">
        <a
          v-if="row.MatCode"
          class="text-blue-500"
          @click="openSkuDrawer(row.MatCode)"
        >
          {{ row.MatCode }}
        </a>
      </template>
      <template #toMaterial="{ row }">
        <a
          v-if="row.ToMaterial"
          class="text-blue-500"
          @click="openSkuDrawer(row.ToMaterial)"
        >
          {{ row.ToMaterial }}
        </a>
      </template>
      <template #fromBatch="{ row }">
        <a
          v-if="row.BatchNo"
          class="text-blue-500"
          @click="openBatchDrawer(row.MatCode, row.BatchNo)"
        >
          {{ row.BatchNo }}
        </a>
      </template>
      <template #toBatch="{ row }">
        <a
          v-if="row.ToBatch"
          class="text-blue-500"
          @click="openBatchDrawer(row.ToMaterial, row.ToBatch)"
        >
          {{ row.ToBatch }}
        </a>
      </template>
      <template #batchmanage="{ row }">
        <Tag :color="row.isBatchManagementRequired ? 'green' : 'blue'">
          {{ row.isBatchManagementRequired ? '是' : '否' }}
        </Tag>
      </template>
      <template #stocktype="{ row }">
        <Tag :color="row.StockType === '非限制' ? 'green' : 'red'">
          {{ row.StockType }}
        </Tag>
      </template>
      <template #tostocktype="{ row }">
        <Tag
          v-if="row.ToStockType"
          :color="row.ToStockType === '非限制' ? 'green' : 'red'"
        >
          {{ row.ToStockType }}
        </Tag>
      </template>
    </Grid>
    <ItemImportModal @success="handleSuccess()" />
    <BlockItemImportModal @success="handleSuccess()" />
    <SkuDrawer />
    <BatchDrawer />
  </div>
</template>
