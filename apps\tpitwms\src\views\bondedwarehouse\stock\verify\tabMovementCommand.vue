<script setup lang="ts">
import type { VxeGridProps } from '#/adapter';

import { reactive } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';
import { AntClear } from '@vben/icons';

import { Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import { getStockMovementCommandList } from '#/api/bondedwarehouse/stock';
import batchDrawer from '#/views/sapneo/master/batch/batchDrawer.vue';
import skuDrawer from '#/views/sapneo/master/product/skuDrawer.vue';

const searchField: any = reactive({
  /* SAPDocNo: undefined, */
  MaterialDocumentHeaderText: undefined,
});
const [SkuDrawer, skuDrawerApi] = useVbenDrawer({
  connectedComponent: skuDrawer,
});
const [BatchDrawer, batchDrawerApi] = useVbenDrawer({
  connectedComponent: batchDrawer,
});
function openSkuDrawer(id: string) {
  skuDrawerApi.setData({ id });
  skuDrawerApi.open();
}
function openBatchDrawer(sku: string, batch: string) {
  batchDrawerApi.setData({ sku, batch });
  batchDrawerApi.open();
}

const gridOptions = reactive<VxeGridProps>({
  id: 'BWStockMovementCommandList',
  checkboxConfig: { highlight: true, range: true },
  columns: [
    {
      fixed: 'left',
      title: '#',
      type: 'seq',
      width: 60,
    },
    {
      field: 'VerifyStatus',
      title: '核销状态',
      width: 88,
      align: 'center',
      filters: [
        { label: '待核销', value: false },
        { label: '已核销', value: true },
      ],
      filterMultiple: false,
      slots: { default: 'verifystatus' },
    },
    {
      field: 'SAPDocNo',
      title: 'SAP调整单',
      width: 120,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    { field: 'SAPDocItemNo', title: 'SAP调整项号', width: 100 },
    {
      field: 'MaterialDocumentHeaderText',
      title: '抬头文本',
      minWidth: 150,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    { field: 'GoodsMovementType', title: '指令类型', width: 150 },
    { field: 'MoveQty', title: '调整数量', width: 100 },
    { field: 'Plant', title: '工厂', width: 80 },
    { field: 'StorageLocation', title: 'SAP位置', width: 108 },
    {
      field: 'MatCode',
      title: '物料编号',
      width: 88,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      slots: { default: 'material' },
    },
    {
      field: 'StockType',
      title: '库存类型',
      width: 88,
      filters: [
        { label: '非限制', value: 'UNR' },
        { label: '冻结', value: 'BLK' },
      ],
      filterMultiple: false,
      slots: { default: 'stocktype' },
    },
    {
      field: 'CreateDate',
      title: '创建日期',
      width: 150,
      formatter: 'formatDateTime',
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'VerifyDate',
      title: '核销日期',
      width: 150,
      formatter: 'formatDateTime',
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
  ],
  filterConfig: {
    remote: true,
  },
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: ({ filters, page, sorts }) => {
        const queryParams: any = Object.assign({}, page);
        // 处理排序
        if (sorts.length === 0) {
          queryParams.sort = 'SAPDocNo desc, Len(SAPDocItemNo), SAPDocItemNo';
        } else {
          const sortItem = sorts.map((item) => {
            const newItem = `${item.field} ${item.order}`;
            return newItem;
          });
          queryParams.sort = sortItem.join(',');
        }
        // 处理筛选
        Object.getOwnPropertyNames(searchField).forEach((key) => {
          searchField[key] = undefined;
        });
        filters.forEach(({ field, values }) => {
          queryParams[field] = values.join(',');
          if (Object.prototype.hasOwnProperty.call(searchField, field)) {
            const jsonObject = JSON.parse(values.toString());
            searchField[field] = jsonObject.text;
          }
        });
        return getStockMovementCommandList(queryParams);
      },
    },
    seq: true,
  },
  showFooter: false,
  size: 'mini',
  sortConfig: {
    remote: true,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
  scrollX: { enabled: true, gt: 0 },
  scrollY: { enabled: true, mode: 'wheel', gt: 30, oSize: 0 },
});

const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });

/* function handleSuccess() {
  gridApi.grid.commitProxy('query');
} */
function searchEvent(field: string) {
  const column = gridApi.grid.getColumnByField(field);
  if (column) {
    // 修改第一个选项为勾选状态
    const option = column.filters[0];
    if (!option) {
      return;
    }

    if (searchField[field]) {
      option.data = { type: '1', text: searchField[field] };
      option.value = JSON.stringify(option.data);
      option.checked = true;
    } else {
      option.data = { type: '0', text: undefined };
      option.checked = false;
    }

    // 修改条件之后，需要手动调用 updateData 处理表格数据
    gridApi.grid.updateData();
    gridApi.grid.commitProxy('query');
  }
}
function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
  gridApi.grid.commitProxy('query');
}
</script>

<template>
  <div class="flex h-full flex-col">
    <Grid>
      <template #toolbar_left>
        <a-button class="mr-1" @click="clearFilterAndSort()">
          <AntClear class="size-5" />
        </a-button>
        <div class="hidden pl-1 md:block">
          <!-- <a-input-search
            v-model:value="searchField.SAPDocNo"
            allow-clear
            class="mr-2 w-48"
            placeholder="SAP调整单"
            @search="searchEvent('SAPDocNo', 'Input')"
          /> -->
          <a-input-search
            v-model:value="searchField.MaterialDocumentHeaderText"
            allow-clear
            class="mr-2 w-60"
            placeholder="抬头文本"
            @search="searchEvent('MaterialDocumentHeaderText')"
          />
        </div>
      </template>
      <template #toolbar-tools> </template>
      <template #material="{ row }">
        <a
          v-if="row.MatCode"
          class="text-blue-500"
          @click="openSkuDrawer(row.MatCode)"
        >
          {{ row.MatCode }}
        </a>
      </template>
      <template #batch="{ row }">
        <a
          v-if="row.BatchNo"
          class="text-blue-500"
          @click="openBatchDrawer(row.MatCode, row.BatchNo)"
        >
          {{ row.BatchNo }}
        </a>
      </template>
      <template #stocktype="{ row }">
        <Tag :color="row.StockType === 'UNR' ? 'green' : 'red'">
          {{ row.StockType === 'UNR' ? '非限制' : '冻结' }}
        </Tag>
      </template>
      <template #verifystatus="{ row }">
        <Tag :color="row.VerifyStatus ? 'green' : 'blue'">
          {{ row.VerifyStatus ? '已核销' : '待核销' }}
        </Tag>
      </template>
    </Grid>
    <SkuDrawer />
    <BatchDrawer />
  </div>
</template>
