import { requestClient, sleep } from '#/api/request';

/**
 * 入库明细 相关API
 */
export async function getRecord4BWInReceived(obj: object) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/inbound/record/getReceived', {
    params: obj,
  });
}

export async function getRecord4BWInCSAR(obj: object) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/inbound/record/getCSAR', {
    params: obj,
  });
}
