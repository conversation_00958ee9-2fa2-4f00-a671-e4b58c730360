import { requestClient, sleep } from '#/api/request';

/**
 * 出库拣货 相关API
 */
export async function getBWOutPickingList(obj: object) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/outbound/picking/getList', {
    params: obj,
  });
}

export async function bwOutPickingCreate(params: object) {
  await sleep(100);
  return requestClient.post('/bondedwarehouse/outbound/picking/create', params);
}

export async function bwOutPickingDelete(id: string) {
  await sleep(100);
  return requestClient.post('/bondedwarehouse/outbound/picking/del', { id });
}

export async function getOutPickingHead(id: string) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/outbound/picking/head/getData', {
    params: { id },
  });
}

export async function bwOutPickingHeadUpdate(params: object) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/outbound/picking/head/update',
    params,
  );
}

export async function getOutPickingOPList(id: string) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/outbound/picking/op/getList', {
    params: { id },
  });
}

export async function getOutPickingSampleOPList(id: string) {
  await sleep(100);
  return requestClient.get(
    '/bondedwarehouse/outbound/picking/sample/op/getList',
    {
      params: { id },
    },
  );
}

export async function getOutPickingSampleDetailByIds(
  id: string,
  ids: Array<string>,
) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/outbound/picking/sample/getDetailByIds',
    {
      id,
      ids,
    },
  );
}

export async function bwOutPickingItemAdd(
  id: string,
  ids: Array<string>,
  values: Array<object>,
  typ: object,
) {
  await sleep(100);
  return requestClient.post('/bondedwarehouse/outbound/picking/item/add', {
    id,
    ids,
    values,
    typ,
  });
}

export async function bwOutPickingItemSampleAdd(
  id: string,
  ids: Array<string>,
  typ: object,
) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/outbound/picking/item/sample/add',
    {
      id,
      ids,
      typ,
    },
  );
}

export async function bwOutPickingItemSampleRemove(
  id: string,
  ids: Array<string>,
) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/outbound/picking/item/sample/remove',
    {
      id,
      ids,
    },
  );
}

export async function getOutPickingItem(id: string) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/outbound/picking/item/getList', {
    params: { id },
  });
}

export async function bwOutPickingItemRemove(id: string, ids: Array<string>) {
  await sleep(100);
  return requestClient.post('/bondedwarehouse/outbound/picking/item/remove', {
    id,
    ids,
  });
}

export async function bwOutPickingItemUpdate(params: object) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/outbound/picking/item/update',
    params,
  );
}

export async function bwOutPickingItemStatusUpdate(
  id: string,
  ids: Array<string>,
  typ: number,
) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/outbound/picking/item/status/update',
    { id, ids, typ },
  );
}

export async function bwOutPickingOutboundBinding(id: string, oid: string) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/outbound/picking/outbound/binding',
    {
      id,
      oid,
    },
  );
}

export async function bwOutPickingSampleOutboundBinding(
  id: string,
  oid: string,
) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/outbound/picking/sample/outbound/binding',
    {
      id,
      oid,
    },
  );
}
export async function bwOutPickingSampleOutboundUnBinding(
  id: string,
  ids: Array<string>,
) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/outbound/picking/sample/outbound/unbinding',
    {
      id,
      ids,
    },
  );
}

export async function bwOutPickingItemOBDBinding(id: string, oid: string) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/outbound/picking/item/outbound/binding',
    {
      id,
      oid,
    },
  );
}

export async function bwOutPickingOutboundUnBinding(id: string) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/outbound/picking/outbound/unbinding',
    {
      id,
    },
  );
}

export async function bwOutPickingPGIGetData(id: string) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/outbound/picking/pgi/getData', {
    params: { id },
  });
}

export async function bwOutPickingHandlePGI(id: string) {
  await sleep(100);
  return requestClient.post('/bondedwarehouse/outbound/picking/pgi', {
    id,
  });
}
