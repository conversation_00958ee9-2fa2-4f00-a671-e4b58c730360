<script lang="ts" setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';

import { useVbenModal } from '@vben/common-ui';

import { Result } from 'ant-design-vue';

import { useVbenForm, z } from '#/adapter';
import {
  stockMovementCreate,
  stockMovementGetDoc,
} from '#/api/bondedwarehouse/stock';

const emit = defineEmits(['success']);

const docId = ref(undefined);
const docNo = ref(undefined);
const router = useRouter();
const [Form, formApi] = useVbenForm({
  handleSubmit: onSubmit,
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  schema: [
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 24,
      },
      fieldName: 'DocNo',
      label: '调整单号',
      rules: z
        .string()
        .min(8, { message: '至少需要8个字符' })
        .regex(/^[A-Z0-9]+$/, { message: '只能包含大写字母或数字' }),
    },
  ],
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  fullscreenButton: false,
  closeOnClickModal: false,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await formApi.submitForm();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      modalApi.setState({ showCancelButton: true });
      modalApi.setState({ showConfirmButton: true });
      docId.value = undefined;
      docNo.value = undefined;
      formApi.resetForm();
      fetch();
    }
  },
  title: '创建库存调整单',
});

async function onSubmit(values: Record<string, any>) {
  const check = await formApi.validate();
  if (check.valid) {
    modalApi.setState({ confirmLoading: true });
    modalApi.setState({ loading: true });
    stockMovementCreate(Object.assign({}, values))
      .then((res) => {
        modalApi.setState({ showCancelButton: false });
        modalApi.setState({ showConfirmButton: false });
        docId.value = res;
      })
      .catch(() => {})
      .finally(() => {
        modalApi.setState({ loading: false });
        modalApi.setState({ confirmLoading: false });
      });
  }
}
function handleClose() {
  modalApi.close();
  emit('success');
}
function handleGoDetail() {
  modalApi.close();
  router.push({
    name: 'BWStockMovementDetail',
    params: { id: docId.value },
  });
  emit('success');
}
async function fetch() {
  docNo.value = await stockMovementGetDoc();
  formApi.setFieldValue('DocNo', docNo.value);
}
</script>
<template>
  <Modal>
    <Result v-if="docId" status="success" title="创建成功">
      <template #extra>
        <a-button type="primary" @click="handleGoDetail()">
          查看调整单据
        </a-button>
        <a-button @click="handleClose()">关闭</a-button>
      </template>
    </Result>
    <Form v-else class="mt-4" />
  </Modal>
</template>
