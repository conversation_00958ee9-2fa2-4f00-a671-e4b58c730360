<script setup lang="ts">
import type { VxeGridProps } from '#/adapter';

import { reactive } from 'vue';

import { Page, useVbenDrawer } from '@vben/common-ui';
import { AntClear } from '@vben/icons';

import { Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import { getBatchList } from '#/api/sapneo';

import customerDrawer from '../customer/customerDrawer.vue';
import skuDrawer from '../product/skuDrawer.vue';
import batchDrawer from './batchDrawer.vue';

const [CustomerDrawer, customerDrawerApi] = useVbenDrawer({
  connectedComponent: customerDrawer,
});
const [SkuDrawer, skuDrawerApi] = useVbenDrawer({
  connectedComponent: skuDrawer,
});
const [BatchDrawer, batchDrawerApi] = useVbenDrawer({
  connectedComponent: batchDrawer,
});
function openCustomerDrawer(id: string) {
  customerDrawerApi.setData({ id });
  customerDrawerApi.open();
}
function openSkuDrawer(id: string) {
  skuDrawerApi.setData({ id });
  skuDrawerApi.open();
}
function openBatchDrawer(sku: string, batch: string) {
  batchDrawerApi.setData({ sku, batch });
  batchDrawerApi.open();
}

const searchField: any = reactive({
  Material: undefined,
  Batch: undefined,
});

const gridOptions = reactive<VxeGridProps>({
  id: 'NEOBatchMaster',
  columns: [
    {
      title: '#',
      type: 'seq',
      width: 60,
      fixed: 'left',
    },
    {
      field: 'Material',
      title: '产品编号',
      width: 108,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      slots: { default: 'skucode' },
    },
    {
      field: 'Batch',
      title: '批次',
      width: 100,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      slots: { default: 'batch' },
    },
    {
      field: 'RelatedBatches',
      title: '关联批次',
      minWidth: 136,
      filters: [{ data: null }],
      filterRender: { name: 'FilterInput' },
    },
    {
      field: 'Plants',
      title: '工厂',
      minWidth: 168,
      filters: [{ data: null }],
      filterRender: { name: 'FilterInput' },
    },
    /* {
      field: 'BatchIsMarkedForDeletion',
      title: '删除标记',
      minWidth: 96,
      align: 'center',
      slots: { default: 'deletion' },
      filters: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      filterMultiple: false,
    }, */
    {
      field: 'MatlBatchIsInRstrcdUseStock',
      title: '限制使用标记',
      width: 120,
      align: 'center',
      slots: { default: 'rstrcd' },
      filters: [
        { label: '受限制', value: true },
        { label: '非限制', value: false },
      ],
      filterMultiple: false,
    },
    {
      field: 'ManufactureDate',
      title: '制造日期',
      formatter: 'formatDate',
      width: 108,
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'ShelfLifeExpirationDate',
      title: '批次效期',
      formatter: 'formatDate',
      width: 100,
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'MatlBatchAvailabilityDate',
      title: '生效日期',
      formatter: 'formatDate',
      width: 100,
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'NextInspectionDate',
      title: '下次检查日期',
      formatter: 'formatDate',
      width: 128,
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'Supplier',
      title: '供应商',
      width: 100,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      slots: { default: 'supplier' },
    },
    {
      field: 'BatchBySupplier',
      title: '供应商批次',
      width: 100,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'CountryOfOrigin',
      title: '原产国',
      width: 100,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'RegionOfOrigin',
      title: '原产地',
      width: 100,
      visible: false,
    },
    {
      field: 'CreateDate',
      title: '创建日期',
      minWidth: 150,
      formatter: 'formatDateTime',
      sortable: true,
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'UpdateDate',
      title: '更新日期',
      minWidth: 150,
      formatter: 'formatDateTime',
      sortable: true,
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'FreeDefinedDate1',
      title: '自定义日期1',
      minWidth: 150,
      sortable: true,
      visible: false,
    },
    {
      field: 'FreeDefinedDate2',
      title: '自定义日期2',
      minWidth: 150,
      sortable: true,
      visible: false,
    },
    {
      field: 'FreeDefinedDate3',
      title: '自定义日期3',
      minWidth: 150,
      sortable: true,
      visible: false,
    },
    {
      field: 'FreeDefinedDate4',
      title: '自定义日期4',
      minWidth: 150,
      sortable: true,
      visible: false,
    },
    {
      field: 'FreeDefinedDate5',
      title: '自定义日期5',
      minWidth: 150,
      sortable: true,
      visible: false,
    },
    {
      field: 'FreeDefinedDate6',
      title: '自定义日期6',
      minWidth: 150,
      sortable: true,
      visible: false,
    },
    {
      field: 'BatchExtWhseMgmtInternalId',
      title: 'BatchExtWhseMgmtInternalId',
      width: 260,
      visible: false,
    },
    {
      field: 'ClassInternalID',
      title: 'ClassInternalID',
      width: 136,
      visible: false,
    },
  ],
  filterConfig: {
    remote: true,
  },
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: ({ filters, page, sorts }) => {
        const queryParams: any = Object.assign({}, page);
        // 处理排序条件
        if (sorts.length === 0) {
          queryParams.sort = 'CreateDate desc';
        } else {
          const sortItem = sorts.map((item) => {
            const newItem = `${item.field} ${item.order}`;
            return newItem;
          });
          queryParams.sort = sortItem.join(',');
        }
        // 处理筛选
        Object.getOwnPropertyNames(searchField).forEach((key) => {
          searchField[key] = undefined;
        });
        filters.forEach(({ field, values }) => {
          queryParams[field] = values.join(',');
          if (Object.prototype.hasOwnProperty.call(searchField, field)) {
            const jsonObject = JSON.parse(values.toString());
            searchField[field] = jsonObject.text;
          }
        });
        return getBatchList(queryParams);
      },
    },
    seq: true,
  },
  scrollY: { enabled: true, gt: 0 },
  size: 'mini',
  sortConfig: {
    remote: true,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
});
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });
function searchEvent(field: string) {
  const column = gridApi.grid.getColumnByField(field);
  if (column) {
    // 修改第一个选项为勾选状态
    const option = column.filters[0];
    if (!option) {
      return;
    }

    if (searchField[field]) {
      option.data = { type: '1', text: searchField[field] };
      option.value = JSON.stringify(option.data);
      option.checked = true;
    } else {
      option.data = { type: '0', text: undefined };
      option.checked = false;
    }

    // 修改条件之后，需要手动调用 updateData 处理表格数据
    gridApi.grid.updateData();
    gridApi.grid.commitProxy('query');
  }
}
function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
  gridApi.grid.commitProxy('query');
}
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar_left>
        <a-button class="mr-1" @click="clearFilterAndSort()">
          <AntClear class="size-5" />
        </a-button>
        <div class="hidden pl-1 md:block">
          <a-input-search
            v-model:value="searchField.Material"
            allow-clear
            class="mr-2 w-48"
            placeholder="产品编号"
            @search="searchEvent('Material')"
          />
          <a-input-search
            v-model:value="searchField.Batch"
            allow-clear
            class="mr-2 w-48"
            placeholder="批次"
            @search="searchEvent('Batch')"
          />
        </div>
      </template>
      <template #toolbar-tools> </template>
      <template #skucode="{ row }">
        <a-button size="small" type="link" @click="openSkuDrawer(row.Material)">
          {{ row.Material }}
        </a-button>
        <!-- <a class="text-blue-500" @click="openSkuDrawer(row.Material)">
          {{ row.Material }}
        </a> -->
      </template>
      <template #batch="{ row }">
        <a-button
          size="small"
          type="link"
          @click="openBatchDrawer(row.Material, row.Batch)"
        >
          {{ row.Batch }}
        </a-button>
        <!-- <a
          class="text-blue-500"
          @click="openBatchDrawer(row.Material, row.Batch)"
        >
          {{ row.Batch }}
        </a> -->
      </template>
      <template #supplier="{ row }">
        <a-button
          v-if="row.Supplier"
          size="small"
          type="link"
          @click="openCustomerDrawer(row.Supplier)"
        >
          {{ row.Supplier }}
        </a-button>
        <!-- <a
          v-if="row.Supplier"
          class="text-blue-500"
          @click="openCustomerDrawer(row.Supplier)"
        >
          {{ row.Supplier }}
        </a> -->
      </template>
      <template #rstrcd="{ row }">
        <Tag :color="row.MatlBatchIsInRstrcdUseStock ? 'red' : 'green'">
          {{ row.MatlBatchIsInRstrcdUseStock ? '受限制' : '非限制' }}
        </Tag>
      </template>
    </Grid>
    <CustomerDrawer />
    <SkuDrawer />
    <BatchDrawer />
  </Page>
</template>
