<script setup lang="ts">
import type { VxeGridListeners, VxeGridProps } from '#/adapter';

import { defineProps, reactive } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';
import { AntClear } from '@vben/icons';

import { Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import { getInOrderCSARList } from '#/api/bondedwarehouse/inbound';

import csarDrawer from './CSARDrawer.vue';

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
  hbl: {
    type: String,
    default: '',
  },
  isClosed: {
    type: Boolean,
    default: false,
  },
});

const [CSARDrawer, CSARDrawerApi] = useVbenDrawer({
  connectedComponent: csarDrawer,
});
function openCSARDrawer(id: string) {
  CSARDrawerApi.setData({ id });
  CSARDrawerApi.open();
}

const gridOptions = reactive<VxeGridProps>({
  id: 'BWInOrderCSQC',
  /* checkboxConfig: { highlight: true, range: true }, */
  columns: [
    /* { type: 'checkbox', width: 48, fixed: 'left' }, */
    {
      fixed: 'left',
      title: '#',
      type: 'seq',
      width: 60,
    },
    {
      field: 'MatCode',
      title: '物料编号',
      width: 88,
    },
    { field: 'BatchNo', title: '实收批号', width: 88 },
    {
      field: 'Status',
      width: 88,
      title: '检查状态',
      align: 'center',
      slots: { default: 'status' },
    },
    { field: 'CName', title: '中文品名', minWidth: 100 },
    { field: 'GuidingWords', title: '引导语', minWidth: 100 },
    { field: 'ExpireDate', title: '限制使用日期', minWidth: 100 },
    { field: 'HP', title: 'HP No.', minWidth: 100 },
    { field: 'CName2', title: '备案信息', minWidth: 100 },
    { field: 'SPF', title: '防晒值', minWidth: 100 },
    { field: 'MadeIn', title: '原产国', minWidth: 100 },
    { field: 'Formula', title: '配方号', minWidth: 100 },
    { field: 'BarCode', title: '条形码', minWidth: 100 },
    { field: 'Others', title: '其他特殊情况', minWidth: 200 },
    {
      field: 'UpdateBy',
      title: '检查人',
      minWidth: 100,
    },
    {
      field: 'UpdateDate',
      title: '更新日期',
      minWidth: 150,
      sortable: true,
    },
  ],
  filterConfig: {
    remote: false,
  },
  height: 'auto',
  keepSource: true,
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    ajax: {
      query: async () => {
        return await getInOrderCSARList(props.id);
      },
    },
    seq: true,
  },
  showFooter: false,
  size: 'mini',
  sortConfig: {
    remote: false,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
  scrollX: { enabled: true, gt: 0 },
  scrollY: { enabled: true, mode: 'wheel', gt: 30, oSize: 0 },
  menuConfig: {
    trigger: 'cell',
    body: {
      options: [
        [
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuCopy',
            disabled: false,
            name: '复制单元格',
            prefixConfig: {
              icon: 'vxe-icon-copy',
            },
            visible: true,
          },
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuRefresh',
            disabled: false,
            name: '刷新',
            prefixConfig: { icon: 'vxe-icon-refresh' },
            visible: true,
          },
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuExport',
            disabled: false,
            name: '导出明细',
            prefixConfig: { icon: 'vxe-icon-download' },
            visible: true,
          },
        ],
      ],
    },
    className: 'font-medium shadow rounded-md',
  },
});
const gridEvents: VxeGridListeners = {
  cellDblclick({ row }) {
    openCSARDrawer(row.Guid);
  },
  menuClick({ menu }) {
    switch (menu.code) {
      case 'menuExport': {
        handleExport();
        break;
      }
      default: {
        break;
      }
    }
  },
};
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions, gridEvents });

function handleExport() {
  gridApi.grid.exportData({
    filename: `CSARQC检查记录_${props.hbl}`,
    sheetName: 'Sheet1',
    type: 'xlsx',
    isFooter: false,
    columnFilterMethod: ({ column }) => {
      return column.field !== undefined;
    },
  });
}
function handleSuccess() {
  gridApi.grid.commitProxy('query');
}

function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
}
</script>

<template>
  <div class="flex h-full flex-col">
    <Grid>
      <template #toolbar_left>
        <div class="hidden pl-1 text-base font-medium md:block">
          <span class="mr-2">CSAR检查</span>
        </div>
        <a-button class="mr-1" size="small" @click="clearFilterAndSort()">
          <AntClear class="size-5" />
        </a-button>
      </template>
      <template #toolbar-tools> </template>
      <template #status="{ row }">
        <Tag :color="row.Status ? 'green' : 'blue'">
          {{ row.Status ? '已检查' : '待检查' }}
        </Tag>
      </template>
    </Grid>
    <CSARDrawer @success="handleSuccess()" />
  </div>
</template>
