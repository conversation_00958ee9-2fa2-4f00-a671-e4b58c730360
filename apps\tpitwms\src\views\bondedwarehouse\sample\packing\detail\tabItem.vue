<script setup lang="ts">
import type { VxeGridListeners, VxeGridProps } from '#/adapter';

import { defineProps, reactive } from 'vue';
import { useRouter } from 'vue-router';

import { useVbenModal } from '@vben/common-ui';
import { AntClear } from '@vben/icons';

import { message, Modal, Popconfirm } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import {
  bwOutSampleIteRemove,
  getOutSamplePackingItem,
} from '#/api/bondedwarehouse/outbound';

import samplePackingModal from './samplePackingModal.vue';

/* import csarDrawer from './CSARDrawer.vue'; */

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
  doc: {
    type: String,
    default: '',
  },
  isClosed: {
    type: Boolean,
    default: false,
  },
});
const router = useRouter();
const [SamplePackingModal, SamplePackingModalApi] = useVbenModal({
  connectedComponent: samplePackingModal,
});

function openPalletLoadingModa() {
  SamplePackingModalApi.setData({ id: props.id });
  SamplePackingModalApi.open();
}

const gridOptions = reactive<VxeGridProps>({
  id: 'BWOutSamplePackingItem',
  checkboxConfig: { highlight: true, range: true },
  columns: [
    { type: 'checkbox', width: 48, fixed: 'left' },
    {
      fixed: 'left',
      title: '#',
      type: 'seq',
      width: 60,
    },

    { field: 'BLNo', title: '出库提单BL', minWidth: 150 },
    { field: 'YUB', title: 'SAP销售订单', minWidth: 100 },
    { field: 'Invoice', title: '发票号', minWidth: 100 },
    { field: 'MatCode', title: '物料编号', minWidth: 100 },
    { field: 'BatchNo', title: '批号', minWidth: 100 },
    { field: 'ActualExpiration', title: '限用日期', minWidth: 100 },
    { field: 'SampleQty', title: '留样件数', minWidth: 100 },
    { field: 'oCName', title: '中文名称', minWidth: 270 },
    { field: 'oBrandName', title: '品牌', width: 100 },
  ],
  filterConfig: {
    remote: false,
  },
  footerRowStyle: { 'font-weight': 'bold ' },
  footerMethod({ columns, data }) {
    return [
      columns.map((column, columnIndex) => {
        if (columnIndex === 0) {
          return `合计`;
        }
        if (columnIndex === 1) {
          return `${data.length} 项`;
        }
        if (['SampleQty'].includes(column.field)) {
          return sumNum(data, column.field);
        }
        return null;
      }),
    ];
  },
  height: 'auto',
  keepSource: true,
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    ajax: {
      query: async () => {
        return await getOutSamplePackingItem(props.id);
      },
    },
    seq: true,
  },
  showFooter: true,
  size: 'mini',
  sortConfig: {
    remote: false,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
  scrollX: { enabled: true, gt: 0 },
  scrollY: { enabled: true, mode: 'wheel', gt: 30, oSize: 0 },
  menuConfig: {
    trigger: 'cell',
    body: {
      options: [
        [
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuCopy',
            disabled: false,
            name: '复制单元格',
            prefixConfig: {
              icon: 'vxe-icon-copy',
            },
            visible: true,
          },
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuRefresh',
            disabled: false,
            name: '刷新',
            prefixConfig: { icon: 'vxe-icon-refresh' },
            visible: true,
          },
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuExport',
            disabled: false,
            name: '导出明细',
            prefixConfig: { icon: 'vxe-icon-download' },
            visible: true,
          },
        ],
      ],
    },
    className: 'font-medium shadow rounded-md',
  },
});
const gridEvents: VxeGridListeners = {
  menuClick({ menu }) {
    switch (menu.code) {
      case 'menuExport': {
        handleExport();
        break;
      }
      default: {
        break;
      }
    }
  },
};
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions, gridEvents });
function sumNum(list: any[], field: string) {
  let count = 0;
  list.forEach((item) => {
    count += Number(item[field]);
  });
  return count;
}
function handleExport() {
  gridApi.grid.exportData({
    filename: `留样装箱明细_${props.doc}`,
    sheetName: 'Sheet1',
    type: 'xlsx',
    isFooter: false,
    columnFilterMethod: ({ column }) => {
      return column.field !== undefined;
    },
  });
}
function handleSuccess() {
  gridApi.grid.commitProxy('query');
}
function handleRemove() {
  if (props.isClosed) {
    message.error(`留样外箱已被拣货，无法调整！`);
    return;
  }
  const checkedRecords = gridApi.grid
    .getCheckboxRecords()
    .map((item: any) => item.Guid);
  if (checkedRecords.length === 0) {
    message.info(`请勾选需要移除的装箱明细！`);
    return;
  }
  Modal.confirm({
    content: `请确认是否移除勾选的装箱明细？`,
    onCancel() {},
    onOk() {
      handleDelete(checkedRecords);
    },
    title: `移除装箱明细`,
    okButtonProps: { danger: true },
  });
}
function handleDelete(ids: Array<string>) {
  bwOutSampleIteRemove(props.id, ids)
    .then(() => {
      handleSuccess();
    })
    .catch(() => {})
    .finally(() => {});
}

function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
}
</script>

<template>
  <div class="flex h-full flex-col">
    <Grid>
      <template #toolbar_left>
        <div class="hidden pl-1 text-base font-medium md:block">
          <span class="mr-2">装箱明细</span>
        </div>
        <a-button class="mr-1" size="small" @click="clearFilterAndSort()">
          <AntClear class="size-5" />
        </a-button>
        <a-button
          :disabled="props.isClosed"
          class="ml-2"
          size="small"
          type="primary"
          @click="openPalletLoadingModa()"
        >
          装箱作业
        </a-button>
        <!-- <a-button
          :disabled="props.isClosed"
          class="ml-2"
          size="small"
          @click="openPalletRemoveDrawer()"
        >
          卸载托盘
        </a-button> -->
      </template>
      <template #toolbar-tools>
        <a-button
          class="mr-2"
          size="small"
          :disabled="props.isClosed"
          @click="handleRemove()"
        >
          装箱移除
        </a-button>
      </template>
      <template #pickingDoc="{ row }">
        <Popconfirm
          cancel-text="取消"
          ok-text="查看拣货单"
          title="跳转提示"
          @confirm="
            router.push({
              name: 'BWOutPickingDetail',
              params: { id: row.PickingHeadID },
            })
          "
        >
          <a class="text-blue-500" @click.prevent>
            {{ row.PickingDocNo }}
          </a>
        </Popconfirm>
      </template>
    </Grid>
    <SamplePackingModal @success="handleSuccess()" />
  </div>
</template>
