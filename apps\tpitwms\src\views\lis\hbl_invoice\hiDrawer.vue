<script lang="ts" setup>
import type { VxeGridProps } from '#/adapter';

import { reactive, ref } from 'vue';

import { Fallback, useVbenDrawer } from '@vben/common-ui';

import { useVbenVxeGrid } from '#/adapter';
import { getTaskData } from '#/api/lis';

const data = ref();
const isNotFound = ref(false);
const [Drawer, drawerApi] = useVbenDrawer({
  class: 'w-[560px]',
  showConfirmButton: false,
  cancelText: '关闭',
  zIndex: 900,
  onCancel() {
    drawerApi.close();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      isNotFound.value = false;
      data.value = drawerApi.getData<Record<string, any>>();
      handleGetData(data.value.id);
    }
  },
});

const gridOptions = reactive<VxeGridProps>({
  id: 'LISHBLInvoiceTaskData',
  columns: [
    { type: 'seq', width: 52 },
    {
      field: 'H_B_L',
      title: 'H_B_L',
      minWidth: 136,
      treeNode: true,
    },
    {
      field: 'Delivery',
      title: 'Inbound',
      width: 88,
    },
    {
      field: 'Item',
      title: 'Item',
      width: 64,
    },
    {
      field: 'Invoice',
      title: 'Invoice',
      width: 80,
    },
    {
      field: 'Material',
      title: 'Material',
      width: 80,
    },
  ],
  customConfig: {
    storage: false,
  },
  menuConfig: {
    body: {
      options: [
        [
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuCopy',
            disabled: false,
            name: '复制单元格',
            prefixConfig: {
              icon: 'vxe-icon-copy',
            },
            visible: true,
          },
        ],
      ],
    },
    className: 'font-medium shadow rounded-md',
  },
  rowConfig: {
    useKey: true,
  },
  treeConfig: {
    rowField: 'H_B_L',
    childrenField: 'results',
    showLine: true,
  },
  size: 'mini',
  stripe: false,
  showOverflow: true,
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    enabled: false,
  },
  toolbarConfig: {
    enabled: false,
  },
});

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
});

function handleGetData(id: string) {
  drawerApi.setState({ loading: true });
  getTaskData(id)
    .then((res) => {
      gridApi.grid.loadData(res);
      isNotFound.value = false;
    })
    .catch(() => {
      isNotFound.value = true;
    })
    .finally(() => {
      drawerApi.setState({ loading: false });
    });
}
</script>
<template>
  <Drawer title="HBL发票关系 ">
    <Fallback v-if="isNotFound" :show-back="false" status="404" />
    <div v-else>
      <Grid />
    </div>
  </Drawer>
</template>
