<script setup lang="ts">
import type { VxeGridProps } from '#/adapter';

import { onMounted, reactive } from 'vue';

import { Page } from '@vben/common-ui';
import { AntClear } from '@vben/icons';

import { Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import { getRecord4BWOutProcessing } from '#/api/bondedwarehouse/outbound';

const searchField: any = reactive({
  BLNo: undefined,
});
const footQty: any = reactive({
  GoodsNum: 0,
  NumberOfProcessings: 0,
});
const gridOptions = reactive<VxeGridProps>({
  id: 'Record4BWOutboundProcessing',
  columns: [
    {
      fixed: 'left',
      title: '#',
      type: 'seq',
      width: 60,
    },
    {
      field: 'BLNo',
      title: '出库提单BL',
      width: 150,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'MatCode',
      title: '物料编号',
      width: 88,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'BatchNo',
      title: '批号',
      width: 80,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'ProcessingStatus',
      title: '加工状态',
      width: 88,
      formatter({ cellValue }) {
        return cellValue ? '已加工' : '待加工';
      },
      filters: [
        { label: '已加工', value: true },
        { label: '待加工', value: false },
      ],
      filterMultiple: false,
      slots: { default: 'processingStatus' },
    },
    {
      field: 'GoodsNum',
      title: '产品数量',
      width: 100,
      filters: [{ data: { type: '0', num1: undefined, num2: undefined } }],
      filterRender: { name: 'FilterNumberRange' },
    },
    {
      field: 'NumberOfProcessings',
      title: '贴标操作数',
      width: 100,
    },
    {
      field: 'ProcessingMethod',
      title: '加工方式',
      width: 160,
      filters: [{ data: null }],
      filterRender: { name: 'FilterInput' },
    },
    {
      field: 'ProcessingAreaName',
      title: '加工位置',
      width: 120,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'Remark',
      title: '加工备注',
      width: 100,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      visible: false,
    },
    {
      field: 'ProcessingDate',
      title: '贴标日期',
      width: 100,
      formatter: 'formatDate',
      filterRender: { name: 'FilterDateRange' },
      filters: [{ data: { date: [], type: '0' } }],
    },
    {
      field: 'ProcessingBy',
      title: '操作人',
      width: 88,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'PCB',
      title: 'PCB',
      width: 72,
      filters: [{ data: { type: '0', num1: undefined, num2: undefined } }],
      filterRender: { name: 'FilterNumberRange' },
    },
    {
      field: 'SPCB',
      title: 'SPCB',
      width: 72,
      filters: [{ data: { type: '0', num1: undefined, num2: undefined } }],
      filterRender: { name: 'FilterNumberRange' },
    },
    {
      field: 'ExtLabNum',
      title: '外标数',
      width: 80,
      filters: [{ data: { type: '0', num1: undefined, num2: undefined } }],
      filterRender: { name: 'FilterNumberRange' },
    },
    {
      field: 'InLabNum',
      title: '内标数',
      width: 80,
      filters: [{ data: { type: '0', num1: undefined, num2: undefined } }],
      filterRender: { name: 'FilterNumberRange' },
    },
    {
      field: 'SealingStickerNum',
      title: '封口贴',
      width: 80,
      filters: [{ data: { type: '0', num1: undefined, num2: undefined } }],
      filterRender: { name: 'FilterNumberRange' },
    },
    {
      field: 'QRCodeNum',
      title: '二维码贴',
      width: 88,
      filters: [{ data: { type: '0', num1: undefined, num2: undefined } }],
      filterRender: { name: 'FilterNumberRange' },
    },
    {
      field: 'PlasticPackagingNum',
      title: '塑封数',
      width: 80,
      filters: [{ data: { type: '0', num1: undefined, num2: undefined } }],
      filterRender: { name: 'FilterNumberRange' },
    },
    {
      field: 'OutBoxLabelNum',
      title: '外箱贴',
      width: 80,
      filters: [{ data: { type: '0', num1: undefined, num2: undefined } }],
      filterRender: { name: 'FilterNumberRange' },
    },
    {
      field: 'PackingMethod',
      title: '包装方式',
      width: 160,
    },
    {
      field: 'PackageType',
      title: '包装大类',
      width: 128,
    },
    {
      field: 'LabType',
      title: '标签类型',
      width: 88,
    },
    {
      field: 'LabColor',
      title: '标签颜色',
      width: 88,
    },
    {
      field: 'LabMaterial',
      title: '标签材质',
      width: 108,
    },
    {
      field: 'LabSize',
      title: '标签尺寸',
      width: 128,
    },
    {
      field: 'PlaSize',
      title: '塑封尺寸',
      width: 128,
    },
    {
      field: 'Special',
      title: '特殊操作',
      width: 128,
      filters: [{ data: null }],
      filterRender: { name: 'FilterInput' },
    },
  ],
  filterConfig: {
    remote: true,
  },
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: ({ filters, page, sorts }) => {
        const queryParams: any = Object.assign({}, page);
        // 处理排序条件
        if (sorts.length === 0) {
          queryParams.sort = 'ProcessingStatus,_Identify desc';
        } else {
          const sortItem = sorts.map((item) => {
            const newItem = `${item.field} ${item.order}`;
            return newItem;
          });
          queryParams.sort = sortItem.join(',');
        }
        // 处理筛选
        Object.getOwnPropertyNames(searchField).forEach((key) => {
          searchField[key] = undefined;
        });
        filters.forEach(({ field, values }) => {
          queryParams[field] = values.join(',');
          if (Object.prototype.hasOwnProperty.call(searchField, field)) {
            const jsonObject = JSON.parse(values.toString());
            searchField[field] = jsonObject.text;
          }
        });
        return getRecord4BWOutProcessing(queryParams)
          .then((res) => {
            Object.assign(footQty, res.qty);
            return res;
          })
          .catch(() => {})
          .finally(() => {});
      },
    },
    seq: true,
  },
  scrollX: { enabled: true, gt: 0 },
  scrollY: { enabled: true, gt: 0 },
  showFooter: true,
  size: 'mini',
  sortConfig: {
    remote: true,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
  footerRowStyle: { 'font-weight': 'bold ', backgroundColor: '#ebfbee' },
  footerMethod({ columns }) {
    return [
      columns.map((column, columnIndex) => {
        if (columnIndex === 0) {
          return `合计`;
        }
        if (['GoodsNum', 'NumberOfProcessings'].includes(column.field)) {
          return footQty[column.field];
        }

        return null;
      }),
    ];
  },
});
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });
function searchEvent(field: string) {
  const column = gridApi.grid.getColumnByField(field);
  if (column) {
    // 修改第一个选项为勾选状态
    const option = column.filters[0];
    if (!option) {
      return;
    }

    if (searchField[field]) {
      option.data = { type: '1', text: searchField[field] };
      option.value = JSON.stringify(option.data);
      option.checked = true;
    } else {
      option.data = { type: '0', text: undefined };
      option.checked = false;
    }

    // 修改条件之后，需要手动调用 updateData 处理表格数据
    gridApi.grid.updateData();
    gridApi.grid.commitProxy('query');
  }
}
function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
  gridApi.grid.commitProxy('query');
}
async function fetch() {}
onMounted(() => {
  setTimeout(() => {
    fetch();
  }, 300);
});
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar_left>
        <a-button class="mr-1" @click="clearFilterAndSort()">
          <AntClear class="size-5" />
        </a-button>
        <div class="hidden pl-1 md:block">
          <a-input-search
            v-model:value="searchField.BLNo"
            allow-clear
            class="mr-2 w-60"
            placeholder="出库提单BL"
            @search="searchEvent('BLNo')"
          />
        </div>
      </template>
      <template #processingStatus="{ row }">
        <Tag :color="row.ProcessingStatus ? 'green' : 'red'">
          {{ row.ProcessingStatus ? '已加工' : '待加工' }}
        </Tag>
      </template>
      <template #toolbar-tools> </template>
    </Grid>
    <SkuDrawer />
    <BatchDrawer />
  </Page>
</template>
