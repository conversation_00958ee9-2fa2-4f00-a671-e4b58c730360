<script lang="ts" setup>
import { onActivated, onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';

import { Page, useVbenModal, VbenButton, VbenLoading } from '@vben/common-ui';
import { useTabs } from '@vben/hooks';
import { AntCaretDown, AntCaretUp } from '@vben/icons';
import { useTabbarStore } from '@vben/stores';
import { downloadFileFromBlob } from '@vben/utils';

import { Card, message, Modal, TabPane, Tabs } from 'ant-design-vue';

import { useVbenForm } from '#/adapter';
import {
  bwOutDispatchHeadUpdate,
  getOutDispatchHead,
} from '#/api/bondedwarehouse/outbound/dispatch';
import { createFile } from '#/api/common';

import docCloseModal from './docCloseModal.vue';
/* import TabFlow from './tabFlow.vue'; */
/* import TabCSAR from './tabCSAR.vue';
import TabInbound from './tabInbound.vue';
import TabReceiving from './tabReceiving.vue'; */
import TabItem from './tabItem.vue';

const headData = ref({}) as any;
const selectOptions = ref([]) as any;
const loadingRef = ref(false);
const submitLoading = ref(false);
const init = ref(false);
const showHead = ref(true);
const activeKey = ref('Item');
const route = useRoute();
const { setTabTitle } = useTabs();
const id = route.params?.id?.toString() ?? '';

const [Form, baseFormApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'pb-1',
    labelClass: 'w-24',
  },
  // 提交函数
  handleSubmit: onSubmit,
  layout: 'horizontal',
  schema: [
    {
      component: 'Switch',
      componentProps: {},
      fieldName: 'DeliveryStatus',
      label: '发货状态',
      dependencies: {
        show: false,
        triggerFields: ['A'],
      },
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'DispatchNo',
      label: '派车单号',
      disabled: true,
    },
    {
      component: 'DatePicker',
      componentProps: {
        style: { width: '100%' },
        showTime: { format: 'HH:00:00' },
        format: 'YYYY-MM-DD HH:00:00',
        valueFormat: 'YYYY-MM-DD HH:00:00',
        placeholder: '请在发货时选择',
      },
      fieldName: 'DeliveryDate',
      label: '发货时间',
      disabled: true,
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        getPopupContainer: () => document.body,
      },
      fieldName: 'AddressTo',
      label: '目的地仓库',
      /* dependencies: {
        disabled(values) {
          return values.DeliveryStatus;
        },
        triggerFields: ['DeliveryStatus'],
      }, */
    },
    {
      component: 'Input',
      componentProps: {
        maxlength: 32,
        autocomplete: 'off',
      },
      fieldName: 'LicensePlate',
      label: '车牌号',
      /* dependencies: {
        disabled(values) {
          return values.DeliveryStatus;
        },
        triggerFields: ['DeliveryStatus'],
      }, */
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        getPopupContainer: () => document.body,
      },
      fieldName: 'TruckModel',
      label: '车型',
      /* dependencies: {
        disabled(values) {
          return values.DeliveryStatus;
        },
        triggerFields: ['DeliveryStatus'],
      }, */
    },
    {
      component: 'Input',
      componentProps: {
        maxlength: 128,
        autocomplete: 'off',
      },
      fieldName: 'Remark',
      label: '备注',
      formItemClass: 'col-span-1 md:col-span-3 xl:col-span-5',
    },
  ],
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2 md:grid-cols-3 xl:grid-cols-5',
});
async function onSubmit(values: Record<string, any>) {
  const check = await baseFormApi.validate();
  if (check.valid) {
    submitLoading.value = true;
    bwOutDispatchHeadUpdate(Object.assign({ id }, values))
      .then(() => {
        message.success('操作成功');
      })
      .catch(() => {
        baseFormApi.setValues(headData.value);
      })
      .finally(() => {
        setTimeout(() => {
          fetch();
          submitLoading.value = false;
        }, 500);
      });
  } else {
    message.info('必填项未填写完整，请检查！');
  }
}
const [DocCloseModal, DocCloseModalApi] = useVbenModal({
  connectedComponent: docCloseModal,
});
function openDocCloseModal() {
  DocCloseModalApi.setData({ id, selectOptions: selectOptions.value });
  DocCloseModalApi.open();
}

function handleSave() {
  if (headData.value.DeliveryStatus) {
    Modal.confirm({
      title: '确认保存',
      content: '当前派车单已关闭，请确认是否保存修改，重新生成LIS回传任务？',
      okButtonProps: { danger: true },
      onOk() {
        baseFormApi.submitForm();
      },
    });
  } else {
    baseFormApi.submitForm();
  }
}

async function handleGetFile() {
  submitLoading.value = true;
  createFile('OutDispatchDoc', id)
    .then((res) => {
      downloadFileFromBlob({
        source: res.data,
        fileName: `送货凭证_${headData.value.DispatchNo}.xlsx`,
      });
    })
    .catch(() => {
      /* message.error(`生成文件错误！`); */
    })
    .finally(() => {
      submitLoading.value = false;
    });
}
async function fetch() {
  const res = await getOutDispatchHead(id);
  headData.value = res.head;
  selectOptions.value = res.options;
  const fieldList = ['AddressTo', 'TruckModel'];
  fieldList.forEach((field) => {
    baseFormApi.updateSchema([
      {
        componentProps: {
          options: selectOptions.value.filter((item: any) => {
            return item.type === field;
          }),
        },
        fieldName: field,
      },
    ]);
  });

  const tabbarStore = useTabbarStore();
  const currentTab = tabbarStore.getTabs.find(
    (item) => item.path === route.path,
  );
  if (currentTab?.meta.title === '发车操作') {
    setTabTitle(`发车操作-${res.doc}`);
  }
  baseFormApi.setValues(headData.value);
  init.value = false;
}
onMounted(() => {
  init.value = true;
  showHead.value = true;
  fetch();
});
onActivated(() => {
  const tabbarStore = useTabbarStore();
  const currentTab = tabbarStore.getTabs.find(
    (item) => item.path === route.path,
  );
  if (!currentTab?.meta.newTabTitle && !init.value) {
    fetch();
  }
});
</script>

<template>
  <Page auto-content-height>
    <VbenLoading :spinning="loadingRef" />
    <div class="flex h-full flex-col">
      <Card :body-style="{ padding: '4px' }" :bordered="false" size="small">
        <template #title>
          <!-- <div class="hidden md:block"> -->
          {{ `派车单表头` }}
          <!-- </div> -->
        </template>
        <!--  v-if="headData.CreateBy !== 'System'" -->
        <template #extra>
          <div class="flex">
            <a-button class="mr-2" @click="handleGetFile()">
              送货凭证
            </a-button>
            <a-button
              :loading="submitLoading"
              :danger="headData.DeliveryStatus"
              class="mr-2"
              type="primary"
              @click="handleSave"
            >
              保存
            </a-button>
            <VbenButton
              :disabled="headData.DeliveryStatus"
              class="mr-2"
              size="sm"
              variant="info"
              @click="openDocCloseModal()"
            >
              发车关单
            </VbenButton>
            <!-- <a-button
              :loading="submitLoading"
              class="mr-2"
              danger
              type="dashed"
              @click="openDocCloseModal()"
            >
              表头重发
            </a-button> -->
            <AntCaretDown
              v-if="showHead"
              class="size-6"
              @click="showHead = !showHead"
            />
            <AntCaretUp v-else class="size-6" @click="showHead = !showHead" />
          </div>
        </template>
        <Form v-show="showHead" />
      </Card>
      <div class="inorder-tab min-h-0 flex-1">
        <Tabs v-model:active-key="activeKey" class="h-full" tab-position="left">
          <TabPane key="Item" tab="装载">
            <div v-if="activeKey === 'Item'" class="h-full">
              <TabItem
                :id="id"
                :doc="headData.Dispatch"
                :is-closed="headData.DeliveryStatus"
              />
            </div>
          </TabPane>
        </Tabs>
      </div>
    </div>
    <DocCloseModal @success="fetch()" />
  </Page>
</template>

<style scoped lang="less">
::v-deep(.inorder-tab) {
  background-color: #fff;

  .ant-tabs-tabpane .ant-tabs-tabpane-active {
    padding-left: 0 !important;
  }

  .ant-tabs-left > .ant-tabs-nav .ant-tabs-tab {
    padding: 8px 12px;
  }

  .ant-tabs-tab-active {
    background-color: #e7f5ff;
  }

  .ant-tabs-content-holder > .ant-tabs-content > .ant-tabs-tabpane {
    padding-left: 0;
  }
  .ant-tabs-content-left {
    height: 100%;
  }
}
</style>
