<script lang="ts" setup>
import type { PropType } from 'vue';

import type {
  VxeGlobalRendererHandles,
  VxeTableDefines,
} from '@vben/plugins/vxe-table';

import { ref, watch } from 'vue';

import { RadioButton, RadioGroup } from 'ant-design-vue';

const props = defineProps({
  params: {
    default: () => ({}),
    type: Object as PropType<VxeGlobalRendererHandles.RenderTableFilterParams>,
  },
});
const currOption = ref<VxeTableDefines.FilterOption>();

const load = () => {
  const { params } = props;
  if (params) {
    const { column } = params;
    const option = column.filters[0];
    currOption.value = option;
  }
};

const changeOptionEvent = () => {
  const { params } = props;
  const option = currOption.value;
  if (params && option) {
    option.value = JSON.stringify(option.data);
    const { $panel } = params;
    const checked = !!(
      (option.data.text !== undefined &&
        option.data.text !== null &&
        option.data.text !== '') ||
      option.data.type === '3'
    );
    $panel.changeOption(null, checked, option);
  }
};

/* const keyupEvent = (e: any) => {
  const { params } = props;
  if (params) {
    const { $panel } = params;
    $panel.confirmFilter(e);
  }
}; */

watch(() => {
  const { column } = props.params || {};
  return column;
}, load);

load();
</script>

<template>
  <div v-if="currOption" class="my-filter-text">
    <div style="padding-bottom: 8px">
      <RadioGroup
        v-model:value="currOption.data.type"
        button-style="solid"
        size="small"
        @change="changeOptionEvent"
      >
        <RadioButton value="0">精确</RadioButton>
        <RadioButton value="1">模糊</RadioButton>
        <RadioButton value="2">排除</RadioButton>
        <RadioButton value="3">空值</RadioButton>
        <RadioButton value="4">多值</RadioButton>
      </RadioGroup>
    </div>
    <a-input
      v-model:value="currOption.data.text"
      :disabled="currOption.data.type === '3'"
      allow-clear
      mode="text"
      placeholder="请输入查询内容"
      @input="changeOptionEvent"
    />
  </div>
</template>

<style scoped>
.my-filter-text {
  padding: 10px;
}

.my-fc-footer {
  padding-top: 8px;
  text-align: left;
}

.my-fc-footer button {
  padding: 0 8px 0 0;
  margin-left: 2px;
}
</style>
