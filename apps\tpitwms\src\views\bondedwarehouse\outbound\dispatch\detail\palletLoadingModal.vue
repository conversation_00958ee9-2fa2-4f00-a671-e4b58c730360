<script lang="ts" setup>
import type { VxeGridListeners, VxeGridProps } from '#/adapter';

import { reactive, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { AntClear } from '@vben/icons';

import { message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import {
  bwOutDispatchItemLoading,
  getOutDispatchUnLoadingItem,
  getOutDispatchUnLoadingList,
} from '#/api/bondedwarehouse/outbound';

const emit = defineEmits(['success']);

const data = ref();
const searchField: any = reactive({
  PickingDocNo: undefined,
  NewSSCC: undefined,
});

const [Modal, modalApi] = useVbenModal({
  class: 'w-full h-full',
  destroyOnClose: true,
  fullscreenButton: true,
  closeOnClickModal: false,
  confirmText: '确认装载',
  onCancel() {
    handleClose();
  },
  onConfirm: async () => {
    await handleLoading();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      data.value = modalApi.getData<Record<string, any>>();
      fetch();
    }
  },
  onBeforeClose() {
    emit('success');
    return true;
  },
  title: '托盘装载',
});
const gridOptions = reactive<VxeGridProps>({
  id: 'BWOutDispatchLoading',
  checkboxConfig: { highlight: true, range: true },
  columns: [
    { type: 'checkbox', width: 40 },
    {
      fixed: 'left',
      title: '#',
      type: 'seq',
      width: 60,
    },
    { type: 'expand', width: 48, slots: { content: 'expand_content' } },
    { field: 'PickingDocNo', title: '出库拣货单号', minWidth: 150 },
    { field: 'NewPalletNo', title: '出库托盘号', minWidth: 160 },
    { field: 'NewSSCC', title: '出库基准SSCC', minWidth: 100 },
    { field: 'MissingQty', title: '贴标少', minWidth: 100 },
    { field: 'GoodsNum', title: '拣货总数', minWidth: 100 },
    { field: 'CIQQty', title: 'CIQ抽样', minWidth: 100 },
    { field: 'ShelveQty', title: '搁置数量', minWidth: 100 },
    {
      field: 'PickingDate',
      title: '拣货日期',
      minWidth: 88,
      formatter: 'formatDateTime',
      sortable: true,
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
  ],
  expandConfig: {
    accordion: true,
  },
  filterConfig: {
    remote: true,
  },
  height: 'auto',
  pagerConfig: {
    pageSize: 100,
  },
  proxyConfig: {
    ajax: {
      query: ({ filters, page, sorts }) => {
        const queryParams: any = Object.assign({}, page);
        // 处理排序条件
        if (sorts.length === 0) {
          queryParams.sort = 'NewSSCC';
        } else {
          const sortItem = sorts.map((item) => {
            const newItem = `${item.field} ${item.order}`;
            return newItem;
          });
          queryParams.sort = sortItem.join(',');
        }
        // 处理筛选
        Object.getOwnPropertyNames(searchField).forEach((key) => {
          searchField[key] = undefined;
        });
        filters.forEach(({ field, values }) => {
          queryParams[field] = values.join(',');
          if (Object.prototype.hasOwnProperty.call(searchField, field)) {
            searchField[field] = values.toString();
          }
        });
        return getOutDispatchUnLoadingList(queryParams);
      },
    },
    seq: true,
  },
  /* scrollY: { enabled: true, gt: 0 }, */
  size: 'mini',
  sortConfig: {
    remote: true,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
});
const itemGridOptions = reactive<VxeGridProps>({
  columns: [
    { type: 'seq', width: 48 },
    { field: 'YUB', title: 'YUB', minWidth: 100 },
    { field: 'Outbound', title: 'Outbound', minWidth: 100 },
    { field: 'MatCode', title: '物料编码', minWidth: 88 },
    { field: 'BatchNo', title: '批次', minWidth: 88 },
    { field: 'MissingQty', title: '贴标少', minWidth: 100 },
    { field: 'PickingQty', title: '拣货数量', minWidth: 88 },
    { field: 'CIQQty', title: 'CIQ抽样', minWidth: 100 },
    { field: 'ShelveQty', title: '搁置数量', minWidth: 100 },
    { field: 'oBarCode', title: '条形码', minWidth: 100 },
    { field: 'oMatType', title: '物料类型', minWidth: 100 },
    { field: 'oCName', title: '中文品名', minWidth: 240 },
  ],
  customConfig: {
    storage: false,
  },
  menuConfig: {
    body: {
      options: [
        [
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuCopy',
            disabled: false,
            name: '复制单元格',
            prefixConfig: {
              icon: 'vxe-icon-copy',
            },
            visible: true,
          },
        ],
      ],
    },
    className: 'font-medium shadow rounded-md',
  },
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    enabled: false,
  },
  rowConfig: {
    useKey: true,
    keyField: 'Guid',
  },
  rowStyle({ row }) {
    if (row.Qty === 0) {
      return {
        backgroundColor: '#fff4e6',
        color: '#222',
      };
    }
  },
  size: 'mini',
  toolbarConfig: {
    enabled: false,
  },
});
const [ItemGrid, itemGridApi] = useVbenVxeGrid({
  gridOptions: itemGridOptions,
});
const gridEvents: VxeGridListeners = {
  async toggleRowExpand({ expanded, row }) {
    await handleItemData([]);
    if (expanded) {
      handleItemLoading(true);
      const itemData = await getOutDispatchUnLoadingItem(row.NewPalletID);
      await handleHeadRow(row);
      await handleItemData(itemData);
      handleItemLoading(false);
      itemGridApi.grid?.setAllTreeExpand(true);
    }
  },
};
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions, gridEvents });
function handleHeadRow(row: any) {
  gridApi.grid.setCurrentRow(row);
}
function handleItemData(data: any) {
  itemGridApi.setGridOptions({
    data,
  });
}
function handleItemLoading(isLoading: boolean) {
  itemGridApi.setLoading(isLoading);
}
function handleClose() {
  modalApi.close();
  emit('success');
}
async function handleLoading() {
  const checkedRecords = gridApi.grid
    .getCheckboxRecords()
    .map((item: any) => item.NewPalletID);
  if (checkedRecords.length === 0) {
    message.info(`请勾选需要装载的托盘`);
    return;
  }
  modalApi.setState({ confirmLoading: true });
  modalApi.setState({ loading: true });
  bwOutDispatchItemLoading(data.value.id, checkedRecords)
    .then(() => {
      handleClose();
    })
    .catch(() => {})
    .finally(() => {
      modalApi.setState({ loading: false });
      modalApi.setState({ confirmLoading: false });
    });
}
function searchEvent(field: string, type: string) {
  const column = gridApi.grid.getColumnByField(field);
  if (column) {
    // 修改第一个选项为勾选状态
    const option = column.filters[0];
    if (!option) {
      return;
    }
    if (searchField[field]) {
      option.data = searchField[field];
      option.value = option.data;
      option.checked = true;
    } else {
      if (type) {
        option.data = undefined;
        option.checked = false;
      }
    }
    // 修改条件之后，需要手动调用 updateData 处理表格数据
    gridApi.grid.updateData();
    gridApi.grid.commitProxy('query');
  }
}
/* function handleSuccess() {
  gridApi.grid.commitProxy('query');
} */
function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
  gridApi.grid.commitProxy('query');
}
async function fetch() {}
</script>
<template>
  <Modal>
    <Grid>
      <template #toolbar_left>
        <a-button class="mr-1" @click="clearFilterAndSort()">
          <AntClear class="size-5" />
        </a-button>
        <div class="hidden pl-1 md:block">
          <a-input-search
            v-model:value="searchField.PickingDocNo"
            allow-clear
            class="mr-2 w-60"
            placeholder="出库拣货单号"
            @search="searchEvent('PickingDocNo', 'Input')"
          />
          <a-input-search
            v-model:value="searchField.NewSSCC"
            allow-clear
            class="mr-2 w-60"
            placeholder="出库SSCC"
            @search="searchEvent('NewSSCC', 'Input')"
          />
        </div>
      </template>
      <template #toolbar-tools> </template>
      <template #expand_content>
        <div>
          <ItemGrid />
        </div>
      </template>
    </Grid>
  </Modal>
</template>
