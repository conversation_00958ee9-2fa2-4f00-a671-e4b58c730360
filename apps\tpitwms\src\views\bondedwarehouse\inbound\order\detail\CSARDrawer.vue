<script lang="ts" setup>
import { ref } from 'vue';

import { Fallback, useVbenDrawer } from '@vben/common-ui';

import { message, Modal } from 'ant-design-vue';

import { useVbenForm } from '#/adapter';
import {
  bwInOrderCSARUpdate,
  getInOrderCSARData,
} from '#/api/bondedwarehouse/inbound';

const emit = defineEmits(['success']);

const data = ref();

const isNotFound = ref(false);
const [Form, baseFormApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',

      /* size: 'small', */
    },
    formItemClass: 'pb-5',
  },
  // 提交函数
  handleSubmit: onSubmit,
  // 垂直布局，label和input在不同行，值为vertical
  // 水平布局，label和input在同一行
  layout: 'horizontal',
  schema: [
    {
      component: 'Input',
      componentProps: {},
      disabled: true,
      fieldName: 'MatCode',
      label: '物料编号',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {},
      disabled: true,
      fieldName: 'BatchNo',
      label: '实收批次',
      rules: 'required',
    },
    {
      component: 'RadioGroup',
      componentProps: {
        buttonStyle: 'solid',
        optionType: 'button',
        options: [
          { label: 'Y', value: 'Y' },
          { label: 'N', value: 'N' },
          { label: 'NA', value: 'NA' },
        ],
      },
      fieldName: 'CName',
      label: '中文品名',
      rules: 'required',
    },
    {
      component: 'RadioGroup',
      componentProps: {
        buttonStyle: 'solid',
        optionType: 'button',
        options: [
          { label: 'Y', value: 'Y' },
          { label: 'N', value: 'N' },
          { label: 'NA', value: 'NA' },
        ],
      },
      fieldName: 'GuidingWords',
      label: '引导语',
    },
    {
      component: 'RadioGroup',
      componentProps: {
        optionType: 'button',
        buttonStyle: 'solid',
        options: [
          { label: 'Y', value: 'Y' },
          { label: 'N', value: 'N' },
          { label: 'NA', value: 'NA' },
        ],
      },
      defaultValue: 'NA',
      disabled: true,
      fieldName: 'ExpireDate',
      label: '限用日期',
    },
    {
      component: 'RadioGroup',
      componentProps: {
        buttonStyle: 'solid',
        optionType: 'button',
        options: [
          { label: 'Y', value: 'Y' },
          { label: 'N', value: 'N' },
          { label: 'NA', value: 'NA' },
        ],
      },
      fieldName: 'HP',
      label: 'HP No.',
    },
    {
      component: 'RadioGroup',
      componentProps: {
        buttonStyle: 'solid',
        optionType: 'button',
        options: [
          { label: 'Y', value: 'Y' },
          { label: 'N', value: 'N' },
          { label: 'NA', value: 'NA' },
        ],
      },
      fieldName: 'CName2',
      label: '备案信息.',
    },
    {
      component: 'RadioGroup',
      componentProps: {
        optionType: 'button',
        buttonStyle: 'solid',
        options: [
          { label: 'Y', value: 'Y' },
          { label: 'N', value: 'N' },
          { label: 'NA', value: 'NA' },
        ],
      },
      defaultValue: 'NA',
      disabled: true,
      fieldName: 'SPF',
      label: '防晒值.',
    },
    {
      component: 'RadioGroup',
      componentProps: {
        buttonStyle: 'solid',
        optionType: 'button',
        options: [
          { label: 'Y', value: 'Y' },
          { label: 'N', value: 'N' },
          { label: 'NA', value: 'NA' },
        ],
      },
      fieldName: 'MadeIn',
      label: '原产国.',
    },
    {
      component: 'RadioGroup',
      componentProps: {
        buttonStyle: 'solid',
        optionType: 'button',
        options: [
          { label: 'Y', value: 'Y' },
          { label: 'N', value: 'N' },
          { label: 'NA', value: 'NA' },
        ],
      },
      fieldName: 'Formula',
      label: '配方号.',
    },
    {
      component: 'RadioGroup',
      componentProps: {
        buttonStyle: 'solid',
        optionType: 'button',
        options: [
          { label: 'Y', value: 'Y' },
          { label: 'N', value: 'N' },
          { label: 'NA', value: 'NA' },
        ],
      },
      defaultValue: 'NA',
      disabled: true,
      fieldName: 'BarCode',
      label: '条形码.',
    },
    {
      component: 'Textarea',
      componentProps: {
        autocomplete: 'off',
        autosize: { minRows: 1, maxRows: 2 },
        maxlength: 100,
        placeholder: '请输入',
      },
      fieldName: 'Others',
      label: '其他',
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'Mu',
      label: '批量操作',
    },
  ],
  showDefaultActions: false,
  wrapperClass: 'grid-cols-1',
});
const [Drawer, drawerApi] = useVbenDrawer({
  class: 'w-[360px]',
  zIndex: 900,
  onCancel() {
    drawerApi.close();
  },
  onConfirm() {
    baseFormApi.submitForm();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      isNotFound.value = false;
      baseFormApi.resetForm();
      data.value = drawerApi.getData<Record<string, any>>();
      handleGetData(data.value.id);
    }
  },
  title: 'CSAR QC 拍照检查',
});
function handleGetData(id: string) {
  drawerApi.setState({ loading: true });
  getInOrderCSARData(id)
    .then((res) => {
      baseFormApi.setValues(res);
    })
    .catch(() => {
      isNotFound.value = true;
    })
    .finally(() => {
      drawerApi.setState({ loading: false });
    });
}
function csarUpdateAfterClosed(values: Record<string, any>) {
  drawerApi.setState({ confirmLoading: true });
  drawerApi.setState({ loading: true });
  bwInOrderCSARUpdate(
    Object.assign({ id: data.value.id, enforce: true }, values),
  )
    .then(() => {
      message.success('操作成功');
      drawerApi.close();
      emit('success');
    })
    .catch(() => {})
    .finally(() => {
      drawerApi.setState({ loading: false });
      drawerApi.setState({ confirmLoading: false });
    });
}

async function onSubmit(values: Record<string, any>) {
  const check = await baseFormApi.validate();
  if (check.valid) {
    drawerApi.setState({ confirmLoading: true });
    drawerApi.setState({ loading: true });
    bwInOrderCSARUpdate(
      Object.assign({ id: data.value.id, enforce: false }, values),
    )
      .then((res) => {
        if (res === 'Confirm') {
          Modal.confirm({
            content: `将自动上传更新至LIS系统？`,
            onCancel() {},
            onOk() {
              csarUpdateAfterClosed(values);
            },
            title: `入库订单已关闭，确认更新检查记录？`,
            okButtonProps: { danger: true },
          });
        } else {
          message.success('操作成功');
          drawerApi.close();
          emit('success');
        }
      })
      .catch(() => {})
      .finally(() => {
        drawerApi.setState({ loading: false });
        drawerApi.setState({ confirmLoading: false });
      });
  }
}
async function allSelect(value: any) {
  baseFormApi.setValues({
    CName: value,
    GuidingWords: value,
    HP: value,
    CName2: value,
    MadeIn: value,
    Formula: value,
  });
}
</script>
<template>
  <Drawer>
    <Fallback v-if="isNotFound" :show-back="false" status="404" />
    <Form v-else>
      <template #Mu>
        <a-button
          size="small"
          style="margin-right: 4px"
          @click="allSelect(undefined)"
        >
          清空
        </a-button>
        <a-button
          size="small"
          style="margin-right: 4px"
          @click="allSelect('Y')"
        >
          全选Y
        </a-button>
        <a-button
          size="small"
          style="margin-right: 4px"
          @click="allSelect('NA')"
        >
          全选NA
        </a-button>
      </template>
    </Form>
  </Drawer>
</template>

<style scoped lang="less">
::v-deep(.ant-input[disabled]) {
  color: rgb(0 0 0 / 70%);
}
::v-deep(input[disabled]) {
  color: rgb(0 0 0 / 70%) !important;
}
</style>
