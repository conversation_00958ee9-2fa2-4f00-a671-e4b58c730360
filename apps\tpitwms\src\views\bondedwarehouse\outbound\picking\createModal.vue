<script lang="ts" setup>
import { reactive, ref } from 'vue';
import { useRouter } from 'vue-router';

import { useVbenModal } from '@vben/common-ui';

import { Result } from 'ant-design-vue';
import dayjs from 'dayjs';

import { useVbenForm, z } from '#/adapter';
import { bwOutPickingCreate } from '#/api/bondedwarehouse/outbound';
import { getOptions } from '#/api/common';

const emit = defineEmits(['success']);

const orderId = ref(undefined);
const cName = reactive({
  OwnerShortName: undefined,
  WarehouseName: undefined,
});
const router = useRouter();
const filterOption = (input: string, option: any) => {
  return option.label.toLowerCase().includes(input.toLowerCase());
};
const [Form, formApi] = useVbenForm({
  handleSubmit: onSubmit,
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  schema: [
    {
      component: 'Select',
      componentProps: {
        getPopupContainer: () => document.body,
        options: [
          { label: '正常拣货 By BL', value: 'Normal' },
          { label: '留样拣货', value: 'Sample' },
        ],
        onChange: (_value: any, option: any) => {
          if (option.value === 'Sample') {
            formApi.setFieldValue(
              'PickingDocNo',
              `留样${dayjs().format('YYMMDD')}`,
            );
          } else {
            formApi.setFieldValue('PickingDocNo', undefined, false);
          }
        },
      },
      defaultValue: 'Normal',
      fieldName: 'PickingType',
      label: '拣货类型',
      rules: 'required',
    },
    {
      component: 'Select',
      componentProps: {
        getPopupContainer: () => document.body,
        filterOption,
        onChange: (value: any, option: any) => {
          if (value) {
            formApi.setFieldValue('PickingDocNo', `${option.label}`);
          } else {
            formApi.setFieldValue('PickingDocNo', undefined, false);
          }
        },
        showSearch: true,
        allowClear: true,
      },
      fieldName: 'BLNo',
      label: '出库提单(BL)',
      dependencies: {
        show(values) {
          return values.PickingType === 'Normal';
        },
        rules(values) {
          return values.PickingType === 'Normal' ? 'required' : null;
        },
        triggerFields: ['PickingType'],
      },
    },
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 48,
      },
      fieldName: 'PickingDocNo',
      label: '拣货单号',
      rules: z
        .string({ message: '请输入拣货单号' })
        .min(8, { message: '至少需要8个字符' }),
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        getPopupContainer: () => document.body,
        onSelect: (_value: string, option: any) => {
          cName.OwnerShortName = option.label;
        },
      },
      fieldName: 'OwnerCode',
      label: '货主',
      dependencies: {
        show(values) {
          return values.PickingType === 'Sample';
        },
        rules(values) {
          return values.PickingType === 'Sample' ? 'required' : null;
        },
        triggerFields: ['PickingType'],
      },
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        getPopupContainer: () => document.body,
        onSelect: (_value: string, option: any) => {
          cName.WarehouseName = option.label;
        },
      },
      fieldName: 'WarehouseCode',
      label: '仓库',
      dependencies: {
        show(values) {
          return values.PickingType === 'Sample';
        },
        rules(values) {
          return values.PickingType === 'Sample' ? 'required' : null;
        },
        triggerFields: ['PickingType'],
      },
    },
  ],
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  fullscreenButton: false,
  closeOnClickModal: false,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await formApi.submitForm();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      modalApi.setState({ showCancelButton: true });
      modalApi.setState({ showConfirmButton: true });
      orderId.value = undefined;
      formApi.resetForm();
      fetch();
    }
  },
  title: '创建拣货单',
});

async function onSubmit(values: Record<string, any>) {
  const check = await formApi.validate();
  if (check.valid) {
    modalApi.setState({ confirmLoading: true });
    modalApi.setState({ loading: true });
    bwOutPickingCreate(Object.assign({}, values, cName))
      .then((res) => {
        modalApi.setState({ showCancelButton: false });
        modalApi.setState({ showConfirmButton: false });
        orderId.value = res;
      })
      .catch(() => {})
      .finally(() => {
        modalApi.setState({ loading: false });
        modalApi.setState({ confirmLoading: false });
      });
  }
}
function handleClose() {
  modalApi.close();
  emit('success');
}
function handleGoDetail() {
  modalApi.close();
  router.push({
    name: 'BWOutPickingDetail',
    params: { id: orderId.value },
  });
  emit('success');
}
async function fetch() {
  const options = await getOptions('BWOutPickingCreate');
  const fieldList = ['BLNo', 'OwnerCode', 'WarehouseCode'];
  fieldList.forEach((field) => {
    formApi.updateSchema([
      {
        componentProps: {
          options: options.filter((item: any) => {
            return item.type === field;
          }),
        },
        fieldName: field,
      },
    ]);
  });
}
</script>
<template>
  <Modal>
    <Result v-if="orderId" status="success" title="创建成功">
      <template #extra>
        <a-button type="primary" @click="handleGoDetail()">
          查看拣货单
        </a-button>
        <a-button @click="handleClose()">关闭</a-button>
      </template>
    </Result>
    <Form v-else />
  </Modal>
</template>
