<script lang="ts" setup>
import { onActivated, onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';

import { Page, useVbenDrawer, useVbenModal, VbenButton } from '@vben/common-ui';
import { useTabs } from '@vben/hooks';
import { AntCaretDown, AntCaretUp, AntSearch } from '@vben/icons';
import { useTabbarStore } from '@vben/stores';
import { downloadFileFromBlob } from '@vben/utils';

import { Card, message, TabPane, Tabs } from 'ant-design-vue';

import { useVbenForm } from '#/adapter';
import {
  bwOutPickingHeadUpdate,
  getOutPickingHead,
} from '#/api/bondedwarehouse/outbound';
import { createFile } from '#/api/common';
import customerDrawer from '#/views/sapneo/master/customer/customerDrawer.vue';

import outboundImportModal from './outboundImportModal.vue';
import outboundPGIModal from './outboundPGIModal.vue';
import TabPicking from './tabPicking.vue';
import TabPickingSample from './tabPickingSample.vue';

const headData = ref({}) as any;
const init = ref(false);
const showForm = ref(true);
const submitLoading = ref(false);
const activeKey = ref('Picking');
const route = useRoute();
const { setTabTitle } = useTabs();
const id = route.params?.id?.toString() ?? '';

const [CustomerDrawer, customerDrawerApi] = useVbenDrawer({
  connectedComponent: customerDrawer,
});
const [OutboundImportModal, OutboundImportModalApi] = useVbenModal({
  connectedComponent: outboundImportModal,
});
const [OutboundPGIModal, OutboundPGIModalApi] = useVbenModal({
  connectedComponent: outboundPGIModal,
});
function openCustomerDrawer(id: string) {
  customerDrawerApi.setData({ id });
  customerDrawerApi.open();
}
function openOutboundImportModal() {
  OutboundImportModalApi.setData(headData.value);
  OutboundImportModalApi.open();
}
function openOutboundPGIModal() {
  OutboundPGIModalApi.setData({ id });
  OutboundPGIModalApi.open();
}
const [Form, baseFormApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'pb-1',
    labelClass: 'w-24',
  },
  // 提交函数
  handleSubmit: onSubmit,
  layout: 'horizontal',
  schema: [
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'BLNo',
      label: '出库提单',
      disabled: true,
      dependencies: {
        show(values) {
          return values.PickingType === 'Normal';
        },
        triggerFields: ['PickingType'],
      },
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'OutDeliveryType',
      label: '订单类型',
      disabled: true,
      dependencies: {
        show(values) {
          return values.PickingType === 'Normal';
        },
        triggerFields: ['PickingType'],
      },
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'OwnerShortName',
      label: '货主简称',
      disabled: true,
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'WarehouseName',
      label: '仓库',
      disabled: true,
    },
    {
      component: 'Select',
      componentProps: {
        options: [
          { label: '正常拣货 By BL', value: 'Normal' },
          { label: '留样拣货', value: 'Sample' },
        ],
      },
      fieldName: 'PickingType',
      label: '拣货类型',
      disabled: true,
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'PickingDocNo',
      label: '拣货单号',
      disabled: true,
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请在出库订单中维护',
      },
      disabled: true,
      fieldName: 'YUB',
      label: 'SAP销售订单',
      dependencies: {
        show(values) {
          return values.PickingType === 'Normal';
        },
        triggerFields: ['PickingType'],
      },
    },
    /* {
      component: 'Input',
      componentProps: {
        placeholder: '请绑定Outbound',
      },
      disabled: true,
      fieldName: 'DeliveryDocumentType',
      label: 'OBD单据类型',
      dependencies: {
        show(values) {
          return values.PickingType === 'Normal';
        },
        triggerFields: ['PickingType'],
      },
    }, */
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'BoundOutbound',
      label: '绑定Outbound',
      dependencies: {
        show(values) {
          return values.PickingType === 'Normal';
        },
        triggerFields: ['PickingType'],
      },
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'ShipToParty',
      label: '运往地址',
      dependencies: {
        show(values) {
          return values.PickingType === 'Normal';
        },
        triggerFields: ['PickingType'],
      },
    },
    {
      component: 'Switch',
      componentProps: {
        class: 'w-auto',
        checkedChildren: '紧急',
        unCheckedChildren: '普通',
      },
      disabled: true,
      fieldName: 'Urgent',
      label: '紧急程度',
      dependencies: {
        show(values) {
          return values.PickingType === 'Normal';
        },
        triggerFields: ['PickingType'],
      },
    },
    {
      component: 'DatePicker',
      componentProps: {
        style: { width: '100%' },
        valueFormat: 'YYYY-MM-DD',
      },
      fieldName: 'PreOutDate',
      label: '预计出库日期',
    },
    {
      component: 'Input',
      componentProps: {
        maxlength: 128,
        autocomplete: 'off',
      },
      fieldName: 'Remark',
      label: '备注',
      formItemClass: 'col-span-1 md:col-span-2',
    },
  ],
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2 md:grid-cols-3 xl:grid-cols-5',
});

async function onSubmit(values: Record<string, any>) {
  const check = await baseFormApi.validate();
  if (check.valid) {
    submitLoading.value = true;
    bwOutPickingHeadUpdate(Object.assign({ id }, values))
      .then(() => {
        message.success('操作成功');
      })
      .catch(() => {
        baseFormApi.setValues(headData.value);
      })
      .finally(() => {
        setTimeout(() => {
          submitLoading.value = false;
        }, 500);
      });
  } else {
    message.info('必填项未填写完整，请检查！');
  }
}
/* function handleSuccess() {
  fetch();
} */
function handleBoundOBD() {
  if (!headData.value.YUB) {
    message.info('请先在出库订单中维护SAP销售订单!');
    return;
  }
  if (headData.value.BoundOutbound) {
    message.info('已绑定Outbound，如需变更请在拣货作业中移除！');
    return;
  }
  openOutboundImportModal();
}
function handleOpenCustomerDrawer() {
  if (!headData.value.BoundOutbound) {
    message.info('请先绑定Outbound！');
    return;
  }
  openCustomerDrawer(headData.value.ShipToParty);
}
async function handleGetFile() {
  submitLoading.value = true;
  createFile('OutPalletDoc', id)
    .then((res) => {
      downloadFileFromBlob({
        source: res.data,
        fileName: `随托信息单_${headData.value.PickingDocNo}.xlsx`,
      });
    })
    .catch(() => {
      /* message.error(`生成文件错误！`); */
    })
    .finally(() => {
      submitLoading.value = false;
    });
}
async function fetch() {
  const res = await getOutPickingHead(id);
  headData.value = res.head;

  const tabbarStore = useTabbarStore();
  const currentTab = tabbarStore.getTabs.find(
    (item) => item.path === route.path,
  );
  if (currentTab?.meta.title === '拣货操作') {
    setTabTitle(`拣货操作-${res.doc}`);
  }
  baseFormApi.resetForm();
  baseFormApi.setValues(headData.value);
  init.value = false;
}
onMounted(() => {
  init.value = true;
  showForm.value = true;
  fetch();
});
onActivated(() => {
  const tabbarStore = useTabbarStore();
  const currentTab = tabbarStore.getTabs.find(
    (item) => item.path === route.path,
  );
  if (!currentTab?.meta.newTabTitle && !init.value) {
    fetch();
  }
});
</script>

<template>
  <Page auto-content-height>
    <div class="flex h-full flex-col">
      <Card :body-style="{ padding: '4px' }" :bordered="false" size="small">
        <template #title>
          <div class="hidden md:block">
            {{ `拣货单表头` }}
          </div>
        </template>
        <template #extra>
          <div class="flex">
            <a-button class="mr-2" @click="handleGetFile()">
              随托信息单
            </a-button>
            <a-button
              :loading="submitLoading"
              class="mr-2"
              type="primary"
              @click="baseFormApi.submitForm()"
            >
              保存
            </a-button>
            <VbenButton
              :disabled="headData.IsClosed"
              class="mr-2"
              size="sm"
              variant="info"
              @click="openOutboundPGIModal()"
            >
              PGI关单
            </VbenButton>
            <!-- <VbenButton class="mr-2" size="sm" variant="info" @click="1">
              关单
            </VbenButton> -->
            <AntCaretDown
              v-if="showForm"
              class="size-6"
              @click="showForm = !showForm"
            />
            <AntCaretUp v-else class="size-6" @click="showForm = !showForm" />
          </div>
        </template>

        <Form v-show="showForm">
          <template #BoundOutbound="slotProps">
            <a-input-search
              v-bind="slotProps"
              readonly="readonly"
              @search="handleBoundOBD()"
            >
              <template #enterButton>
                <a-button class="!pl-1 !pr-1">
                  <AntSearch class="size-5" />
                </a-button>
              </template>
            </a-input-search>
          </template>
          <template #ShipToParty="slotProps">
            <a-input-search
              v-bind="slotProps"
              readonly="readonly"
              @search="handleOpenCustomerDrawer"
            >
              <template #enterButton>
                <a-button class="!pl-1 !pr-1">
                  <AntSearch class="size-5" />
                </a-button>
              </template>
            </a-input-search>
          </template>
        </Form>
      </Card>
      <div class="inorder-tab min-h-0 flex-1">
        <Tabs v-model:active-key="activeKey" class="h-full" tab-position="left">
          <TabPane key="Picking" tab="拣货">
            <div v-if="activeKey === 'Picking'" class="h-full">
              <TabPicking
                v-if="headData.PickingType === 'Normal'"
                :id="id"
                :is-closed="headData.IsClosed"
                :doc="headData.PickingDocNo"
                @success="fetch()"
              />
              <TabPickingSample
                v-if="headData.PickingType === 'Sample'"
                :id="id"
                :is-closed="headData.IsClosed"
                :doc="headData.PickingDocNo"
                @success="fetch()"
              />
            </div>
          </TabPane>
        </Tabs>
      </div>
      <OutboundImportModal @success="fetch()" />
      <OutboundPGIModal @success="fetch()" />
      <CustomerDrawer />
    </div>
  </Page>
</template>

<style scoped lang="less">
::v-deep(.inorder-tab) {
  background-color: #fff;

  .ant-tabs-tabpane .ant-tabs-tabpane-active {
    padding-left: 0 !important;
  }

  .ant-tabs-left > .ant-tabs-nav .ant-tabs-tab {
    padding: 8px 12px;
  }

  .ant-tabs-tab-active {
    background-color: #e7f5ff;
  }

  .ant-tabs-content-holder > .ant-tabs-content > .ant-tabs-tabpane {
    padding-left: 0;
  }
  .ant-tabs-content-left {
    height: 100%;
  }
}
</style>
