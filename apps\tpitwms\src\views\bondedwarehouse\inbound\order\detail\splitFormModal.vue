<script lang="ts" setup>
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { useVbenForm } from '#/adapter/form';
import { bwInOrderItemSplit } from '#/api/bondedwarehouse/inbound';

defineOptions({
  name: '拆分项',
});
const emit = defineEmits(['success']);
const data = ref();

const [Form, formApi] = useVbenForm({
  handleSubmit: onSubmit,
  schema: [
    {
      component: 'InputNumber',
      componentProps: {
        class: 'w-full',
        min: 0,
        placeholder: '请输入拆分数量',
      },
      fieldName: 'qty',
      label: '拆分数量',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        class: 'w-full',
        autocomplete: 'off',
        maxlength: 20,
        placeholder: '请输入',
      },
      fieldName: 'PalletNo',
      label: '拆分托盘号',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        class: 'w-full',
        autocomplete: 'off',
        maxlength: 20,
        placeholder: '请输入',
      },
      fieldName: 'SSCC',
      label: '拆分SSCC',
      rules: 'required',
    },
  ],
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  fullscreenButton: false,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await formApi.validateAndSubmitForm();
    // modalApi.close();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      data.value = modalApi.getData<Record<string, any>>();
      formApi.setValues(data.value);
    }
  },
  title: '预拆分',
});

function onSubmit(values: Record<string, any>) {
  modalApi.setState({ loading: true });
  modalApi.setState({ confirmLoading: true });

  bwInOrderItemSplit(Object.assign({ id: data.value.Guid }, values))
    .then(() => {
      modalApi.close();
      emit('success');
    })
    .catch(() => {})
    .finally(() => {
      modalApi.setState({ loading: false });
      modalApi.setState({ confirmLoading: false });
    });
}
</script>
<template>
  <Modal>
    <Form />
  </Modal>
</template>
