<script setup lang="ts">
import type { VxeGridProps } from '#/adapter';

import { onMounted, reactive } from 'vue';

import { Page } from '@vben/common-ui';
import { AntClear } from '@vben/icons';

import { Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import { getRecord4BWOutTally } from '#/api/bondedwarehouse/outbound';

const searchField: any = reactive({
  BLNo: undefined,
  MatCode: undefined,
  BatchNo: undefined,
});
const qty: any = reactive({
  GoodsNum: 0,
  TallyQty: 0,
  TallyBoxNum: 0,
  MissingQty: 0,
  RetentionQty: 0,
  CIQQty: 0,
  ShelveQty: 0,
});
const gridOptions = reactive<VxeGridProps>({
  id: 'Record4BWOutboundTally',
  columns: [
    {
      fixed: 'left',
      title: '#',
      type: 'seq',
      width: 60,
    },
    {
      field: 'BLNo',
      title: '出库提单BL',
      width: 150,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'MatCode',
      title: '物料编号',
      width: 88,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'BatchNo',
      title: '批号',
      width: 80,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'GoodsNum',
      title: '产品数量',
      width: 100,
      filters: [{ data: { type: '0', num1: undefined, num2: undefined } }],
      filterRender: { name: 'FilterNumberRange' },
    },
    {
      field: 'RetentionQty',
      title: '留样数量',
      width: 100,
      filters: [{ data: { type: '0', num1: undefined, num2: undefined } }],
      filterRender: { name: 'FilterNumberRange' },
    },
    {
      field: 'TallyBoxNum',
      title: '理货箱数',
      width: 100,
      filters: [{ data: { type: '0', num1: undefined, num2: undefined } }],
      filterRender: { name: 'FilterNumberRange' },
    },
    {
      field: 'TallyQty',
      title: '理货实物数',
      width: 100,
      filters: [{ data: { type: '0', num1: undefined, num2: undefined } }],
      filterRender: { name: 'FilterNumberRange' },
    },
    {
      field: 'MissingQty',
      title: '贴标少',
      width: 80,
      filters: [{ data: { type: '0', num1: undefined, num2: undefined } }],
      filterRender: { name: 'FilterNumberRange' },
    },
    {
      field: 'CIQQty',
      title: 'CIQ',
      width: 80,
      filters: [{ data: { type: '0', num1: undefined, num2: undefined } }],
      filterRender: { name: 'FilterNumberRange' },
    },
    {
      field: 'ShelveQty',
      title: '搁置',
      width: 80,
      filters: [{ data: { type: '0', num1: undefined, num2: undefined } }],
      filterRender: { name: 'FilterNumberRange' },
    },
    {
      field: 'ShelveReason',
      title: '搁置原因',
      width: 160,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'TallyBatch',
      title: '实物批号',
      width: 88,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'TallyExpiration',
      title: '实物效期',
      width: 88,
    },
    {
      field: 'IsBatchDiff',
      title: '批次差异',
      width: 88,
      filters: [
        { label: '无差异', value: false },
        { label: '有差异', value: true },
      ],
      filterMultiple: false,
      slots: { default: 'isBatchDiff' },
    },

    {
      field: 'TallyDate',
      title: '理货时间',
      width: 136,
      formatter: 'formatDateTime',
      filterRender: { name: 'FilterDateRange' },
      filters: [{ data: { date: [], type: '0' } }],
    },
    {
      field: 'TallyBy',
      title: '操作人',
      width: 88,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'PalletNo',
      title: '入库托盘号',
      width: 88,
    },
    {
      field: 'SSCC',
      title: 'SSCC',
      width: 144,
    },
  ],
  filterConfig: {
    remote: true,
  },
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: ({ filters, page, sorts }) => {
        const queryParams: any = Object.assign({}, page);
        // 处理排序条件
        if (sorts.length === 0) {
          queryParams.sort = '_Identify desc';
        } else {
          const sortItem = sorts.map((item) => {
            const newItem = `${item.field} ${item.order}`;
            return newItem;
          });
          queryParams.sort = sortItem.join(',');
        }
        // 处理筛选
        Object.getOwnPropertyNames(searchField).forEach((key) => {
          searchField[key] = undefined;
        });
        filters.forEach(({ field, values }) => {
          queryParams[field] = values.join(',');
          if (Object.prototype.hasOwnProperty.call(searchField, field)) {
            const jsonObject = JSON.parse(values.toString());
            searchField[field] = jsonObject.text;
          }
        });
        return getRecord4BWOutTally(queryParams)
          .then((res) => {
            Object.assign(qty, res.qty);
            return res;
          })
          .catch(() => {})
          .finally(() => {});
      },
    },
    seq: true,
  },
  showFooter: true,
  scrollX: { enabled: true, gt: 0 },
  scrollY: { enabled: true, gt: 0 },
  size: 'mini',
  sortConfig: {
    remote: true,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
  footerRowStyle: { 'font-weight': 'bold ', backgroundColor: '#ebfbee' },
  footerMethod({ columns }) {
    return [
      columns.map((column, columnIndex) => {
        if (columnIndex === 0) {
          return `合计`;
        }
        if (
          [
            'CIQQty',
            'GoodsNum',
            'MissingQty',
            'RetentionQty',
            'ShelveQty',
            'TallyBoxNum',
            'TallyQty',
          ].includes(column.field)
        ) {
          return qty[column.field];
        }

        return null;
      }),
    ];
  },
});
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });
function searchEvent(field: string) {
  const column = gridApi.grid.getColumnByField(field);
  if (column) {
    // 修改第一个选项为勾选状态
    const option = column.filters[0];
    if (!option) {
      return;
    }

    if (searchField[field]) {
      option.data = { type: '1', text: searchField[field] };
      option.value = JSON.stringify(option.data);
      option.checked = true;
    } else {
      option.data = { type: '0', text: undefined };
      option.checked = false;
    }

    // 修改条件之后，需要手动调用 updateData 处理表格数据
    gridApi.grid.updateData();
    gridApi.grid.commitProxy('query');
  }
}
function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
  gridApi.grid.commitProxy('query');
}
async function fetch() {}
onMounted(() => {
  setTimeout(() => {
    fetch();
  }, 300);
});
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar_left>
        <a-button class="mr-1" @click="clearFilterAndSort()">
          <AntClear class="size-5" />
        </a-button>
        <div class="hidden pl-1 md:block">
          <a-input-search
            v-model:value="searchField.BLNo"
            allow-clear
            class="mr-2 w-60"
            placeholder="出库提单BL"
            @search="searchEvent('BLNo')"
          />
        </div>
      </template>
      <template #isBatchDiff="{ row }">
        <Tag v-if="row.TallyStatus" :color="row.IsBatchDiff ? 'red' : 'green'">
          {{ row.IsBatchDiff ? '有差异' : '无差异' }}
        </Tag>
      </template>
      <template #toolbar-tools> </template>
    </Grid>
  </Page>
</template>
