<script setup lang="ts">
import type { VxeGridProps } from '#/adapter';

import { reactive } from 'vue';

import { Page, useVbenDrawer } from '@vben/common-ui';
import { AntClear } from '@vben/icons';

import { Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import { getTaskList } from '#/api/lis';

import hiDrawer from './hiDrawer.vue';

const [HIDrawer, hiDrawerApi] = useVbenDrawer({
  connectedComponent: hiDrawer,
});
function openHIDrawer(id: string) {
  hiDrawerApi.setData({ id });
  hiDrawerApi.open();
}

const searchField: any = reactive({
  TaskNo: undefined,
});

const gridOptions = reactive<VxeGridProps>({
  id: 'LISHBLInvoiceTaskList',
  columns: [
    {
      title: '#',
      type: 'seq',
      width: 60,
      fixed: 'left',
    },
    {
      field: 'TaskNo',
      title: '任务单据',
      fixed: 'left',
      width: 180,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      slots: { default: 'taskNo' },
    },
    {
      field: 'TaskType',
      title: '任务类型',
      width: 100,
      filters: [
        { label: 'Document', value: 'Document' },
        { label: 'Manual', value: 'Manual' },
      ],
      filterMultiple: true,
    },
    {
      field: 'TaskStatus',
      title: 'API请求状态',
      width: 128,
      filters: [
        { label: '已完成', value: true },
        { label: '未完成', value: false },
      ],
      filterMultiple: false,
      slots: { default: 'taskStatus' },
    },
    {
      field: 'AnalysisStatus',
      title: '解析状态',
      width: 128,
      filters: [
        { label: '已完成', value: 1 },
        { label: '未完成', value: 0 },
        { label: '解析失败', value: -1 },
      ],
      filterMultiple: true,
      slots: { default: 'analysisStatus' },
    },
    /* {
      field: 'AnalysisTime',
      title: '解析耗时',
      width: 100,
    }, */
    {
      field: 'HBLCount',
      title: 'HBL数量',
      width: 100,
    },
    {
      field: 'HBLs',
      title: '包含HBL',
      minWidth: 240,
      filters: [{ data: null }],
      filterRender: { name: 'FilterInput' },
    },
    {
      field: '创建人',
      title: 'CreateBy',
      width: 88,
      visible: false,
      filters: [{ data: null }],
      filterRender: { name: 'FilterInput' },
    },
    {
      field: 'CreateDate',
      title: '创建日期',
      width: 150,
      formatter: 'formatDateTime',
      sortable: true,
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'APIExecuteDate',
      title: 'API请求日期',
      width: 150,
      formatter: 'formatDateTime',
      sortable: true,
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
  ],
  filterConfig: {
    remote: true,
  },
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: ({ filters, page, sorts }) => {
        const queryParams: any = Object.assign({}, page);
        // 处理排序条件
        if (sorts.length === 0) {
          queryParams.sort = '_Identify desc';
        } else {
          const sortItem = sorts.map((item) => {
            const newItem = `${item.field} ${item.order}`;
            return newItem;
          });
          queryParams.sort = sortItem.join(',');
        }
        // 处理筛选
        Object.getOwnPropertyNames(searchField).forEach((key) => {
          searchField[key] = undefined;
        });
        filters.forEach(({ field, values }) => {
          queryParams[field] = values.join(',');
          if (Object.prototype.hasOwnProperty.call(searchField, field)) {
            const jsonObject = JSON.parse(values.toString());
            searchField[field] = jsonObject.text;
          }
        });
        return getTaskList(queryParams);
      },
    },
    seq: true,
  },
  scrollX: { enabled: true, gt: 0 },
  scrollY: { enabled: true, gt: 0 },
  size: 'mini',
  sortConfig: {
    remote: true,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
});
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });
function searchEvent(field: string) {
  const column = gridApi.grid.getColumnByField(field);
  if (column) {
    // 修改第一个选项为勾选状态
    const option = column.filters[0];
    if (!option) {
      return;
    }

    if (searchField[field]) {
      option.data = { type: '1', text: searchField[field] };
      option.value = JSON.stringify(option.data);
      option.checked = true;
    } else {
      option.data = { type: '0', text: undefined };
      option.checked = false;
    }

    // 修改条件之后，需要手动调用 updateData 处理表格数据
    gridApi.grid.updateData();
    gridApi.grid.commitProxy('query');
  }
}
function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
  gridApi.grid.commitProxy('query');
}
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar_left>
        <a-button class="mr-1" @click="clearFilterAndSort()">
          <AntClear class="size-5" />
        </a-button>
        <div class="hidden pl-1 md:block">
          <a-input-search
            v-model:value="searchField.TaskNo"
            allow-clear
            class="mr-2 w-60"
            placeholder="任务单据"
            @search="searchEvent('TaskNo')"
          />
        </div>
      </template>
      <template #toolbar-tools> </template>
      <template #taskStatus="{ row }">
        <Tag :color="row.TaskStatus ? 'green' : 'orange'">
          {{ row.TaskStatus ? '已完成' : '未完成' }}
        </Tag>
      </template>
      <template #analysisStatus="{ row }">
        <Tag
          :color="
            row.AnalysisStatus === 1
              ? 'green'
              : row.AnalysisStatus === 0
                ? 'orange'
                : 'red'
          "
        >
          {{
            row.AnalysisStatus === 1
              ? '已完成'
              : row.AnalysisStatus === 0
                ? '未完成'
                : '解析失败'
          }}
        </Tag>
      </template>
      <template #taskNo="{ row }">
        <a-button size="small" type="link" @click="openHIDrawer(row.Guid)">
          {{ row.TaskNo }}
        </a-button>
      </template>
    </Grid>
    <HIDrawer />
  </Page>
</template>
