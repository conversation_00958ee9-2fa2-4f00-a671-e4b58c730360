<script lang="ts" setup>
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter';
import { apiHeadersUpdate } from '#/api/maintenance/interface';

defineOptions({
  name: 'HeadersModal',
});

const emit = defineEmits(['success']);
const data = ref();

const [Form, formApi] = useVbenForm({
  handleSubmit: onSubmit,
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  schema: [
    {
      component: 'InputNumber',
      componentProps: {
        min: 0,
      },
      fieldName: 'HeaderOrder',
      label: 'Sort No.',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 32,
      },
      fieldName: 'HeaderKey',
      label: 'Key',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 64,
      },
      fieldName: 'HeaderValue',
      label: 'Value',
      rules: 'required',
    },
  ],
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  fullscreenButton: false,
  closeOnClickModal: false,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await formApi.submitForm();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      data.value = modalApi.getData<Record<string, any>>();
    }
  },
  title: 'Add New Header',
});

async function onSubmit(values: Record<string, any>) {
  const check = await formApi.validate();
  if (check.valid) {
    modalApi.setState({ confirmLoading: true });
    modalApi.setState({ loading: true });
    apiHeadersUpdate(
      Object.assign({ id: 'new', APIID: data.value.apiID }, values),
    )
      .then(() => {
        message.success('操作成功');
        modalApi.close();
        emit('success');
      })
      .catch(() => {})
      .finally(() => {
        modalApi.setState({ confirmLoading: false });
        modalApi.setState({ loading: false });
      });
  }
}
</script>
<template>
  <Modal>
    <Form />
  </Modal>
</template>
