<script setup lang="ts">
import type { VxeGridListeners, VxeGridProps } from '#/adapter';

import { reactive } from 'vue';

import { Page, useVbenDrawer } from '@vben/common-ui';
import { AntClear, AntDelete } from '@vben/icons';

import { message, Modal, Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import { getRoleList, roleDelete } from '#/api/maintenance/user';

import roleDrawer from './roleDrawer.vue';

const [RoleDrawer, roleDrawerApi] = useVbenDrawer({
  // 连接抽离的组件
  connectedComponent: roleDrawer,
});

const searchField: any = reactive({
  RoleName: undefined,
});
const gridOptions = reactive<VxeGridProps>({
  id: 'UserRole',
  columns: [
    {
      title: '#',
      type: 'seq',
      width: 60,
    },
    {
      field: 'RoleName',
      filterRender: { name: 'FilterInput' },
      filters: [{ data: null }],
      title: '角色名称',
      width: 160,
    },
    {
      field: 'IsActived',
      filterMultiple: false,
      filters: [
        { label: '正常', value: true },
        { label: '停用', value: false },
      ],
      slots: { default: 'isActived' },
      title: '状态',
      width: 80,
    },
    {
      field: 'Description',
      minWidth: 100,
      title: '描述',
    },
    {
      field: 'CreateDate',
      filterRender: { name: 'FilterDateRange' },
      filters: [{ data: { date: [], type: '0' } }],
      formatter: 'formatDateTime',
      sortable: true,
      title: '创建日期',
      width: 136,
    },
    {
      field: 'UpdateDate',
      filterRender: { name: 'FilterDateRange' },
      filters: [{ data: { date: [], type: '0' } }],
      formatter: 'formatDateTime',
      sortable: true,
      title: '更新日期',
      width: 136,
    },
    {
      align: 'center',
      slots: { default: 'action' },
      title: '删除',
      width: 60,
    },
  ],
  filterConfig: {
    remote: true,
  },
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: ({ filters, page, sorts }) => {
        const queryParams: any = Object.assign({}, page);
        // 处理排序条件
        if (sorts.length === 0) {
          queryParams.sort = '_Identify desc';
        } else {
          const sortItem = sorts.map((item) => {
            const newItem = `${item.field} ${item.order}`;
            return newItem;
          });
          queryParams.sort = sortItem.join(',');
        }
        // 处理筛选
        Object.getOwnPropertyNames(searchField).forEach((key) => {
          searchField[key] = undefined;
        });
        filters.forEach(({ field, values }) => {
          queryParams[field] = values.join(',');
          if (Object.prototype.hasOwnProperty.call(searchField, field)) {
            searchField[field] = values.toString();
          }
        });
        return getRoleList(queryParams);
      },
    },
    seq: true,
  },
  scrollY: { enabled: true, gt: 0 },
  size: 'mini',
  sortConfig: {
    remote: true,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
});
const gridEvents: VxeGridListeners = {
  cellDblclick({ row }) {
    openAccountDrawer(row.Guid);
  },
};

const [Grid, gridApi] = useVbenVxeGrid({ gridEvents, gridOptions });

function searchEvent(field: string, type: string) {
  const column = gridApi.grid.getColumnByField(field);
  if (column) {
    // 修改第一个选项为勾选状态
    const option = column.filters[0];
    if (!option) {
      return;
    }
    if (searchField[field]) {
      option.data = searchField[field];
      option.value = option.data;
      option.checked = true;
    } else {
      if (type) {
        option.data = undefined;
        option.checked = false;
      }
    }
    // 修改条件之后，需要手动调用 updateData 处理表格数据
    gridApi.grid.updateData();
    gridApi.grid.commitProxy('query');
  }
}
function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
  gridApi.grid.commitProxy('query');
}
function openAccountDrawer(id: string) {
  roleDrawerApi.setData({ id });
  roleDrawerApi.open();
}
function handleSuccess() {
  gridApi.grid.commitProxy('query');
}
function handleDelete(id: string) {
  roleDelete(id)
    .then(() => {
      message.success('操作成功');
      handleSuccess();
    })
    .catch(() => {})
    .finally(() => {});
}

function showDeleteModal(id: string, name: string) {
  Modal.confirm({
    content: `请确认是否删除？`,
    /* icon: createVNode(ExclamationCircleOutlined), */
    onCancel() {},
    onOk() {
      handleDelete(id);
    },
    title: `角色名称：${name}`,
    okButtonProps: { danger: true },
  });
}
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar_left>
        <a-button class="mr-1" @click="clearFilterAndSort()">
          <AntClear class="size-5" />
        </a-button>
        <div class="hidden pl-1 md:block">
          <a-input-search
            v-model:value="searchField.RoleName"
            allow-clear
            class="w-60"
            placeholder="角色名称"
            @search="searchEvent('RoleName', 'Input')"
          />
        </div>
      </template>
      <template #isActived="{ row }">
        <Tag :color="row.IsActived ? 'green' : 'red'">
          {{ row.IsActived ? '正常' : '停用' }}
        </Tag>
      </template>
      <template #toolbar-tools>
        <a-button class="mr-3" type="primary" @click="openAccountDrawer('new')">
          新增角色
        </a-button>
      </template>
      <template #action="{ row }">
        <a-button
          danger
          size="small"
          type="link"
          @click="showDeleteModal(row.Guid, row.RoleName)"
        >
          <AntDelete class="size-5" />
        </a-button>
      </template>
    </Grid>
    <RoleDrawer @success="handleSuccess" />
  </Page>
</template>
