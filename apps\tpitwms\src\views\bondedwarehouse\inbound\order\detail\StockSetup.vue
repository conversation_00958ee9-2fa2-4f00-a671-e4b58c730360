<script lang="ts" setup>
import type { VxeGridProps } from '#/adapter';

import { reactive, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import { bwInOrderStockSetup } from '#/api/bondedwarehouse/inbound';

const emit = defineEmits(['success']);

const data = ref();

const gridOptions = reactive<VxeGridProps>({
  id: 'BWInOrderStockSetup',
  columns: [
    {
      field: 'Material',
      title: '物料编号',
      minWidth: 100,
    },
    {
      field: 'Batch',
      title: '批号',
      minWidth: 100,
    },
    {
      field: 'GoodsNum',
      title: '产品数量',
      minWidth: 100,
    },
    /* {
      field: 'Plant',
      title: '工厂',
      minWidth: 100,
    }, */
    {
      field: 'StorageLocation',
      title: 'SAP位置',
      minWidth: 100,
    },
  ],
  filterConfig: {
    remote: false,
  },
  height: 'auto',
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    enable: false,
  },
  scrollX: {
    enabled: false,
  },
  scrollY: {
    enabled: false,
  },
  size: 'mini',
  sortConfig: {
    remote: false,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
    custom: false,
    refresh: false,
  },
});

const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });
const [StockSetupModal, modalApi] = useVbenModal({
  class: 'w-[720px] h-full',
  fullscreenButton: true,
  closeOnClickModal: false,
  /* showConfirmButton: false, */
  confirmText: '初始化导入',
  zIndex: 900,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await handleSetup();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      data.value = modalApi.getData<Record<string, any>>();
      gridApi.setGridOptions({ data: [] });
    } else {
      emit('success');
    }
  },
  title: '库存初始化',
});
function exportEvent() {
  gridApi.grid.exportData({
    filename: `库存初始化模板`,
    sheetName: 'StockSetup',
    type: 'xlsx',
    original: true,
  });
}
function importEvent() {
  gridApi.grid.importData({
    types: ['xlsx'],
  });
}
function handleSetup() {
  const table = gridApi.grid.getTableData();
  if (table.tableData.length === 0) {
    message.info('不存在待初始化的库存，请填入模板并导入！');
  }
  modalApi.setState({ confirmLoading: true });
  modalApi.setState({ loading: true });
  bwInOrderStockSetup({ id: data.value.id, values: table.tableData })
    .then(() => {
      message.success('操作成功！');
      modalApi.close();
    })
    .catch(() => {})
    .finally(() => {
      modalApi.setState({ loading: false });
      modalApi.setState({ confirmLoading: false });
    });
}
</script>
<template>
  <StockSetupModal>
    <Grid>
      <template #toolbar_left>
        <a-button class="mr-3" @click="exportEvent">下载模板</a-button>
        <a-button class="mr-3" type="primary" @click="importEvent">
          上传模板
        </a-button>
      </template>
    </Grid>
  </StockSetupModal>
</template>
