<script lang="ts" setup>
import { onActivated, onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';

import { Page, VbenButton } from '@vben/common-ui';
import { useTabs } from '@vben/hooks';
import { AntCaretDown, AntCaretUp, AntSearch } from '@vben/icons';
import { useTabbarStore } from '@vben/stores';
import { downloadFileFromBlob } from '@vben/utils';

import { Card, message, Modal, TabPane, Tabs } from 'ant-design-vue';

import { useVbenForm } from '#/adapter';
import {
  bwOutOrderCheckIsProcessingRequired,
  bwOutOrderHeadUpdate,
  bwOutOrderLISLabelWorkCreate,
  getOutOrderHead,
} from '#/api/bondedwarehouse/outbound';
import { createFile } from '#/api/common';

import TabItem from './tabItem.vue';
import TabProcessing from './tabProcessing.vue';
import TabTally from './tabTally.vue';

const headData = ref({}) as any;
const init = ref(false);
const loadingRef = ref(false);
const showForm = ref(true);
const submitLoading = ref(false);
const activeKey = ref('Item');
const route = useRoute();
const { setTabTitle } = useTabs();
const id = route.params?.id?.toString() ?? '';

const [Form, baseFormApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'pb-1',
    labelClass: 'w-24',
  },
  // 提交函数
  handleSubmit: onSubmit,
  layout: 'horizontal',
  schema: [
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'BLNo',
      label: '出库提单',
      disabled: true,
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'OutDeliveryType',
      label: '订单类型',
      disabled: true,
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'OwnerShortName',
      label: '货主简称',
      disabled: true,
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'WarehouseName',
      label: '仓库',
      disabled: true,
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'HBL',
      label: '绑定HBL',
      disabled: true,
    },
    {
      component: 'Input',
      componentProps: {
        maxlength: 10,
        autocomplete: 'off',
      },
      fieldName: 'YUB',
      label: 'SAP销售订单',
    },
    {
      component: 'DatePicker',
      componentProps: {
        style: { width: '100%' },
        valueFormat: 'YYYY-MM-DD',
      },
      fieldName: 'PreDeliveryDate',
      label: '预计发货日期',
    },
    {
      component: 'DatePicker',
      componentProps: {
        style: { width: '100%' },
        valueFormat: 'YYYY-MM-DD',
      },
      fieldName: 'OutDeclareDate',
      label: '出库申报日期',
    },
    {
      component: 'DatePicker',
      componentProps: {
        style: { width: '100%' },
        valueFormat: 'YYYY-MM-DD',
      },
      fieldName: 'CIQDate',
      label: 'CIQ出证日期',
    },
    {
      component: 'DatePicker',
      componentProps: {
        style: { width: '100%' },
        valueFormat: 'YYYY-MM-DD',
      },
      fieldName: 'PaymentDate',
      label: '付税日期',
    },
    {
      component: 'Input',
      componentProps: {
        maxlength: 32,
        autocomplete: 'off',
      },
      fieldName: 'OutContractNo',
      label: '出库合同号',
    },
    {
      component: 'Input',
      componentProps: {
        maxlength: 64,
        autocomplete: 'off',
      },
      fieldName: 'OutEntryID',
      label: '出库报关单',
      formItemClass: 'col-span-1 md:col-span-2',
    },
    {
      component: 'Input',
      componentProps: {
        maxlength: 128,
        autocomplete: 'off',
      },
      fieldName: 'Remark',
      label: '备注',
      formItemClass: 'col-span-1 md:col-span-2',
    },
  ],
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2 md:grid-cols-3 xl:grid-cols-5',
});
async function handleGetFile() {
  if (await bwOutOrderCheckIsProcessingRequired(id)) {
    submitLoading.value = true;
    createFile('ProcessingAllocate', id)
      .then((res) => {
        downloadFileFromBlob({
          source: res.data,
          fileName: `分货贴标清单_${headData.value.BLNo}.xls`,
        });
      })
      .catch(() => {
        /* message.error(`生成文件错误！`); */
      })
      .finally(() => {
        submitLoading.value = false;
      });
  } else {
    message.info(`不存在需要贴标的明细，请检查备货表体中的是否需要贴标！`);
  }
}
async function handleGetLabel() {
  submitLoading.value = true;
  createFile('SampleProductLabel', id)
    .then((res) => {
      downloadFileFromBlob({
        source: res.data,
        fileName: `留样产品标签_${headData.value.BLNo}.pdf`,
      });
    })
    .catch(() => {
      /* message.error(`生成文件错误！`); */
    })
    .finally(() => {
      submitLoading.value = false;
    });
}
async function onSubmit(values: Record<string, any>) {
  const check = await baseFormApi.validate();
  if (check.valid) {
    submitLoading.value = true;
    bwOutOrderHeadUpdate(Object.assign({ id }, values))
      .then(() => {
        message.success('操作成功');
      })
      .catch(() => {
        baseFormApi.setValues(headData.value);
      })
      .finally(() => {
        setTimeout(() => {
          submitLoading.value = false;
        }, 500);
      });
  } else {
    message.info('必填项未填写完整，请检查！');
  }
}
/* function handleSuccess() {
  fetch();
} */
function handleCreateLISLabelWork() {
  loadingRef.value = true;
  bwOutOrderLISLabelWorkCreate(id)
    .then(() => {
      message.success('LIS推送任务创建成功');
    })
    .catch(() => {})
    .finally(() => {
      loadingRef.value = false;
    });
}
function showLisModal() {
  Modal.confirm({
    content: `请确认是否推送更新LIS贴标明细？`,
    onCancel() {},
    onOk() {
      handleCreateLISLabelWork();
    },
    title: `${headData.value.BLNo}`,
    okButtonProps: { danger: true },
  });
}
async function fetch() {
  const res = await getOutOrderHead(id);
  headData.value = res.head;
  /* const fieldList = ['TrafMode', 'EntyPort', 'WrapType', 'CargoType'];
  fieldList.forEach((field) => {
    baseFormApi.updateSchema([
      {
        componentProps: {
          options: res.options?.filter((item: any) => {
            return item.type === field;
          }),
        },
        fieldName: field,
      },
    ]);
  }); */
  const tabbarStore = useTabbarStore();
  const currentTab = tabbarStore.getTabs.find(
    (item) => item.path === route.path,
  );
  if (currentTab?.meta.title === '出库操作') {
    setTabTitle(`出库操作-${res.bl}`);
  }

  baseFormApi.setValues(headData.value);
  init.value = false;
}
onMounted(() => {
  init.value = true;
  showForm.value = true;
  fetch();
});
onActivated(() => {
  const tabbarStore = useTabbarStore();
  const currentTab = tabbarStore.getTabs.find(
    (item) => item.path === route.path,
  );
  if (!currentTab?.meta.newTabTitle && !init.value) {
    fetch();
  }
});
</script>

<template>
  <Page auto-content-height v-loading="loadingRef">
    <div class="flex h-full flex-col">
      <Card :body-style="{ padding: '4px' }" :bordered="false" size="small">
        <template #title>
          <div class="hidden md:block">
            {{ `出库订单表头` }}
          </div>
        </template>
        <template #extra>
          <div class="flex">
            <a-button
              class="mr-2"
              :loading="submitLoading"
              @click="handleGetFile()"
            >
              分货贴标清单
            </a-button>
            <a-button
              :loading="submitLoading"
              class="mr-2"
              @click="handleGetLabel()"
            >
              留样产品标签
            </a-button>
            <a-button
              :loading="submitLoading"
              class="mr-2"
              type="primary"
              @click="baseFormApi.submitForm()"
            >
              保存
            </a-button>
            <VbenButton
              class="mr-2"
              size="sm"
              variant="info"
              @click="showLisModal"
            >
              推送LIS贴标明细
            </VbenButton>
            <AntCaretDown
              v-if="showForm"
              class="size-6"
              @click="showForm = !showForm"
            />
            <AntCaretUp v-else class="size-6" @click="showForm = !showForm" />
          </div>

          <!-- <a-button type="link" @click="showForm = !showForm"> -->
          <!-- </a-button> -->
        </template>

        <Form v-show="showForm">
          <template #Invoices="slotProps">
            <a-input-search v-bind="slotProps" readonly="readonly" @search="1">
              <template #enterButton>
                <a-button class="!pl-1 !pr-1">
                  <AntSearch class="size-5" />
                </a-button>
              </template>
            </a-input-search>
          </template>
        </Form>
      </Card>
      <div class="inorder-tab min-h-0 flex-1">
        <Tabs v-model:active-key="activeKey" class="h-full" tab-position="left">
          <TabPane key="Item" tab="备货">
            <div v-if="activeKey === 'Item'" class="h-full">
              <TabItem :id="id" :bl="headData.BLNo" />
            </div>
          </TabPane>
          <TabPane key="Processing" tab="贴标">
            <div v-if="activeKey === 'Processing'" class="h-full">
              <TabProcessing :id="id" :bl="headData.BLNo" />
            </div>
          </TabPane>
          <TabPane key="Tally" tab="理货">
            <div v-if="activeKey === 'Tally'" class="h-full">
              <TabTally :id="id" :bl="headData.BLNo" />
            </div>
          </TabPane>
          <!-- <TabPane key="CSARQC" tab="CSAR">
            <div v-if="activeKey === 'CSARQC'" class="h-full">
              <TabCSAR
                :id="id"
                :hbl="headData.HBL"
                :is-closed="headData.IsClosed"
              />
            </div>
          </TabPane> -->
          <!-- <TabPane key="IBDGR" tab="IBD">
            <div v-if="activeKey === 'IBDGR'" class="h-full">
              <TabInbound
                :id="id"
                :hbl="headData.HBL"
                :is-closed="headData.IsClosed"
              />
            </div>
          </TabPane> -->
          <!-- <TabPane key="Shelve" tab="上架">
            <div v-if="activeKey === 'Shelve'">222</div>
          </TabPane> -->
        </Tabs>
      </div>
    </div>
  </Page>
</template>

<style scoped lang="less">
::v-deep(.inorder-tab) {
  background-color: #fff;

  .ant-tabs-tabpane .ant-tabs-tabpane-active {
    padding-left: 0 !important;
  }

  .ant-tabs-left > .ant-tabs-nav .ant-tabs-tab {
    padding: 8px 12px;
  }

  .ant-tabs-tab-active {
    background-color: #e7f5ff;
  }

  .ant-tabs-content-holder > .ant-tabs-content > .ant-tabs-tabpane {
    padding-left: 0;
  }
  .ant-tabs-content-left {
    height: 100%;
  }
}
</style>
