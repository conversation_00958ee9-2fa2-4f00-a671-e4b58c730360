<script lang="ts" setup>
import { reactive, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Checkbox } from 'ant-design-vue';

import { useVbenForm } from '#/adapter';
import { bwOutProcessingItemMultiUpdate } from '#/api/bondedwarehouse/outbound';

const emit = defineEmits(['success']);
const data = ref();
const isUpdateNull = ref(false);
const cName = reactive({
  ProcessingAreaName: undefined,
});

const [Form, formApi] = useVbenForm({
  handleSubmit: onSubmit,
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  schema: [
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        getPopupContainer: () => document.body,
        mode: 'tags',
      },
      fieldName: 'ProcessingMethod',
      label: '加工方式',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        getPopupContainer: () => document.body,
        onChange: (value: string, option: any) => {
          cName.ProcessingAreaName = value ? option.label : undefined;
        },
      },
      fieldName: 'ProcessingAreaID',
      label: '加工位置',
    },
    {
      component: 'DatePicker',
      componentProps: {
        style: { width: '100%' },
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
      },
      fieldName: 'ProcessingDate',
      label: '贴标日期',
    },
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 16,
      },
      fieldName: 'ProcessingBy',
      label: '操作人',
    },
  ],
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  draggable: true,
  fullscreenButton: false,
  closeOnClickModal: false,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await formApi.submitForm();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      data.value = modalApi.getData<Record<string, any>>();
      modalApi.setState({ showCancelButton: true });
      modalApi.setState({ showConfirmButton: true });
      formApi.resetForm();
      isUpdateNull.value = false;
      cName.ProcessingAreaName = undefined;
      fetch();
    }
  },
  title: '批量设置',
});

async function onSubmit(values: Record<string, any>) {
  const check = await formApi.validate();
  if (check.valid) {
    modalApi.setState({ confirmLoading: true });
    modalApi.setState({ loading: true });
    bwOutProcessingItemMultiUpdate(
      Object.assign(
        { ids: data.value.ids, typ: isUpdateNull.value },
        values,
        cName,
      ),
    )
      .then(() => {
        modalApi.setState({ showCancelButton: false });
        modalApi.setState({ showConfirmButton: false });
        handleClose();
      })
      .catch(() => {})
      .finally(() => {
        modalApi.setState({ loading: false });
        modalApi.setState({ confirmLoading: false });
      });
  }
}
function handleClose() {
  modalApi.close();
  emit('success');
}
async function fetch() {
  const options = data.value.options;
  formApi.updateSchema([
    {
      componentProps: {
        options: options.filter((item: any) => {
          return item.type === 'ProcessingMethod';
        }),
      },
      fieldName: 'ProcessingMethod',
    },
  ]);
  formApi.updateSchema([
    {
      componentProps: {
        options: options.filter((item: any) => {
          return item.type === 'ProcessingArea';
        }),
      },
      fieldName: 'ProcessingAreaID',
    },
  ]);
  const defauleParams: any = {};
  const foundOptionPM = options.find(
    (item: any) => item.type === 'ProcessingMethod' && item.d === true,
  );
  if (foundOptionPM) {
    defauleParams.ProcessingMethod = [foundOptionPM.value];
  }
  const foundOptionPA = options.find(
    (item: any) => item.type === 'ProcessingArea' && item.d === true,
  );
  if (foundOptionPA) {
    defauleParams.ProcessingAreaID = foundOptionPA.value;
    cName.ProcessingAreaName = foundOptionPA.label;
  }
  formApi.setValues(defauleParams);
}
</script>
<template>
  <Modal>
    <Form />
    <template #prepend-footer>
      <Checkbox v-model:checked="isUpdateNull">空值覆盖（清除）</Checkbox>
    </template>
  </Modal>
</template>
