<script setup lang="ts">
import type { VxeGridListeners, VxeGridProps } from '#/adapter';

import { defineProps, reactive } from 'vue';

import { Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import { getBWStockFlowByDoc } from '#/api/bondedwarehouse/stock';

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
  doc: {
    type: String,
    default: '',
  },
  isClosed: {
    type: Boolean,
    default: false,
  },
});

const gridOptions = reactive<VxeGridProps>({
  id: 'BWStockMovementFlow',
  columns: [
    {
      title: '#',
      type: 'seq',
      width: 60,
    },
    { field: 'Plant', title: '工厂', width: 80 },
    {
      field: 'StorageLocation',
      title: 'SAP位置',
      width: 100,
    },
    {
      field: 'MatCode',
      title: '物料编号',
      width: 100,
    },
    {
      field: 'BatchNo',
      title: '批号',
      width: 100,
    },
    {
      field: 'SAPBatch',
      title: 'SAP主批次',
      width: 100,
    },
    { field: 'StockType', title: '库存类型', width: 100 },
    { field: 'FlowQty', title: '调整数量', width: 100 },

    {
      field: 'CreateDate',
      title: '调整日期',
      width: 150,
    },
    { field: 'PalletNo', title: '内部托盘', width: 100 },
    { field: 'StockStatus', title: 'WMS状态', width: 100 },
    { field: 'BrandName', title: '品牌名称', width: 100 },
    { field: 'CName', title: '中文名称', minWidth: 240 },
  ],
  filterConfig: {
    remote: false,
  },
  height: 'auto',
  keepSource: true,
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    ajax: {
      query: async () => {
        return await getBWStockFlowByDoc(props.id);
      },
    },
    seq: true,
  },
  rowStyle({ row }) {
    return row.FlowQty > 0
      ? {
          backgroundColor: '#ebfbee',
          color: '#222',
        }
      : {
          backgroundColor: '#fff5f5',
          color: '#222',
        };
  },
  showFooter: false,
  size: 'mini',
  sortConfig: {
    remote: false,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
  scrollX: { enabled: true, gt: 0 },
  scrollY: { enabled: true, mode: 'wheel', gt: 30, oSize: 0 },
  menuConfig: {
    trigger: 'cell',
    body: {
      options: [
        [
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuCopy',
            disabled: false,
            name: '复制单元格',
            prefixConfig: {
              icon: 'vxe-icon-copy',
            },
            visible: true,
          },
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuRefresh',
            disabled: false,
            name: '刷新',
            prefixConfig: { icon: 'vxe-icon-refresh' },
            visible: true,
          },
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuExport',
            disabled: false,
            name: '导出明细',
            prefixConfig: { icon: 'vxe-icon-download' },
            visible: true,
          },
        ],
      ],
    },
    className: 'font-medium shadow rounded-md',
  },
});
const gridEvents: VxeGridListeners = {
  menuClick({ menu }) {
    switch (menu.code) {
      case 'menuExport': {
        handleExport();
        break;
      }
      default: {
        break;
      }
    }
  },
};
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions, gridEvents });

function handleExport() {
  gridApi.grid.exportData({
    filename: `库存变动记录_${props.doc}`,
    sheetName: 'Sheet1',
    type: 'xlsx',
    isFooter: false,
    columnFilterMethod: ({ column }) => {
      return column.field !== undefined;
    },
  });
}
/* function handleSuccess() {
  gridApi.grid.commitProxy('query');
} */

/* function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
} */
</script>

<template>
  <div class="flex h-full flex-col">
    <Grid>
      <template #toolbar_left>
        <div class="hidden pl-1 text-base font-medium md:block">
          <span class="mr-2">库存变动记录</span>
        </div>
      </template>
      <template #toolbar-tools> </template>
      <template #status="{ row }">
        <Tag :color="row.Status ? 'green' : 'red'">
          {{ row.Status ? '已检查' : '待检查' }}
        </Tag>
      </template>
    </Grid>
  </div>
</template>
