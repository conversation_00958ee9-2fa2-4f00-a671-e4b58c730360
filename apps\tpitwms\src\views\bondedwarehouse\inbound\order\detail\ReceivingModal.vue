<script lang="ts" setup>
import { h, ref } from 'vue';

import { useVbenDrawer, useVbenModal } from '@vben/common-ui';
import { AntSearch } from '@vben/icons';

import { Button, Descriptions, Modal, Tag } from 'ant-design-vue';
import dayjs from 'dayjs';

import { useVbenForm, z } from '#/adapter';
import {
  bwInOrderItemGetData,
  bwInOrderItemReceiving,
} from '#/api/bondedwarehouse/inbound';

import batchSelectDrawer from './batchSelectDrawer.vue';

const emit = defineEmits(['success']);

const data = ref();
const itemData = ref({}) as any;
const submitData = ref({}) as any;
const [BatchSelectDrawer, BatchSelectDrawerApi] = useVbenDrawer({
  connectedComponent: batchSelectDrawer,
});

const [Form, formApi] = useVbenForm({
  handleSubmit: onSubmit,
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
    labelClass: 'w-24',
  },
  schema: [
    {
      component: 'InputNumber',
      componentProps: {
        autocomplete: 'off',
        min: 0,
        onChange: (e: number) => {
          formApi.setFieldValue('ReceivedQty', e * itemData.value.iPCB);
        },
      },
      fieldName: 'ReceivedBoxNum',
      label: '实收箱数',
      rules: 'required',
      dependencies: {
        trigger(values, form) {
          if (itemData.value.iPCB > 0) {
            form.setFieldValue(
              'ReceivedBoxNum',
              Math.ceil(values.ReceivedQty / itemData.value.iPCB),
              true,
            );
          }
        },
        // 只有指定的字段改变时，才会触发
        triggerFields: ['ReceivedQty'],
      },
    },
    {
      component: 'InputNumber',
      componentProps: {
        autocomplete: 'off',
        min: 0,
      },
      fieldName: 'ReceivedQty',
      label: '实收数量',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 16,
      },
      fieldName: 'ReceivedBatch',
      label: '实收批次',
    },
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 10,
      },
      fieldName: 'ReceivedExpirationDate',
      label: '实收效期',
    },
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 128,
      },
      fieldName: 'Remark',
      formItemClass: 'col-span-1 md:col-span-2',
      label: '备注',
    },
  ],
  showDefaultActions: false,
  wrapperClass: 'grid-cols-1 md:grid-cols-2',
});

const [BaseModal, modalApi] = useVbenModal({
  class: 'w-[640px]',
  draggable: true,
  fullscreenButton: false,
  closeOnClickModal: false,
  destroyOnClose: true,
  zIndex: 900,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await formApi.submitForm();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      modalApi.setState({ showConfirmButton: false });
      itemData.value = {};
      submitData.value = {};
      data.value = modalApi.getData<Record<string, any>>();
      formApi.resetForm();
      fetch();
    }
  },
  title: '入库理货',
});

async function onSubmit(values: Record<string, any>) {
  const check = await formApi.validate();
  if (check.valid) {
    submitData.value = values;
    checkBatch();
  }
}
function handleSubmit(typ: number) {
  Modal.destroyAll();
  modalApi.setState({ confirmLoading: true });
  modalApi.setState({ loading: true });
  bwInOrderItemReceiving(
    Object.assign({ typ, id: data.value.id }, submitData.value),
  )
    .then(() => {
      handleClose();
    })
    .catch(() => {})
    .finally(() => {
      modalApi.setState({ loading: false });
      modalApi.setState({ confirmLoading: false });
    });
}
function checkBatch() {
  if (
    itemData.value.isBatchManagementRequired &&
    itemData.value.InboundBatch !== submitData.value.ReceivedBatch
  ) {
    Modal.confirm({
      content: `实收批号与产品批次不一致，请确认？`,
      onCancel() {},
      onOk() {
        checkQty();
      },
      title: `批次差异提醒`,
      okButtonProps: { danger: true },
    });
  } else {
    checkQty();
  }
}

function checkQty() {
  if (itemData.value.EstimatedReceivedQty > submitData.value.ReceivedQty) {
    Modal.confirm({
      closable: true,
      content: `实收数量小于产品数量，请拆分处理或确认来货短缺？`,
      footer: () => {
        return h('div', { class: 'flex justify-end gap-2' }, [
          h(
            Button,
            { onClick: Modal.destroyAll },
            {
              default: () => '取消',
            },
          ),
          h(
            Button,
            { type: 'primary', onClick: () => handleSubmit(1) },
            {
              default: () => '拆分处理',
            },
          ),
          h(
            Button,
            { type: 'primary', danger: true, onClick: () => handleSubmit(2) },
            {
              default: () => '确认少货',
            },
          ),
        ]);
      },
      title: `数量差异提醒`,
    });
  } else {
    handleSubmit(0);
  }
}

function handleBatchSearch() {
  BatchSelectDrawerApi.setData({
    material: itemData.value.MatCode,
  });
  BatchSelectDrawerApi.open();
}
function handleBatchSelected(row: any) {
  formApi.setFieldValue('ReceivedBatch', row.SubBatch);
  if (row.ShelfLifeExpirationDate) {
    formApi.setFieldValue(
      'ReceivedExpirationDate',
      row.ShelfLifeExpirationDate.slice(0, 10),
    );
  }
}
function handleClose() {
  modalApi.close();
  emit('success');
}
async function fetch() {
  await bwInOrderItemGetData(data.value.id)
    .then((res) => {
      modalApi.setState({ showConfirmButton: true });
      itemData.value = res;
      formApi.setValues(itemData.value);
      formApi.updateSchema([
        {
          rules: res.isBatchManagementRequired ? 'required' : null,
          fieldName: 'ReceivedBatch',
          disabled: !res.isBatchManagementRequired,
        },
        {
          rules: res.isBatchManagementRequired
            ? z
                .string()
                .regex(
                  /^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])$|^\d{4}-(0[1-9]|1[0-2])$/,
                  { message: '格式：yyyy-MM-dd或yyyy-MM' },
                )
            : null,
          fieldName: 'ReceivedExpirationDate',
          disabled: !res.isBatchManagementRequired,
        },
        {
          rules: z
            .number()
            .max(res.EstimatedReceivedQty, '实收数量不能大于产品数量')
            .min(0, { message: '请输入实收数量' })
            .nullable()
            .refine((value) => value !== null && value !== undefined, {
              message: '请输入实收数量',
            }),
          fieldName: 'ReceivedQty',
        },
      ]);
    })
    .catch(() => {})
    .finally(() => {});
}
</script>
<template>
  <BaseModal>
    <Descriptions :column="{ xs: 1, sm: 2, md: 3 }" bordered size="small">
      <Descriptions.Item :span="2" label="SSCC">
        {{ itemData.SSCC }}
      </Descriptions.Item>
      <Descriptions.Item label="入库托盘">
        {{ itemData.PalletNo }}
      </Descriptions.Item>
      <Descriptions.Item label="产品编号">
        {{ itemData.MatCode }}
      </Descriptions.Item>
      <Descriptions.Item label="产品条码">
        {{ itemData.iBarCode }}
      </Descriptions.Item>
      <Descriptions.Item label="产品类型">
        {{ itemData.iMatType }}
      </Descriptions.Item>
      <Descriptions.Item label="产品批次">
        {{ itemData.InboundBatch }}
      </Descriptions.Item>
      <Descriptions.Item label="制造日期">
        {{
          itemData.ManufactureDate
            ? dayjs(itemData.ManufactureDate).format('YYYY-MM-DD')
            : ''
        }}
      </Descriptions.Item>
      <Descriptions.Item label="限用日期">
        {{
          itemData.ShelfLifeExpirationDate
            ? dayjs(itemData.ShelfLifeExpirationDate).format('YYYY-MM-DD')
            : ''
        }}
      </Descriptions.Item>
      <Descriptions.Item :span="3" label="关联批次">
        {{ itemData.RelatedBatches }}
      </Descriptions.Item>
      <Descriptions.Item label="箱规数量">
        {{ itemData.iPCB }}
      </Descriptions.Item>
      <Descriptions.Item label="层规数量">
        {{ itemData.iPCL }}
      </Descriptions.Item>
      <Descriptions.Item label="托规数量">
        {{ itemData.iPCP }}
      </Descriptions.Item>
      <Descriptions.Item label="产品数量">
        {{ itemData.EstimatedReceivedQty }}
      </Descriptions.Item>
      <Descriptions.Item label="批次管理">
        <Tag :color="itemData.isBatchManagementRequired ? 'green' : 'blue'">
          {{ itemData.isBatchManagementRequired ? '是' : '否' }}
        </Tag>
      </Descriptions.Item>
      <Descriptions.Item label="精细理货">
        <Tag :color="itemData.iIsCareful ? 'green' : 'blue'">
          {{ itemData.iIsCareful ? '是' : '否' }}
        </Tag>
      </Descriptions.Item>
    </Descriptions>
    <Form class="mt-4">
      <template #ReceivedBatch="slotProps">
        <a-input-search v-bind="slotProps" @search="handleBatchSearch">
          <template #enterButton>
            <a-button class="!pl-1 !pr-1">
              <AntSearch class="size-5" />
            </a-button>
          </template>
        </a-input-search>
      </template>
    </Form>
    <BatchSelectDrawer @success="handleBatchSelected" />
  </BaseModal>
</template>
