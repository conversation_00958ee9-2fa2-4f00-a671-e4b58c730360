<script lang="ts" setup>
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Descriptions, InputNumber, message, Modal } from 'ant-design-vue';

import { useVbenForm } from '#/adapter';
import { stockTakingItem2stUpdate } from '#/api/bondedwarehouse/stock';

const emit = defineEmits(['success']);

const data = ref();
const itemData = ref({}) as any;
const submitData = ref({}) as any;

const [Form, formApi] = useVbenForm({
  handleSubmit: onSubmit,
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
    labelClass: 'w-24',
  },
  schema: [
    {
      component: 'InputNumber',
      componentProps: {
        autocomplete: 'off',
        min: 0,
      },
      fieldName: 'ActualUnrestrictedQty',
      label: '实际非限制',
      rules: 'required',
    },
    {
      component: 'InputNumber',
      componentProps: {
        autocomplete: 'off',
        min: 0,
      },
      fieldName: 'ActualBlockedQty',
      label: '实际冻结',
      rules: 'required',
    },
  ],
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2',
});

const [BaseModal, modalApi] = useVbenModal({
  class: 'w-[640px]',
  draggable: true,
  fullscreenButton: false,
  closeOnClickModal: false,
  destroyOnClose: true,
  zIndex: 900,
  confirmText: '确认',
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await formApi.submitForm();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      /* modalApi.setState({ showConfirmButton: false }); */
      submitData.value = {};
      data.value = modalApi.getData<Record<string, any>>();
      itemData.value = data.value.row;
      formApi.resetForm();
      fetch();
    }
  },
  title: '复盘确认',
});

async function onSubmit(values: Record<string, any>) {
  const check = await formApi.validate();
  if (check.valid) {
    submitData.value = values;
    checkQty();
  }
}
function checkQty() {
  if (
    itemData.value.TakingQty ===
    submitData.value.ActualUnrestrictedQty + submitData.value.ActualBlockedQty
  ) {
    handleSubmit();
  } else {
    Modal.confirm({
      content: `复盘数量和初盘数量不一致，请确认？`,
      onCancel() {},
      onOk() {
        handleSubmit();
      },
      title: `操作提醒`,
      okButtonProps: { danger: true },
    });
  }
}

function handleSubmit() {
  Modal.destroyAll();
  modalApi.setState({ confirmLoading: true });
  modalApi.setState({ loading: true });
  stockTakingItem2stUpdate(
    Object.assign({ id: itemData.value.Guid }, submitData.value),
  )
    .then(() => {
      message.success('操作成功');
      handleClose();
    })
    .catch(() => {})
    .finally(() => {
      modalApi.setState({ loading: false });
      modalApi.setState({ confirmLoading: false });
    });
}

function handleClose() {
  modalApi.close();
  emit('success');
}
async function fetch() {
  /* await bwInOrderItemGetData(data.value.id)
    .then((res) => {
      modalApi.setState({ showConfirmButton: true });
      itemData.value = res;
    })
    .catch(() => {})
    .finally(() => {}); */
}
</script>
<template>
  <BaseModal>
    <Descriptions :column="{ xs: 1, sm: 2 }" bordered size="small">
      <Descriptions.Item label="库区">
        {{ itemData.AreaName }}
      </Descriptions.Item>
      <Descriptions.Item label="货位">
        {{ itemData.BIN }}
      </Descriptions.Item>
      <Descriptions.Item label="HBL">{{ itemData.HBL }}</Descriptions.Item>
      <Descriptions.Item label="托盘号">
        {{ itemData.PalletNo }}
      </Descriptions.Item>
      <Descriptions.Item label="物料编号">
        {{ itemData.MatCode }}
      </Descriptions.Item>
      <Descriptions.Item label="批号">{{ itemData.BatchNo }}</Descriptions.Item>
      <!-- <Descriptions.Item label="库存状态">
        {{ itemData.StockStatus }}
      </Descriptions.Item>
      <Descriptions.Item label="账面库存">
        {{ itemData.StockQty }}
      </Descriptions.Item> -->
      <Descriptions.Item label="账面非限制">
        {{ itemData.UnrestrictedQty }}
      </Descriptions.Item>
      <Descriptions.Item label="账面冻结">
        {{ itemData.BlockedQty }}
      </Descriptions.Item>
      <Descriptions.Item label="初盘库存">
        {{ itemData.TakingQty }}
      </Descriptions.Item>
      <Descriptions.Item label="初盘差异">
        {{ itemData.TakingDiffQty }}
      </Descriptions.Item>
    </Descriptions>
    <Form class="mt-4">
      <template #ActualUnrestrictedQty="slotProps">
        <InputNumber v-bind="slotProps" :min="0">
          <template #addonAfter>
            <span v-if="slotProps.value || slotProps.value === 0">
              {{
                `差异：${Number(slotProps.value) - Number(itemData.UnrestrictedQty)}`
              }}
            </span>
          </template>
        </InputNumber>
      </template>
      <template #ActualBlockedQty="slotProps">
        <InputNumber v-bind="slotProps" :min="0">
          <template #addonAfter>
            <span v-if="slotProps.value || slotProps.value === 0">
              {{
                `差异：${Number(slotProps.value) - Number(itemData.BlockedQty)}`
              }}
            </span>
          </template>
        </InputNumber>
      </template>
    </Form>
  </BaseModal>
</template>
