<script setup lang="ts">
import type { VxeGridListeners, VxeGridProps } from '#/adapter';

import { defineProps, reactive, ref } from 'vue';

import { useVbenDrawer, VbenLoading } from '@vben/common-ui';

import { useVbenVxeGrid } from '#/adapter';
import { getStockTakingItemDiff } from '#/api/bondedwarehouse/stock';
import batchDrawer from '#/views/sapneo/master/batch/batchDrawer.vue';
import skuDrawer from '#/views/sapneo/master/product/skuDrawer.vue';

/* import csarDrawer from './CSARDrawer.vue'; */

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
  doc: {
    type: String,
    default: '',
  },
  warehouse: {
    type: String,
    default: '',
  },
  owner: {
    type: String,
    default: '',
  },
  isClosed: {
    type: Boolean,
    default: false,
  },
  isBright: {
    type: Boolean,
    default: false,
  },
});
const loadingRef = ref(false);

const [SkuDrawer, skuDrawerApi] = useVbenDrawer({
  connectedComponent: skuDrawer,
});
const [BatchDrawer, batchDrawerApi] = useVbenDrawer({
  connectedComponent: batchDrawer,
});
function openSkuDrawer(id: string) {
  skuDrawerApi.setData({ id });
  skuDrawerApi.open();
}
function openBatchDrawer(sku: string, batch: string) {
  batchDrawerApi.setData({ sku, batch });
  batchDrawerApi.open();
}

const gridOptions = reactive<VxeGridProps>({
  id: 'BWStockTakingDiff',
  checkboxConfig: { highlight: true, range: true },
  columns: [
    {
      fixed: 'left',
      title: '#',
      type: 'seq',
      width: 60,
    },
    { field: 'AreaName', title: '库区', width: 80 },
    { field: 'BIN', title: '货位', width: 96 },
    { field: 'StockStatus', title: '库存状态', width: 80 },
    { field: 'HBL', title: 'HBL', width: 136 },
    { field: 'PalletNo', title: '托盘号', width: 96 },
    {
      field: 'MatCode',
      title: '物料编号',
      width: 80,
      slots: { default: 'material' },
    },
    {
      field: 'BatchNo',
      title: '批号',
      width: 80,
      slots: { default: 'batch' },
    },
    {
      field: 'SAPBatch',
      title: 'SAP批次',
      width: 80,
    },
    {
      field: 'StorageLocation',
      title: 'SAP位置',
      width: 96,
    },
    { field: 'UnrestrictedQty', title: '账面非限制', minWidth: 84 },
    { field: 'ActualUnrestrictedQty', title: '实际非限制', minWidth: 84 },
    { field: 'DiffUnrestricted', title: '非限制差异', minWidth: 84 },
    { field: 'BlockedQty', title: '账面冻结', minWidth: 72 },
    { field: 'ActualBlockedQty', title: '实际冻结', minWidth: 72 },
    { field: 'DiffBlocked', title: '冻结差异', minWidth: 72 },
    { field: 'TakingBy1st', title: '复盘人', width: 72 },
    {
      field: 'TakingDate1st',
      title: '复盘日期',
      width: 88,
      formatter: 'formatDate',
    },
  ],
  filterConfig: {
    remote: false,
  },
  footerRowStyle: { 'font-weight': 'bold ' },
  footerMethod({ columns, data }) {
    return [
      columns.map((column, columnIndex) => {
        if (columnIndex === 0) {
          return `合计`;
        }
        if (columnIndex === 1) {
          return `${data.length} 项`;
        }
        if (['MoveQty'].includes(column.field)) {
          return sumNum(data, column.field);
        }
        return null;
      }),
    ];
  },
  height: 'auto',
  keepSource: true,
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    ajax: {
      query: async () => {
        return await getStockTakingItemDiff(props.id);
      },
    },
    seq: true,
  },
  showFooter: true,
  size: 'mini',
  sortConfig: {
    remote: false,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
  scrollX: { enabled: true, gt: 0 },
  scrollY: { enabled: true, mode: 'wheel', gt: 30, oSize: 0 },
  menuConfig: {
    trigger: 'cell',
    body: {
      options: [
        [
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuCopy',
            disabled: false,
            name: '复制单元格',
            prefixConfig: {
              icon: 'vxe-icon-copy',
            },
            visible: true,
          },
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuRefresh',
            disabled: false,
            name: '刷新',
            prefixConfig: { icon: 'vxe-icon-refresh' },
            visible: true,
          },
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuExport',
            disabled: false,
            name: '导出明细',
            prefixConfig: { icon: 'vxe-icon-download' },
            visible: true,
          },
        ],
      ],
    },
    className: 'font-medium shadow rounded-md',
  },
});
const gridEvents: VxeGridListeners = {
  /* cellDblclick({ row }) {
    openCSARDrawer(row.Guid);
  }, */
  menuClick({ menu }) {
    switch (menu.code) {
      case 'menuExport': {
        handleExport();
        break;
      }
      default: {
        break;
      }
    }
  },
};
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions, gridEvents });
function sumNum(list: any[], field: string) {
  let count = 0;
  list.forEach((item) => {
    count += Number(item[field]);
  });
  return count;
}
function handleExport() {
  gridApi.grid.exportData({
    filename: `盘点差异_${props.doc}`,
    sheetName: 'Sheet1',
    type: 'xlsx',
    isFooter: false,
    columnFilterMethod: ({ column }) => {
      return column.field !== undefined;
    },
  });
}
/* function handleSuccess() {
  gridApi.grid.commitProxy('query');
} */

/* function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
} */
</script>

<template>
  <div class="flex h-full flex-col">
    <Grid>
      <template #toolbar_left>
        <div class="hidden pl-1 text-base font-medium md:block">
          <span class="mr-2">差异明细</span>
        </div>
        <!-- <a-button class="mr-1" size="small" @click="clearFilterAndSort()">
          <AntClear class="size-5" />
        </a-button> -->
      </template>
      <template #toolbar-tools> </template>
      <template #material="{ row }">
        <a
          v-if="row.MatCode"
          class="text-blue-500"
          @click="openSkuDrawer(row.MatCode)"
        >
          {{ row.MatCode }}
        </a>
      </template>
      <template #batch="{ row }">
        <a
          v-if="row.BatchNo"
          class="text-blue-500"
          @click="openBatchDrawer(row.MatCode, row.BatchNo)"
        >
          {{ row.BatchNo }}
        </a>
      </template>
    </Grid>
    <SkuDrawer />
    <BatchDrawer />
    <VbenLoading :spinning="loadingRef" />
  </div>
</template>
