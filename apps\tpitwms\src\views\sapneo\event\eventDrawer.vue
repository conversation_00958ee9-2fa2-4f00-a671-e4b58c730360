<script lang="ts" setup>
import { ref } from 'vue';

import { CodeEditor, Fallback, useVbenDrawer } from '@vben/common-ui';

import { useClipboard } from '@vueuse/core';
import { Card, Descriptions, message } from 'ant-design-vue';

import { getEventData } from '#/api/sapneo';

const data = ref();
const eventData = ref({}) as any;
const isNotFound = ref(false);
const [Drawer, drawerApi] = useVbenDrawer({
  showConfirmButton: false,
  cancelText: 'Close',
  destroyOnClose: true,
  onCancel() {
    drawerApi.close();
  },
  onConfirm() {
    message.info('onConfirm');
    // drawerApi.close();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      data.value = drawerApi.getData<Record<string, any>>();
      handleGetData(data.value.id);
    }
  },
});
function handleGetData(id: string) {
  drawerApi.setState({ loading: true });
  getEventData(id)
    .then((res) => {
      isNotFound.value = false;
      eventData.value = res;
    })
    .catch(() => {
      isNotFound.value = true;
    })
    .finally(() => {
      drawerApi.setState({ loading: false });
    });
}
const { copy } = useClipboard({ legacy: true });
function handleCopy() {
  copy(eventData.value.Message);
  message.success('已复制到剪贴板！');
}
</script>
<template>
  <Drawer title="Event Data">
    <Fallback v-if="isNotFound" :show-back="false" status="404" />
    <Descriptions
      v-else
      :column="1"
      bordered
      size="small"
      style="padding-bottom: 8px"
    >
      <Descriptions.Item label="PartitionOffset">
        {{ `[${eventData.Partition}] @${eventData.OffSet}` }}
      </Descriptions.Item>
      <Descriptions.Item label="ReceiveDate">
        {{ eventData.ReceiveDate }}
      </Descriptions.Item>
    </Descriptions>
    <Card :can-expan="false" size="small">
      <template #title>
        {{ eventData.Topic }}
      </template>
      <template #extra><a @click="handleCopy">Copy</a></template>
      <div class="event-code">
        <CodeEditor v-model:value="eventData.Message" readonly />
      </div>
    </Card>
  </Drawer>
</template>

<style lang="scss">
.event-code .CodeMirror {
  height: auto !important;
}
</style>
