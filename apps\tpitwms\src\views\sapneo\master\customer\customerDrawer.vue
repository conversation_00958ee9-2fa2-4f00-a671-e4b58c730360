<script lang="ts" setup>
import type { VxeGridListeners, VxeGridProps } from '#/adapter';

import { reactive, ref } from 'vue';

import { Fallback, useVbenDrawer } from '@vben/common-ui';

import { Descriptions, RadioButton, RadioGroup } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import { getCustomerData } from '#/api/sapneo';

const data = ref();
const dataType = ref('info');
const customerData = ref({}) as any;
const isNotFound = ref(false);
const [Drawer, drawerApi] = useVbenDrawer({
  class: 'w-[560px]',
  showConfirmButton: false,
  cancelText: '关闭',
  destroyOnClose: true,
  zIndex: 900,
  onCancel() {
    drawerApi.close();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      isNotFound.value = false;
      dataType.value = 'info';
      data.value = drawerApi.getData<Record<string, any>>();
      handleGetData(data.value.id);
    }
  },
});
const contactGridOptions = reactive<VxeGridProps>({
  columns: [
    { type: 'seq', width: 50, fixed: 'left' },
    {
      field: 'bpGoldenIdCentralMdm',
      title: '联系人代码',
      width: 100,
    },
    {
      field: 'personFullName',
      title: '联系人全名',
      minWidth: 100,
    },
    {
      field: 'completeTelNumber',
      title: '联系电话',
      minWidth: 100,
    },
  ],
  customConfig: {
    storage: false,
  },
  menuConfig: {
    body: {
      options: [
        [
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuCopy',
            disabled: false,
            name: '复制单元格',
            prefixConfig: {
              icon: 'vxe-icon-copy',
            },
            visible: true,
          },
        ],
      ],
    },
    className: 'font-medium shadow rounded-md',
  },
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    enabled: false,
  },
  size: 'small',
  toolbarConfig: {
    enabled: false,
  },
});
const saGridOptions = reactive<VxeGridProps>({
  columns: [
    { type: 'seq', width: 48 },
    { type: 'expand', width: 48, slots: { content: 'expand_content' } },
    {
      field: 'customerCompanyCode',
      title: '公司',
      minWidth: 64,
    },
    {
      field: 'deliveringPlant',
      title: '工厂',
      width: 56,
    },
    {
      field: 'salesOrganization',
      title: '销售组织',
      width: 76,
    },
    {
      field: 'distributionChannel',
      title: '分销渠道',
      width: 76,
    },
    {
      field: 'division',
      title: 'Division',
      width: 76,
    },
    {
      field: 'defaultPartner',
      title: '默认伙伴',
      width: 76,
    },
  ],
  customConfig: {
    storage: false,
  },
  expandConfig: {
    accordion: true,
  },
  menuConfig: {
    body: {
      options: [
        [
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuCopy',
            disabled: false,
            name: '复制单元格',
            prefixConfig: {
              icon: 'vxe-icon-copy',
            },
            visible: true,
          },
        ],
      ],
    },
    className: 'font-medium shadow rounded-md',
  },
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    enabled: false,
  },
  size: 'small',
  toolbarConfig: {
    enabled: false,
  },
});

const pfGridOptions = reactive<VxeGridProps>({
  columns: [
    { type: 'seq', width: 48 },
    {
      field: 'partnerFunctionCode',
      title: '伙伴类型',
      width: 80,
    },
    {
      field: 'customer1',
      title: '代码1',
      minWidth: 120,
    },
    {
      field: 'customer2',
      title: '代码2',
      minWidth: 120,
    },
    {
      field: 'defaultPartner',
      title: '是否默认',
      width: 80,
    },
  ],
  customConfig: {
    storage: false,
  },
  menuConfig: {
    body: {
      options: [
        [
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuCopy',
            disabled: false,
            name: '复制单元格',
            prefixConfig: {
              icon: 'vxe-icon-copy',
            },
            visible: true,
          },
        ],
      ],
    },
    className: 'font-medium shadow rounded-md',
  },
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    enabled: false,
  },
  rowStyle({ row }) {
    if (row.defaultPartner) {
      return {
        backgroundColor: '#ebfbee',
        color: '#222',
      };
    }
  },
  size: 'mini',
  toolbarConfig: {
    enabled: false,
  },
});

const [ContactGrid, contactGridApi] = useVbenVxeGrid({
  gridOptions: contactGridOptions,
});
const saGridEvents: VxeGridListeners = {
  async toggleRowExpand({ expanded, row }) {
    if (expanded) {
      await handleSARow(row);
      await handlePFData(row.partnerFunctionJSON);
    }
  },
};
const [SAGrid, saGridApi] = useVbenVxeGrid({
  gridEvents: saGridEvents,
  gridOptions: saGridOptions,
});
const [PFGrid, pfGridApi] = useVbenVxeGrid({
  gridOptions: pfGridOptions,
});
function handleSARow(row: any) {
  saGridApi.grid.setCurrentRow(row);
}
function handlePFData(data: any) {
  pfGridApi.setGridOptions({
    data,
  });
}
function handleGetData(id: string) {
  drawerApi.setState({ loading: true });
  getCustomerData(id)
    .then((res) => {
      customerData.value = res.info;
      contactGridApi.grid.loadData(res.contact);
      saGridApi.grid.loadData(res.salesArea);
      isNotFound.value = false;
    })
    .catch(() => {
      isNotFound.value = true;
    })
    .finally(() => {
      drawerApi.setState({ loading: false });
    });
}
</script>
<template>
  <Drawer title="客户信息">
    <Fallback v-if="isNotFound" :show-back="false" status="404" />
    <div v-else>
      <RadioGroup
        v-model:value="dataType"
        button-style="solid"
        class="mb-2"
        @change="1"
      >
        <RadioButton value="info">基础信息</RadioButton>
        <RadioButton value="contact">联系方式</RadioButton>
        <RadioButton value="salesArea">销售地区</RadioButton>
      </RadioGroup>
      <Descriptions
        v-show="dataType === 'info'"
        :column="1"
        :label-style="{ width: '120px' }"
        bordered
        size="small"
      >
        <Descriptions.Item label="客户代码">
          {{ customerData.bpGoldenIdCentralMdm }}
        </Descriptions.Item>
        <Descriptions.Item label="外部系统代码">
          {{ customerData.bpNumberInExternalSystem }}
        </Descriptions.Item>
        <Descriptions.Item label="客户名称">
          {{ customerData.legalName1OfOrganization }}
        </Descriptions.Item>
        <Descriptions.Item label="类型">
          {{ customerData.customerType }}
        </Descriptions.Item>
        <Descriptions.Item label="账户组">
          {{ customerData.customerAccountGroup }}
        </Descriptions.Item>
        <Descriptions.Item label="分组">
          {{ customerData.bpGroupingCode }}
        </Descriptions.Item>
        <Descriptions.Item label="时区">
          {{ customerData.addressTimeZone }}
        </Descriptions.Item>
        <Descriptions.Item label="语言">
          {{ customerData.bpCorrespondenceLanguage }}
        </Descriptions.Item>
        <Descriptions.Item label="国家">
          {{ customerData.countryCode }}
        </Descriptions.Item>
        <Descriptions.Item label="地区">
          {{ customerData.region }}
        </Descriptions.Item>
        <Descriptions.Item label="区域">
          {{ customerData.district }}
        </Descriptions.Item>
        <Descriptions.Item label="城市">
          {{ customerData.city }}
        </Descriptions.Item>
        <Descriptions.Item label="邮编">
          {{ customerData.cityPostalCode }}
        </Descriptions.Item>
        <Descriptions.Item label="地址街道">
          {{
            `${customerData.street}  ${customerData.street2}  ${customerData.street3}  ${customerData.street4}  ${customerData.street5}`
          }}
        </Descriptions.Item>
        <Descriptions.Item label="地址电话">
          {{ customerData.completeTelNumber }}
        </Descriptions.Item>
        <Descriptions.Item label="创建日期">
          {{ customerData.CreateDate }}
        </Descriptions.Item>
        <Descriptions.Item label="更新日期">
          {{ customerData.UpdateDate }}
        </Descriptions.Item>
      </Descriptions>
      <ContactGrid v-show="dataType === 'contact'" />
      <SAGrid v-show="dataType === 'salesArea'">
        <template #expand_content>
          <div>
            <PFGrid />
          </div>
        </template>
      </SAGrid>
    </div>
  </Drawer>
</template>
