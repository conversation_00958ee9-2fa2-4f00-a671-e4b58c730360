import { requestClient, sleep } from '#/api/request';

/**
 * NEO API配置相关API
 */

export async function getNEOAPIList(type: string) {
  await sleep(100);
  return requestClient.get('/maintenance/interface/neoapi/getList', {
    params: { type },
  });
}

export async function getAPIData(id: string) {
  await sleep(100);
  return requestClient.get('/maintenance/interface/neoapi/getData', {
    params: { id },
  });
}

export async function apiUpdate(params: object) {
  await sleep(100);
  return requestClient.post('/maintenance/interface/neoapi/update', params);
}

export async function getHeadersList(id: string) {
  await sleep(100);
  return requestClient.get('/maintenance/interface/neoapi/headers/getList', {
    params: { id },
  });
}

export async function apiHeadersUpdate(params: object) {
  await sleep(100);
  return requestClient.post(
    '/maintenance/interface/neoapi/headers/update',
    params,
  );
}
export async function apiHeadersDelete(id: string) {
  await sleep(100);
  return requestClient.post('/maintenance/interface/neoapi/headers/del', {
    id,
  });
}

export async function getNEOAPIMappingNodes(id: string) {
  await sleep(100);
  return requestClient.get(
    '/maintenance/interface/neoapi/mapping/nodes/getList',
    {
      params: { id },
    },
  );
}

export async function getNEOAPIMappingFields(id: string) {
  await sleep(100);
  return requestClient.get(
    '/maintenance/interface/neoapi/mapping/fields/getList',
    {
      params: { id },
    },
  );
}

export async function ckeckNEOAPIID(id: string) {
  await sleep(100);
  return requestClient.get('/maintenance/interface/neoapi/check', {
    params: { id },
  });
}

export async function apiMappingNodesUpdate(params: object) {
  await sleep(100);
  return requestClient.post(
    '/maintenance/interface/neoapi/mapping/nodes/update',
    params,
  );
}

export async function apiNodesDelete(id: string) {
  await sleep(100);
  return requestClient.post('/maintenance/interface/neoapi/mapping/nodes/del', {
    id,
  });
}

export async function apiMappingFiledsUpdate(params: object) {
  await sleep(100);
  return requestClient.post(
    '/maintenance/interface/neoapi/mapping/fileds/update',
    params,
  );
}

export async function apiFieldsDelete(id: string) {
  await sleep(100);
  return requestClient.post(
    '/maintenance/interface/neoapi/mapping/fileds/del',
    {
      id,
    },
  );
}
