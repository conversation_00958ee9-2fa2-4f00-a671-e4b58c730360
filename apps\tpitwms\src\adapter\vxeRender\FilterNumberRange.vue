<script lang="ts" setup>
import type { PropType } from 'vue';

import type {
  VxeGlobalRendererHandles,
  VxeTableDefines,
} from '@vben/plugins/vxe-table';

import { ref, watch } from 'vue';

import { InputNumber, RadioButton, RadioGroup } from 'ant-design-vue';

const props = defineProps({
  params: {
    default: () => ({}),
    type: Object as PropType<VxeGlobalRendererHandles.RenderTableFilterParams>,
  },
});
const currOption = ref<VxeTableDefines.FilterOption>();

const load = () => {
  const { params } = props;
  if (params) {
    const { column } = params;
    const option = column.filters[0];
    currOption.value = option;
  }
};

const changeOptionEvent = () => {
  const { params } = props;
  const option = currOption.value;
  if (params && option) {
    option.value = JSON.stringify(option.data);
    const { $panel } = params;
    const checked = !(
      option?.data.type === '0' &&
      (option?.data.num1 === undefined ||
        option?.data.num1 === null ||
        option?.data.num2 === undefined ||
        option?.data.num2 === null)
    );
    $panel.changeOption(null, checked, option);
  }
};

watch(() => {
  const { column } = props.params || {};
  return column;
}, load);

load();
</script>

<template>
  <div v-if="currOption" class="my-filter-number">
    <div style="padding-bottom: 8px">
      <RadioGroup
        v-model:value="currOption.data.type"
        button-style="solid"
        size="small"
        @change="changeOptionEvent"
      >
        <RadioButton value="0">数值范围</RadioButton>
        <RadioButton value="1">非零值</RadioButton>
        <RadioButton value="2">零值</RadioButton>
      </RadioGroup>
    </div>
    <!-- <RangePicker
      v-model:value="currOption.data.date"
      :disabled="currOption.data.type === '1'"
      :get-popup-container="(triggerNode: any) => triggerNode.parentNode"
      value-format="YYYY-MM-DD"
      @change="onRangeChange"
    /> -->
    <InputNumber
      size="small"
      v-model:value="currOption.data.num1"
      :disabled="currOption.data.type !== '0'"
      :min="0"
      @change="changeOptionEvent"
    />
    -
    <InputNumber
      size="small"
      v-model:value="currOption.data.num2"
      :disabled="currOption.data.type !== '0'"
      :min="0"
      @change="changeOptionEvent"
    />
    <!-- <div class="my-fc-footer">
      <a-button
        :disabled="
          currOption?.data.type === '0' && currOption?.data.date.length === 0
        "
        size="mini"
        type="text"
        @click="confirmFilterEvent"
      >
        筛选
      </a-button>
      <a-button size="mini" type="text" @click="resetFilterEvent">
        重置
      </a-button>
    </div> -->
  </div>
</template>

<style scoped>
.my-filter-number {
  padding: 10px;
}

.my-fc-footer {
  padding-top: 8px;
  text-align: left;
}

.my-fc-footer button {
  padding: 0 8px 0 0;
  margin-left: 2px;
}
</style>
