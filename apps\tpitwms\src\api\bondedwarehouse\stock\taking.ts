import { requestClient, sleep } from '#/api/request';

/**
 * 库存管理 库存盘点相关API
 */
export async function getStockTakingList(obj: object) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/stock/taking/getList', {
    params: obj,
  });
}

export async function stockTakingGetDoc() {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/stock/taking/getDoc');
}

export async function stockTakingCreate(params: object) {
  await sleep(100);
  return requestClient.post('/bondedwarehouse/stock/taking/create', params);
}

export async function stockTakingDelete(id: string) {
  await sleep(100);
  return requestClient.post('/bondedwarehouse/stock/taking/del', { id });
}

export async function getStockTakingHead(id: string) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/stock/taking/head/getData', {
    params: { id },
  });
}

export async function stockTakingHeadUpdate(params: object) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/stock/taking/head/update',
    params,
  );
}

export async function stockTakingItemImport(id: string, ids: Array<string>) {
  await sleep(100);
  return requestClient.post('/bondedwarehouse/stock/taking/item/import', {
    id,
    ids,
  });
}

export async function stockTakingMissingItemImport(
  id: string,
  ids: Array<string>,
) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/stock/taking/missing/item/import',
    {
      id,
      ids,
    },
  );
}

export async function getStockTakingItem1st(id: string) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/stock/taking/item/1st/getList', {
    params: { id },
  });
}

export async function getStockTakingItem2st(id: string) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/stock/taking/item/2st/getList', {
    params: { id },
  });
}

export async function getStockTakingItemDiff(id: string) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/stock/taking/item/diff/getList', {
    params: { id },
  });
}

export async function stockTakingItem1stDelete(id: string, ids: Array<string>) {
  await sleep(100);
  return requestClient.post('/bondedwarehouse/stock/taking/item/1st/del', {
    id,
    ids,
  });
}

export async function stockTakingItem1stUpdate(params: object) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/stock/taking/item/1st/update',
    params,
  );
}

export async function stockTakingItem1stCancel(id: string, ids: Array<string>) {
  await sleep(100);
  return requestClient.post('/bondedwarehouse/stock/taking/item/1st/cancel', {
    id,
    ids,
  });
}

export async function stockTakingItem2stUpdate(params: object) {
  await sleep(100);
  return requestClient.post(
    '/bondedwarehouse/stock/taking/item/2st/update',
    params,
  );
}

export async function stockTakingItem2stCancel(id: string, ids: Array<string>) {
  await sleep(100);
  return requestClient.post('/bondedwarehouse/stock/taking/item/2st/cancel', {
    id,
    ids,
  });
}

export async function stockTakingReviewCheck(id: string) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/stock/taking/review/check', {
    params: { id },
  });
}

export async function stockTakingClose(id: string) {
  await sleep(100);
  return requestClient.post('/bondedwarehouse/stock/taking/close', { id });
}
