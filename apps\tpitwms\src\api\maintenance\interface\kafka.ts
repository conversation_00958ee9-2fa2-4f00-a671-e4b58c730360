import { requestClient, sleep } from '#/api/request';

/**
 * Kafka Consumer配置相关API
 */

export async function getConsumerData() {
  await sleep(100);
  return requestClient.get('/maintenance/interface/kafkaconsumer/getData');
}
export async function getTopicList() {
  await sleep(100);
  return requestClient.get(
    '/maintenance/interface/kafkaconsumer/topic/getList',
  );
}

export async function topicDelete(id: string) {
  await sleep(100);
  return requestClient.post('/maintenance/interface/kafkaconsumer/topic/del', {
    id,
  });
}

export async function topicUpdate(params: object) {
  await sleep(100);
  return requestClient.post(
    '/maintenance/interface/kafkaconsumer/topic/update',
    params,
  );
}

export async function consumerUpdate(params: object) {
  await sleep(100);
  return requestClient.post(
    '/maintenance/interface/kafkaconsumer/update',
    params,
  );
}

export async function consumerStatusChange(typ: boolean) {
  await sleep(100);
  return requestClient.post(
    '/maintenance/interface/kafkaconsumer/status/change',
    {
      typ,
    },
  );
}
