<script lang="ts" setup>
import { onMounted, ref } from 'vue';

import { EllipsisText, Page, useVbenDrawer, VbenButton } from '@vben/common-ui';
import { AntBars } from '@vben/icons';

import { Card, Col, List, ListItem, Row, Tag } from 'ant-design-vue';

import { getOptions } from '#/api/common';

import neoApiDrawer from './neoApiDrawer.vue';

const apiList = ref([]) as any;

const [NeoApiDrawer, neoApiDrawerApi] = useVbenDrawer({
  // 连接抽离的组件
  connectedComponent: neoApiDrawer,
});
function openNEOAPIDrawer(id: string) {
  neoApiDrawerApi.setData({ id });
  neoApiDrawerApi.open();
}

async function fetch() {
  apiList.value = await getOptions('NEOAPIList');
}
onMounted(() => {
  fetch();
});
</script>

<template>
  <Page
    description="用于特殊情况下添加或更新接口数据"
    title="NEO API 手动调用任务"
  >
    <template #extra>
      <VbenButton v-if="false" class="mr-5" variant="outline" @click="1">
        <AntBars class="mr-2 size-4" />
        任务记录
      </VbenButton>
    </template>
    <List>
      <Row :gutter="8">
        <template v-for="item in apiList" :key="item.id">
          <Col :lg="8" :md="12" :sm="12" :xl="8" :xs="24" :xxl="6">
            <ListItem>
              <Card :hoverable="true" :title="item.name" class="w-full">
                <template #extra>
                  <Tag :color="item.method === 'GET' ? 'green' : 'blue'">
                    {{ item.method }}
                  </Tag>
                </template>
                <div>
                  <EllipsisText :line="2" :tooltip="false">
                    {{ item.url }}
                  </EllipsisText>
                  <div class="float-right mt-2">
                    <a-button type="primary" @click="openNEOAPIDrawer(item.id)">
                      调用API
                    </a-button>
                  </div>
                </div>
              </Card>
            </ListItem>
          </Col>
        </template>
      </Row>
    </List>
    <NeoApiDrawer />
  </Page>
</template>
