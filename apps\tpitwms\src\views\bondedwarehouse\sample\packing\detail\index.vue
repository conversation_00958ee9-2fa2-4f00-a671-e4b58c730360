<script lang="ts" setup>
import { onActivated, onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';

import { Page } from '@vben/common-ui';
import { useTabs } from '@vben/hooks';
import { AntCaretDown, AntCaretUp } from '@vben/icons';
import { useTabbarStore } from '@vben/stores';
import { downloadFileFromBlob } from '@vben/utils';

import { Card, message, TabPane, Tabs } from 'ant-design-vue';

import { useVbenForm } from '#/adapter';
import {
  bwOutSamplePackingHeadUpdate,
  checkSamplePacking,
  getOutSamplePackingHead,
} from '#/api/bondedwarehouse/outbound';
import { createFile } from '#/api/common';

import TabItem from './tabItem.vue';

const headData = ref({}) as any;
const loadingRef = ref(false);
const submitLoading = ref(false);
const init = ref(false);
const showHead = ref(true);
const activeKey = ref('Item');
const route = useRoute();
const { setTabTitle } = useTabs();
const id = route.params?.id?.toString() ?? '';

const [Form, baseFormApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'pb-1',
    labelClass: 'w-24',
  },
  // 提交函数
  handleSubmit: onSubmit,
  layout: 'horizontal',
  schema: [
    {
      component: 'Switch',
      componentProps: {},
      fieldName: 'PickingStatus',
      label: '拣货状态',
      dependencies: {
        show: false,
        triggerFields: ['A'],
      },
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'SampleBoxNo',
      label: '装箱流水号',
      disabled: true,
    },
    {
      component: 'DatePicker',
      componentProps: {
        style: { width: '100%' },
        showTime: { format: 'HH:mm:00' },
        format: 'YYYY-MM-DD HH:mm:00',
        valueFormat: 'YYYY-MM-DD HH:mm:00',
      },
      fieldName: 'PackingDate',
      label: '装箱日期',
    },
    {
      component: 'Input',
      componentProps: {
        maxlength: 128,
        autocomplete: 'off',
      },
      fieldName: 'Remark',
      label: '备注',
      formItemClass: 'col-span-1 md:col-span-2',
    },
  ],
  showDefaultActions: false,
  wrapperClass: 'grid-cols-1 md:grid-cols-4',
});
async function onSubmit(values: Record<string, any>) {
  const check = await baseFormApi.validate();
  if (check.valid) {
    submitLoading.value = true;
    bwOutSamplePackingHeadUpdate(Object.assign({ id }, values))
      .then(() => {
        message.success('操作成功');
      })
      .catch(() => {
        baseFormApi.setValues(headData.value);
      })
      .finally(() => {
        setTimeout(() => {
          fetch();
          submitLoading.value = false;
        }, 500);
      });
  } else {
    message.info('必填项未填写完整，请检查！');
  }
}
async function handleExport() {
  await checkSamplePacking(id)
    .then(() => {
      loadingRef.value = true;
      createFile('SampleLabel', id)
        .then((res) => {
          downloadFileFromBlob({
            source: res.data,
            fileName: `留样箱唛标签_${headData.value.SampleBoxNo}.pdf`,
          });
        })
        .catch(() => {
          /* message.error(`生成文件错误！`); */
        })
        .finally(() => {
          loadingRef.value = false;
        });
    })
    .catch(() => {})
    .finally(() => {});
}
async function handleGetLabel() {
  submitLoading.value = true;
  createFile('SampleProductLabelInBox', id)
    .then((res) => {
      downloadFileFromBlob({
        source: res.data,
        fileName: `留样产品标签_${headData.value.SampleBoxNo}.pdf`,
      });
    })
    .catch(() => {
      /* message.error(`生成文件错误！`); */
    })
    .finally(() => {
      submitLoading.value = false;
    });
}
async function fetch() {
  const res = await getOutSamplePackingHead(id);
  headData.value = res.head;

  const tabbarStore = useTabbarStore();
  const currentTab = tabbarStore.getTabs.find(
    (item) => item.path === route.path,
  );
  if (currentTab?.meta.title === '留样装箱操作') {
    setTabTitle(`留样装箱操作-${res.doc}`);
  }
  baseFormApi.setValues(headData.value);
  init.value = false;
}
onMounted(() => {
  init.value = true;
  showHead.value = true;
  fetch();
});
onActivated(() => {
  const tabbarStore = useTabbarStore();
  const currentTab = tabbarStore.getTabs.find(
    (item) => item.path === route.path,
  );
  if (!currentTab?.meta.newTabTitle && !init.value) {
    fetch();
  }
});
</script>

<template>
  <Page auto-content-height v-loading="loadingRef">
    <div class="flex h-full flex-col">
      <Card :body-style="{ padding: '4px' }" :bordered="false" size="small">
        <template #title>
          <!-- <div class="hidden md:block"> -->
          {{ `留样装箱表头` }}
          <!-- </div> -->
        </template>
        <!--  v-if="headData.CreateBy !== 'System'" -->
        <template #extra>
          <div class="flex">
            <a-button class="mr-2" @click="handleExport()">箱唛标签</a-button>
            <a-button class="mr-2" @click="handleGetLabel()">产品标签</a-button>
            <a-button
              :loading="submitLoading"
              class="mr-2"
              type="primary"
              @click="baseFormApi.submitForm()"
            >
              保存
            </a-button>

            <AntCaretDown
              v-if="showHead"
              class="size-6"
              @click="showHead = !showHead"
            />
            <AntCaretUp v-else class="size-6" @click="showHead = !showHead" />
          </div>
        </template>
        <Form v-show="showHead" />
      </Card>
      <div class="inorder-tab min-h-0 flex-1">
        <Tabs v-model:active-key="activeKey" class="h-full" tab-position="left">
          <TabPane key="Item" tab="装箱">
            <div v-if="activeKey === 'Item'" class="h-full">
              <TabItem
                :id="id"
                :doc="headData.SampleBoxNo"
                :is-closed="headData.PickingStatus"
              />
            </div>
          </TabPane>
        </Tabs>
      </div>
    </div>
  </Page>
</template>

<style scoped lang="less">
::v-deep(.inorder-tab) {
  background-color: #fff;

  .ant-tabs-tabpane .ant-tabs-tabpane-active {
    padding-left: 0 !important;
  }

  .ant-tabs-left > .ant-tabs-nav .ant-tabs-tab {
    padding: 8px 12px;
  }

  .ant-tabs-tab-active {
    background-color: #e7f5ff;
  }

  .ant-tabs-content-holder > .ant-tabs-content > .ant-tabs-tabpane {
    padding-left: 0;
  }
  .ant-tabs-content-left {
    height: 100%;
  }
}
</style>
