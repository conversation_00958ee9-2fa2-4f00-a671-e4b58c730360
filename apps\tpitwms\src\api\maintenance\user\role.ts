import { requestClient, sleep } from '#/api/request';

/**
 * 系统设置 角色相关API
 */
export async function getRoleList(obj: object) {
  await sleep(100);
  return requestClient.get('/maintenance/user/role/getList', { params: obj });
}

export async function getRoleData(id: string) {
  await sleep(100);
  return requestClient.get('/maintenance/user/role/getData', {
    params: { id },
  });
}

export async function roleUpdate(params: object) {
  await sleep(100);
  return requestClient.post('/maintenance/user/role/update', params);
}

export async function roleDelete(id: string) {
  await sleep(100);
  return requestClient.post('/maintenance/user/role/del', { id });
}
