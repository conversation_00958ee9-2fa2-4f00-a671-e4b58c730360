<script lang="ts" setup>
import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter';

defineOptions({
  name: 'TopicModal',
});

function onSubmit(values: Record<string, any>) {
  message.info(JSON.stringify(values)); // 只会执行一次
}

const [Form, formApi] = useVbenForm({
  handleSubmit: onSubmit,
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  schema: [
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 48,
      },
      fieldName: 'TopicName',
      label: 'Topic Name',
      rules: 'required',
    },
    {
      component: 'Select',
      componentProps: {
        mode: 'tags',
        maxTagCount: 2,
        tokenSeparators: [',', '，'],
      },
      fieldName: 'KeyNode',
      label: 'Data Key Node',
    },
    {
      component: 'Select',
      componentProps: {},
      fieldName: 'APIID',
      label: 'Binding API',
    },
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 255,
      },
      fieldName: 'Filter',
      label: 'Local Filter',
    },
  ],
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  fullscreenButton: false,
  closeOnClickModal: false,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await formApi.submitForm();
    modalApi.close();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      const { values } = modalApi.getData<Record<string, any>>();
      if (values) {
        formApi.setValues(values);
      }
    }
  },
  title: 'Add New Topic',
});
</script>
<template>
  <Modal>
    <Form />
  </Modal>
</template>
