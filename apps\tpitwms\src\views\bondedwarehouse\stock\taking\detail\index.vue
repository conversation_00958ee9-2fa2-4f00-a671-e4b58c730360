<script lang="ts" setup>
import { onActivated, onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';

import { Page, useVbenModal, VbenButton, VbenLoading } from '@vben/common-ui';
import { useTabs } from '@vben/hooks';
import { AntCaretDown, AntCaretUp } from '@vben/icons';
import { useTabbarStore } from '@vben/stores';

import { Card, message, TabPane, Tabs } from 'ant-design-vue';

import { useVbenForm } from '#/adapter';
import {
  getStockTakingHead,
  stockTakingHeadUpdate,
} from '#/api/bondedwarehouse/stock';

import docCloseModal from './docCloseModal.vue';
import TabTaking1st from './tabTaking1st.vue';
import TabTaking2st from './tabTaking2st.vue';
import TabTakingDiff from './tabTakingDiff.vue';

const headData = ref({}) as any;
const loadingRef = ref(false);
const submitLoading = ref(false);
const init = ref(false);
const showHead = ref(true);
const activeKey = ref('1st');
const route = useRoute();
const { setTabTitle } = useTabs();
const id = route.params?.id?.toString() ?? '';

const [Form, baseFormApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'pb-1',
    labelClass: 'w-24',
  },
  // 提交函数
  handleSubmit: onSubmit,
  layout: 'horizontal',
  schema: [
    /* {
      component: 'Switch',
      componentProps: {},
      fieldName: 'IsClosed',
      label: '发货状态',
      dependencies: {
        show: false,
        triggerFields: ['A'],
      },
    }, */
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'TakingNo',
      label: '作业单号',
      disabled: true,
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'WarehouseName',
      label: '仓库',
      disabled: true,
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'OwnerShortName',
      label: '货主',
      disabled: true,
    },
    {
      component: 'DatePicker',
      componentProps: {
        style: { width: '100%' },
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
      },
      fieldName: 'PreTakingDate',
      label: '计划盘点日期',
      disabled: true,
    },
    {
      component: 'Switch',
      componentProps: {
        class: 'w-auto',
      },
      fieldName: 'IsBright',
      label: '是否明盘',
      disabled: true,
    },
    {
      component: 'Input',
      componentProps: {
        maxlength: 128,
        autocomplete: 'off',
      },
      fieldName: 'Remark',
      label: '备注',
      formItemClass: 'col-span-1 md:col-span-3 xl:col-span-5',
    },
  ],
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2 md:grid-cols-3 xl:grid-cols-5',
});

const [DocCloseModal, DocCloseModalApi] = useVbenModal({
  connectedComponent: docCloseModal,
});
function openDocCloseModal() {
  DocCloseModalApi.setData({
    id,
  });
  DocCloseModalApi.open();
}
async function onSubmit(values: Record<string, any>) {
  const check = await baseFormApi.validate();
  if (check.valid) {
    submitLoading.value = true;
    stockTakingHeadUpdate(Object.assign({ id }, values))
      .then(() => {
        message.success('操作成功');
      })
      .catch(() => {
        /* baseFormApi.setValues(headData.value); */
      })
      .finally(() => {
        setTimeout(() => {
          fetch();
          submitLoading.value = false;
        }, 500);
      });
  } else {
    message.info('必填项未填写完整，请检查！');
  }
}
async function fetch() {
  const res = await getStockTakingHead(id);
  headData.value = res;
  const tabbarStore = useTabbarStore();
  const currentTab = tabbarStore.getTabs.find(
    (item) => item.path === route.path,
  );
  if (currentTab?.meta.title === '盘点作业') {
    setTabTitle(`盘点作业-${res.TakingNo}`);
  }
  baseFormApi.setValues(headData.value);
  init.value = false;
}
onMounted(() => {
  init.value = true;
  showHead.value = true;
  fetch();
});
onActivated(() => {
  const tabbarStore = useTabbarStore();
  const currentTab = tabbarStore.getTabs.find(
    (item) => item.path === route.path,
  );
  if (!currentTab?.meta.newTabTitle && !init.value) {
    fetch();
  }
});
</script>

<template>
  <Page auto-content-height>
    <VbenLoading :spinning="loadingRef" />
    <div class="flex h-full flex-col">
      <Card :body-style="{ padding: '4px' }" :bordered="false" size="small">
        <template #title>
          <!-- <div class="hidden md:block"> -->
          {{ `盘点作业表头` }}
          <!-- </div> -->
        </template>
        <!--  v-if="headData.CreateBy !== 'System'" -->
        <template #extra>
          <div class="flex">
            <a-button
              :loading="submitLoading"
              class="mr-2"
              type="primary"
              @click="baseFormApi.submitForm()"
            >
              保存
            </a-button>
            <VbenButton
              :disabled="headData.IsClosed"
              class="mr-2"
              size="sm"
              variant="info"
              @click="openDocCloseModal()"
            >
              审核/关单
            </VbenButton>
            <AntCaretDown
              v-if="showHead"
              class="size-6"
              @click="showHead = !showHead"
            />
            <AntCaretUp v-else class="size-6" @click="showHead = !showHead" />
          </div>
        </template>
        <Form v-show="showHead" />
      </Card>
      <div class="inorder-tab min-h-0 flex-1">
        <Tabs v-model:active-key="activeKey" class="h-full" tab-position="left">
          <TabPane key="1st" tab="初盘">
            <div v-if="activeKey === '1st'" class="h-full">
              <TabTaking1st
                :id="id"
                :doc="headData.TakingNo"
                :warehouse="headData.WarehouseCode"
                :owner="headData.OwnerCode"
                :is-closed="headData.IsClosed"
                :is-bright="headData.IsBright"
              />
            </div>
          </TabPane>
          <TabPane key="2st" tab="复盘">
            <div v-if="activeKey === '2st'" class="h-full">
              <TabTaking2st
                :id="id"
                :doc="headData.TakingNo"
                :warehouse="headData.WarehouseCode"
                :owner="headData.OwnerCode"
                :is-closed="headData.IsClosed"
              />
            </div>
          </TabPane>
          <TabPane key="diff" tab="差异">
            <div v-if="activeKey === 'diff'" class="h-full">
              <TabTakingDiff
                :id="id"
                :doc="headData.TakingNo"
                :warehouse="headData.WarehouseCode"
                :owner="headData.OwnerCode"
                :is-closed="headData.IsClosed"
              />
            </div>
          </TabPane>
        </Tabs>
      </div>
    </div>
    <DocCloseModal @success="fetch()" />
  </Page>
</template>

<style scoped lang="less">
::v-deep(.inorder-tab) {
  background-color: #fff;

  .ant-tabs-tabpane .ant-tabs-tabpane-active {
    padding-left: 0 !important;
  }

  .ant-tabs-left > .ant-tabs-nav .ant-tabs-tab {
    padding: 8px 12px;
  }

  .ant-tabs-tab-active {
    background-color: #e7f5ff;
  }

  .ant-tabs-content-holder > .ant-tabs-content > .ant-tabs-tabpane {
    padding-left: 0;
  }
  .ant-tabs-content-left {
    height: 100%;
  }
}
</style>
