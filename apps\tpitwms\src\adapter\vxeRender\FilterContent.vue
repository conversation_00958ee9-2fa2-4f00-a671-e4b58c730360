<script lang="ts" setup>
import type { PropType } from 'vue';

import type {
  VxeGlobalRendererHandles,
  VxeTableDefines,
} from '@vben/plugins/vxe-table';

import { computed, ref, watch } from 'vue';

import { Checkbox } from 'ant-design-vue';
import XEUtils from 'xe-utils';

const props = defineProps({
  renderParams: {
    default: () => ({}),
    type: Object as PropType<VxeGlobalRendererHandles.RenderTableFilterParams>,
  },
});

interface ColValItem {
  checked: boolean;
  value: string;
}

const currOption = ref<VxeTableDefines.FilterOption>();
const isCheckedAll = ref(false);
const allValList = ref<ColValItem[]>([]);
const columnValList = ref<ColValItem[]>([]);

const currField = computed(() => {
  const { column } = props.renderParams || {};
  return column ? column.field : '';
});

const load = () => {
  const { renderParams } = props;
  if (renderParams) {
    const { $table, column } = renderParams;
    const fullData = $table.getFullData();
    const option = column.filters?.[0];
    const vals = option?.data?.vals ?? [];
    const colValList = Object.keys(XEUtils.groupBy(fullData, column.field))
      .sort()
      .map((val) => {
        return {
          checked: vals.includes(val),
          value: val,
        };
      });
    currOption.value = option;
    allValList.value = colValList;
    columnValList.value = colValList;
  }
};

const searchEvent = () => {
  const option = currOption.value;
  if (option) {
    const searchValue = option.data.sVal?.toLowerCase() || '';
    // 清除所有项的选中状态
    allValList.value.forEach((item) => {
      item.checked = false;
    });
    isCheckedAll.value = false;
    // 过滤列表并保持排序
    columnValList.value = allValList.value
      .filter((item) => item.value.toLowerCase().includes(searchValue))
      .sort((a, b) => a.value.localeCompare(b.value));
  }
};

const changeAllEvent = () => {
  // 只修改当前显示的列表项的选中状态
  columnValList.value.forEach((item) => {
    item.checked = isCheckedAll.value;
  });
  // 同步更新allValList中对应项的选中状态
  allValList.value.forEach((item) => {
    const foundItem = columnValList.value.find(
      (colItem) => colItem.value === item.value,
    );
    if (foundItem) {
      item.checked = foundItem.checked;
    }
  });
};

const confirmFilterEvent = () => {
  const { renderParams } = props;
  const option = currOption.value;
  if (renderParams && option) {
    const { data } = option;
    const { $table } = renderParams;
    data.vals = columnValList.value
      .filter((item) => item.checked)
      .map((item) => item.value);
    $table.updateFilterOptionStatus(option, true);
    $table.saveFilterPanel();
  }
};

const resetFilterEvent = () => {
  const { renderParams } = props;
  if (renderParams) {
    const { $table } = renderParams;
    // 重置所有选项的选中状态
    allValList.value.forEach((item) => {
      item.checked = false;
    });
    columnValList.value = [...allValList.value];
    isCheckedAll.value = false;
    // 重置搜索值
    if (currOption.value) {
      currOption.value.data.sVal = '';
    }
    $table.resetFilterPanel();
  }
};

const handleItemCheckChange = () => {
  // 检查是否所有显示的项都被选中
  const allChecked = columnValList.value.every((item) => item.checked);
  isCheckedAll.value = allChecked;
};

watch(currField, () => {
  load();
});

load();
</script>

<template>
  <div v-if="currOption" class="my-filter-content">
    <div class="my-fc-search">
      <div class="my-fc-search-top">
        <a-input
          v-model:value="currOption.data.sVal"
          placeholder="搜索"
          allow-clear
          @input="searchEvent"
        />
      </div>
      <div class="my-fc-search-content">
        <template v-if="columnValList.length > 0">
          <ul class="my-fc-search-list my-fc-search-list-head">
            <li class="my-fc-search-item">
              <Checkbox v-model:checked="isCheckedAll" @change="changeAllEvent">
                全选
              </Checkbox>
            </li>
          </ul>
          <ul class="my-fc-search-list my-fc-search-list-body">
            <li
              class="my-fc-search-item"
              v-for="(item, sIndex) in columnValList"
              :key="sIndex"
            >
              <Checkbox
                v-model:checked="item.checked"
                @change="handleItemCheckChange"
              >
                {{ item.value }}
              </Checkbox>
            </li>
          </ul>
        </template>
        <template v-else>
          <div class="my-fc-search-empty">无匹配项</div>
        </template>
      </div>
    </div>
    <div class="my-fc-footer">
      <a-button size="small" @click="resetFilterEvent">重置</a-button>
      <a-button type="primary" size="small" @click="confirmFilterEvent">
        确认
      </a-button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.my-filter-content {
  padding: 8px;
  user-select: none;

  .my-fc-search {
    .my-fc-search-top {
      position: relative;
      padding: 2px 0;
    }

    .my-fc-search-content {
      padding: 2px 8px;
      background-color: #fff;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
    }

    .my-fc-search-empty {
      padding: 12px 0;
      color: rgb(0 0 0 / 45%);
      text-align: center;
    }

    .my-fc-search-list {
      padding: 0;
      margin: 0;
      list-style: none;

      .my-fc-search-item {
        display: block;
        padding: 2px 0;
        line-height: 20px;

        &:hover {
          background-color: #f5f5f5;
        }
      }
    }

    .my-fc-search-list-body {
      height: 180px;
      margin-top: 2px;
      overflow: auto;
    }
  }

  .my-fc-footer {
    padding-top: 8px;
    margin-top: 6px;
    text-align: right;
    border-top: 1px solid #f0f0f0;

    button {
      margin-left: 6px;
    }
  }
}
</style>
