<script lang="ts" setup>
import type { VxeGridProps } from '#/adapter';

import { reactive, ref } from 'vue';

import { useVbenModal, VbenLoading } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import {
  getStockTakingItemDiff,
  stockTakingClose,
  stockTakingReviewCheck,
} from '#/api/bondedwarehouse/stock';

const emit = defineEmits(['success']);

const data = ref();
const loadingRef = ref(false);

const [BaseModal, modalApi] = useVbenModal({
  class: 'w-full h-full',
  fullscreenButton: false,
  closeOnClickModal: false,
  confirmText: '确认关闭',
  zIndex: 900,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await handleCloseDoc();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      loadingRef.value = true;
      modalApi.setState({ showConfirmButton: false });
      data.value = modalApi.getData<Record<string, any>>();
      fetch();
    }
  },
  title: '盘点作业_审核关单',
});
async function handleCloseDoc() {
  modalApi.setState({ confirmLoading: true });
  modalApi.setState({ loading: true });
  stockTakingClose(data.value.id)
    .then(() => {
      message.success('操作成功！');
      handleClose();
    })
    .catch(() => {})
    .finally(() => {
      modalApi.setState({ loading: false });
      modalApi.setState({ confirmLoading: false });
    });
}
const gridOptions = reactive<VxeGridProps>({
  id: 'BWStockTakingDiff',
  checkboxConfig: { highlight: true, range: true },
  columns: [
    {
      fixed: 'left',
      title: '#',
      type: 'seq',
      width: 60,
    },
    { field: 'AreaName', title: '库区', width: 80 },
    { field: 'BIN', title: '货位', width: 96 },
    { field: 'StockStatus', title: '库存状态', width: 80 },
    { field: 'HBL', title: 'HBL', width: 136 },
    { field: 'PalletNo', title: '托盘号', width: 96 },
    {
      field: 'MatCode',
      title: '物料编号',
      width: 80,
    },
    {
      field: 'BatchNo',
      title: '批号',
      width: 80,
    },
    {
      field: 'StorageLocation',
      title: 'SAP位置',
      width: 96,
    },
    { field: 'UnrestrictedQty', title: '账面非限制', minWidth: 84 },
    { field: 'ActualUnrestrictedQty', title: '实际非限制', minWidth: 84 },
    { field: 'DiffUnrestricted', title: '非限制差异', minWidth: 84 },
    { field: 'BlockedQty', title: '账面冻结', minWidth: 72 },
    { field: 'ActualBlockedQty', title: '实际冻结', minWidth: 72 },
    { field: 'DiffBlocked', title: '冻结差异', minWidth: 72 },
    { field: 'TakingBy1st', title: '复盘人', width: 72 },
    {
      field: 'TakingDate1st',
      title: '复盘日期',
      width: 88,
      formatter: 'formatDate',
    },
  ],
  filterConfig: {
    remote: false,
  },
  height: 'auto',
  keepSource: true,
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    ajax: {
      query: async () => {
        return await getStockTakingItemDiff(data.value.id);
      },
    },
    seq: true,
  },
  showFooter: true,
  footerMethod({ columns, data }) {
    return [
      columns.map((column, columnIndex) => {
        if (columnIndex === 0) {
          return `合计`;
        }
        if (columnIndex === 1) {
          return `${data.length} 项`;
        }
        if (
          [
            'ActualBlockedQty',
            'ActualUnrestrictedQty',
            'BlockedQty',
            'DiffBlocked',
            'DiffUnrestricted',
            'UnrestrictedQty',
          ].includes(column.field)
        ) {
          return sumNum(data, column.field);
        }
        return null;
      }),
    ];
  },
  size: 'mini',
  sortConfig: {
    remote: false,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
  scrollX: { enabled: true, gt: 0 },
  scrollY: { enabled: true, mode: 'wheel', gt: 30, oSize: 0 },
});
const [Grid] = useVbenVxeGrid({ gridOptions });
function sumNum(list: any[], field: string) {
  let count = 0;
  list.forEach((item) => {
    count += Number(item[field]);
  });
  return count;
}
function handleClose() {
  modalApi.close();
  emit('success');
}
async function fetch() {
  await stockTakingReviewCheck(data.value.id)
    .then((res) => {
      if (res.IsClosed) {
        message.error('作业单据已关闭，请刷新查看！');
      } else {
        modalApi.setState({ showConfirmButton: true });
      }
    })
    .catch(() => {})
    .finally(() => {
      loadingRef.value = false;
    });
}
</script>
<template>
  <BaseModal>
    <Grid>
      <template #toolbar_left>
        <div class="hidden pl-1 text-base font-medium md:block">
          <span class="mr-2">差异记录</span>
        </div>
      </template>
      <template #toolbar-tools> </template>
    </Grid>
    <VbenLoading :spinning="loadingRef" />
  </BaseModal>
</template>
