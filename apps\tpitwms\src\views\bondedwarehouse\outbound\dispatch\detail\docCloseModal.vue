<script lang="ts" setup>
import { ref } from 'vue';

import { useVbenModal, VbenLoading } from '@vben/common-ui';

import { Descriptions, message, Result } from 'ant-design-vue';

import { useVbenForm } from '#/adapter';
import {
  bwOutDispatchDelivery,
  bwOutDispatchStatusCheck,
} from '#/api/bondedwarehouse/outbound';

const emit = defineEmits(['success']);

const data = ref();
const isTaskCreated = ref(false);
const loadingRef = ref(false);
/* const movementID = ref(undefined); */
const dispatchData = ref({}) as any;
const [Form, formApi] = useVbenForm({
  handleSubmit: onSubmit,
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
    labelClass: 'w-24',
  },
  schema: [
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        getPopupContainer: () => document.body,
      },
      fieldName: 'AddressTo',
      label: '目的地仓库',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        maxlength: 32,
        /* autocomplete: 'off', */
      },
      fieldName: 'LicensePlate',
      label: '车牌号',
      rules: 'required',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        getPopupContainer: () => document.body,
      },
      fieldName: 'TruckModel',
      label: '车型',
      rules: 'required',
    },
    {
      component: 'DatePicker',
      componentProps: {
        style: { width: '100%' },
        showTime: { format: 'HH:00:00' },
        format: 'YYYY-MM-DD HH:00:00',
        valueFormat: 'YYYY-MM-DD HH:00:00',
      },
      fieldName: 'DeliveryDate',
      label: '发货时间',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        maxlength: 128,
        autocomplete: 'off',
      },
      fieldName: 'Remark',
      label: '备注',
    },
  ],
  showDefaultActions: false,
  wrapperClass: 'grid-cols-1',
});

const [BaseModal, modalApi] = useVbenModal({
  /* class: 'w-[640px]', */
  fullscreenButton: false,
  closeOnClickModal: false,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await formApi.submitForm();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      isTaskCreated.value = false;
      loadingRef.value = true;
      modalApi.setState({ showConfirmButton: false });
      dispatchData.value = {};
      data.value = modalApi.getData<Record<string, any>>();
      const fieldList = ['AddressTo', 'TruckModel'];
      fieldList.forEach((field) => {
        formApi.updateSchema([
          {
            componentProps: {
              options: data.value.selectOptions.filter((item: any) => {
                return item.type === field;
              }),
            },
            fieldName: field,
          },
        ]);
      });
      formApi.resetForm();
      fetch();
    }
  },
  title: '出库发车确认',
});

async function onSubmit(values: Record<string, any>) {
  const check = await formApi.validate();
  if (check.valid) {
    modalApi.setState({ confirmLoading: true });
    modalApi.setState({ loading: true });
    bwOutDispatchDelivery(Object.assign({ id: data.value.id }, values))
      .then((res) => {
        isTaskCreated.value = res;
        if (isTaskCreated.value) {
          modalApi.setState({ showCancelButton: false });
          modalApi.setState({ showConfirmButton: false });
          message.success('派车单发货完成！');
          emit('success');
        } else {
          message.error('派车单发货失败，请刷新重试！');
        }
      })
      .catch(() => {})
      .finally(() => {
        modalApi.setState({ loading: false });
        modalApi.setState({ confirmLoading: false });
      });
  }
}

/* function handleClose() {
  modalApi.close();
  emit('success');
} */
async function fetch() {
  await bwOutDispatchStatusCheck(data.value.id)
    .then((res) => {
      formApi.setValues(res);
      if (res.DeliveryStatus) {
        message.error('派车单已关闭，请刷新查看！');
      } else {
        modalApi.setState({ showConfirmButton: true });
      }
      dispatchData.value = res;
    })
    .catch(() => {})
    .finally(() => {
      loadingRef.value = false;
    });
}
</script>
<template>
  <BaseModal>
    <Result
      v-if="isTaskCreated"
      status="success"
      title="发货完毕，LIS派车单回传任务创建成功"
    >
      <template #extra>
        <a-button @click="modalApi.close()">关闭</a-button>
      </template>
    </Result>
    <div v-else>
      <Descriptions :column="2" bordered size="small">
        <Descriptions.Item :span="2" label="派车单号">
          {{ dispatchData.DispatchNo }}
        </Descriptions.Item>
        <Descriptions.Item label="实体托盘数">
          {{ dispatchData.PalletNum }}
        </Descriptions.Item>
        <Descriptions.Item label="产品总数">
          {{ dispatchData.GoodsNum }}
        </Descriptions.Item>
      </Descriptions>
      <Form class="mt-4" />
      <VbenLoading :spinning="loadingRef" />
    </div>
  </BaseModal>
</template>
