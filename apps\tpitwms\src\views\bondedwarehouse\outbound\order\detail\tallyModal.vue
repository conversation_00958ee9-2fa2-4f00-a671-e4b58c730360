<script lang="ts" setup>
import { ref } from 'vue';

import { useVbenDrawer, useVbenModal } from '@vben/common-ui';
import { AntSearch } from '@vben/icons';

import { Descriptions, message, Modal, Tag } from 'ant-design-vue';

import { useVbenForm, z } from '#/adapter';
import {
  bwOutTallyItemConfirm,
  bwOutTallyItemGetData,
} from '#/api/bondedwarehouse/outbound';
import batchSelectDrawer from '#/views/bondedwarehouse/inbound/order/detail/batchSelectDrawer.vue';

const emit = defineEmits(['success']);

const data = ref();
const itemData = ref({}) as any;
const submitData = ref({}) as any;
const [BatchSelectDrawer, BatchSelectDrawerApi] = useVbenDrawer({
  connectedComponent: batchSelectDrawer,
});

const [Form, formApi] = useVbenForm({
  handleSubmit: onSubmit,
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
    labelClass: 'w-20',
  },
  schema: [
    {
      component: 'Switch',
      componentProps: {},
      fieldName: 'isBatchManagementRequired',
      label: 'isBatchManagementRequired',
      dependencies: {
        show: false,
        triggerFields: ['A'],
      },
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'oMatType',
      label: 'oMatType',
      dependencies: {
        show: false,
        triggerFields: ['A'],
      },
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'GoodsNum',
      label: 'GoodsNum',
      dependencies: {
        show: false,
        triggerFields: ['A'],
      },
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'BatchNo',
      label: 'BatchNo',
      dependencies: {
        show: false,
        triggerFields: ['A'],
      },
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'ActualExpiration',
      label: 'ActualExpiration',
      dependencies: {
        show: false,
        triggerFields: ['A'],
      },
    },
    {
      component: 'Switch',
      componentProps: {
        class: 'w-auto',
        checkedChildren: '是',
        unCheckedChildren: '否',
      },
      defaultValue: false,
      fieldName: 'IsBatchDiff',
      label: '批次差异',
      rules: 'required',
      dependencies: {
        disabled(values) {
          return !values.isBatchManagementRequired;
        },
        triggerFields: ['isBatchManagementRequired'],
      },
    },
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 16,
      },
      fieldName: 'TallyBatch',
      label: '实物批号',
      dependencies: {
        disabled(values) {
          return !values.isBatchManagementRequired || !values.IsBatchDiff;
        },
        trigger(values, formApi) {
          formApi.setFieldValue(
            'TallyBatch',
            values.IsBatchDiff ? undefined : values.BatchNo,
          );
        },
        rules(values) {
          if (values.isBatchManagementRequired) {
            return 'required';
          }
          return null;
        },
        triggerFields: ['isBatchManagementRequired', 'IsBatchDiff'],
      },
    },
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 10,
      },
      fieldName: 'TallyExpiration',
      label: '实物效期',
      dependencies: {
        disabled(values) {
          return !values.isBatchManagementRequired || !values.IsBatchDiff;
        },
        trigger(values, formApi) {
          formApi.setFieldValue(
            'TallyExpiration',
            values.IsBatchDiff ? undefined : values.ActualExpiration,
          );
        },
        rules(values) {
          return values.isBatchManagementRequired
            ? z
                .string({ message: '请输入实物效期' })
                .regex(
                  /^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])$|^\d{4}-(0[1-9]|1[0-2])$/,
                  { message: '格式：yyyy-MM-dd或yyyy-MM' },
                )
            : null;
        },
        triggerFields: ['isBatchManagementRequired', 'IsBatchDiff'],
      },
    },
    {
      component: 'InputNumber',
      componentProps: {
        autocomplete: 'off',
        min: 0,
      },
      defaultValue: 0,
      fieldName: 'RetentionQty',
      label: '留样数量',
      rules: 'required',
      dependencies: {
        disabled(values) {
          return values.IsBatchDiff;
        },
        trigger(values, formApi) {
          if (values.IsBatchDiff) {
            formApi.setFieldValue('RetentionQty', 0, false);
          } else {
            formApi.setFieldValue(
              'RetentionQty',
              itemData.value.RetentionQty,
              false,
            );
          }
        },
        triggerFields: ['IsBatchDiff'],
      },
    },
    {
      component: 'InputNumber',
      componentProps: {
        autocomplete: 'off',
        min: 0,
      },
      defaultValue: 0,
      fieldName: 'CIQQty',
      label: 'CIQ抽样',
      rules: 'required',
      dependencies: {
        disabled(values) {
          return values.IsBatchDiff;
        },
        trigger(values, formApi) {
          if (values.IsBatchDiff) {
            formApi.setFieldValue('CIQQty', 0);
          }
        },
        triggerFields: ['IsBatchDiff'],
      },
    },
    {
      component: 'InputNumber',
      componentProps: {
        autocomplete: 'off',
        min: 0,
      },
      defaultValue: 0,
      fieldName: 'MissingQty',
      label: '贴标少',
      rules: 'required',
      dependencies: {
        disabled(values) {
          return values.IsBatchDiff || values.oMatType === 'YFG';
        },
        trigger(values, formApi) {
          if (values.IsBatchDiff) {
            formApi.setFieldValue('MissingQty', 0);
          }
        },
        triggerFields: ['IsBatchDiff', 'oMatType'],
      },
    },
    {
      component: 'InputNumber',
      componentProps: {
        autocomplete: 'off',
        min: 0,
        onChange: (e: number) => {
          formApi.setFieldValue('TallyQty', e * itemData.value.oPCB);
        },
      },
      defaultValue: 0,
      fieldName: 'TallyBoxNum',
      label: '理货箱数',
      rules: 'required',
      dependencies: {
        trigger(values, form) {
          if (itemData.value.oPCB > 0) {
            form.setFieldValue(
              'TallyBoxNum',
              Math.ceil(values.TallyQty / itemData.value.oPCB),
              true,
            );
          }
        },
        // 只有指定的字段改变时，才会触发
        triggerFields: ['TallyQty'],
      },
    },
    {
      component: 'InputNumber',
      componentProps: {
        autocomplete: 'off',
        min: 0,
      },
      defaultValue: 0,
      fieldName: 'TallyQty',
      label: '实物数量',
      rules: 'required',
      dependencies: {
        rules(values) {
          return z
            .number()
            .max(
              values.GoodsNum -
                values.RetentionQty -
                values.CIQQty -
                values.MissingQty,
              '留样+CIQ+贴标少+实物数量不能大于产品数量',
            )
            .min(0, { message: '请输入实物数量' })
            .nullable()
            .refine((value) => value !== null && value !== undefined, {
              message: '请输入实物数量',
            });
        },
        triggerFields: ['GoodsNum', 'RetentionQty', 'CIQQty', 'MissingQty'],
      },
      formItemClass: 'col-span-1 md:col-span-2',
    },
    /* {
      component: 'Divider',
      componentProps: {},
      fieldName: 'A',
      label: '',
    }, */
    {
      component: 'InputNumber',
      componentProps: {
        autocomplete: 'off',
        min: 0,
      },
      defaultValue: 0,
      fieldName: 'ShelveQty',
      label: '搁置数量',
      dependencies: {
        rules(values) {
          return z
            .number()
            .max(values.TallyQty, '不能大于实物数量')
            .min(0, { message: '请输入搁置数量' })
            .nullable()
            .refine((value) => value !== null && value !== undefined, {
              message: '请输入搁置数量',
            });
        },
        triggerFields: ['TallyQty'],
      },
    },
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 64,
      },
      fieldName: 'ShelveReason',
      label: '搁置原因',
      dependencies: {
        rules(values) {
          if (values.ShelveQty > 0) {
            return 'required';
          }
          return null;
        },
        triggerFields: ['ShelveQty'],
      },
      formItemClass: 'col-span-1 md:col-span-2',
    },
    /* {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 16,
      },
      fieldName: 'ReceivedBatch',
      label: '实收批次',
    },
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 10,
      },
      fieldName: 'ReceivedExpirationDate',
      label: '实收效期',
    }, */
    /* {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 128,
      },
      fieldName: 'Remark',
      formItemClass: 'col-span-1 md:col-span-2',
      label: '备注',
    }, */
  ],
  showDefaultActions: false,
  wrapperClass: 'grid-cols-1 md:grid-cols-3',
});

const [BaseModal, modalApi] = useVbenModal({
  class: 'w-[672px]',
  draggable: true,
  fullscreenButton: false,
  closeOnClickModal: false,
  zIndex: 900,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await formApi.submitForm();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      modalApi.setState({ showConfirmButton: false });
      itemData.value = {};
      submitData.value = {};
      data.value = modalApi.getData<Record<string, any>>();
      formApi.resetForm();
      fetch();
    }
  },
  title: '出库理货',
});

async function onSubmit(values: Record<string, any>) {
  const check = await formApi.validate();
  if (check.valid) {
    submitData.value = values;
    if (
      !submitData.value.IsBatchDiff &&
      submitData.value.RetentionQty !== itemData.value.SamplesRemainingQty
    ) {
      Modal.confirm({
        content: `留样数量和剩余应留样数不符，请确认是否提交？`,
        onCancel() {},
        onOk() {
          checkBatch();
        },
        title: `留样数量校验提醒`,
        okButtonProps: { danger: true },
      });
    } else {
      checkBatch();
    }
  }
}
function handleSubmit() {
  Modal.destroyAll();
  modalApi.setState({ confirmLoading: true });
  modalApi.setState({ loading: true });
  bwOutTallyItemConfirm(
    Object.assign(
      { id: itemData.value.Guid, rt: itemData.value.RetentionQty },
      submitData.value,
    ),
  )
    .then(() => {
      handleClose();
    })
    .catch(() => {})
    .finally(() => {
      modalApi.setState({ loading: false });
      modalApi.setState({ confirmLoading: false });
    });
}
function checkBatch() {
  if (submitData.value.IsBatchDiff) {
    if (itemData.value.BatchNo === submitData.value.TallyBatch) {
      message.info('实物批次不存在差异，请取消差异标记！');
    } else {
      Modal.confirm({
        content: `批次差异数据将进行拆分，生成库存调整单据回传，无法直接撤销！请确认录入信息是否准确？`,
        onCancel() {},
        onOk() {
          checkQty();
        },
        title: `关键操作确认`,
        okButtonProps: { danger: true },
      });
    }
  } else {
    checkQty();
  }
}

function checkQty() {
  if (submitData.value.IsBatchDiff) {
    if (
      itemData.value.GoodsNum >= submitData.value.TallyQty &&
      submitData.value.TallyQty > 0
    ) {
      handleSubmit();
    } else {
      message.error('批次有差异时，实物应大于0且不能大于产品数量！');
    }
  } else {
    if (
      itemData.value.GoodsNum ===
      submitData.value.RetentionQty +
        submitData.value.CIQQty +
        submitData.value.MissingQty +
        submitData.value.TallyQty
    ) {
      handleSubmit();
    } else {
      message.error('批次无差异时，留样+CIQ+贴标少+实物应等于产品数量！');
    }
  }
}

function handleBatchSearch() {
  BatchSelectDrawerApi.setData({
    material: itemData.value.MatCode,
  });
  BatchSelectDrawerApi.open();
}
function handleBatchSelected(row: any) {
  formApi.setFieldValue('TallyBatch', row.SubBatch);
  if (row.ShelfLifeExpirationDate) {
    formApi.setFieldValue(
      'TallyExpiration',
      row.ShelfLifeExpirationDate.slice(0, 10),
    );
  }
}
function handleClose() {
  modalApi.close();
  emit('success');
}
async function fetch() {
  await bwOutTallyItemGetData(data.value.id)
    .then((res) => {
      modalApi.setState({ showConfirmButton: true });
      itemData.value = res;
      formApi.setValues(itemData.value);
      /* formApi.updateSchema([
        {
          rules: z
            .number()
            .max(res.GoodsNum, '实物数量不能大于产品数量')
            .min(0, { message: '请输入实物数量' })
            .nullable()
            .refine((value) => value !== null && value !== undefined, {
              message: '请输入实物数量',
            }),
          fieldName: 'TallyQty',
        },
      ]); */
    })
    .catch(() => {})
    .finally(() => {});
}
</script>
<template>
  <BaseModal>
    <Descriptions :column="{ xs: 1, sm: 2, md: 3 }" bordered size="small">
      <Descriptions.Item :span="2" label="SSCC">
        {{ itemData.SSCC }}
      </Descriptions.Item>
      <Descriptions.Item label="入库托盘">
        {{ itemData.PalletNo }}
      </Descriptions.Item>
      <Descriptions.Item label="产品编号">
        {{ itemData.MatCode }}
      </Descriptions.Item>
      <Descriptions.Item label="产品条码">
        {{ itemData.oBarCode }}
      </Descriptions.Item>
      <Descriptions.Item label="产品类型">
        {{ itemData.oMatType }}
      </Descriptions.Item>
      <Descriptions.Item label="批次管理">
        <Tag :color="itemData.isBatchManagementRequired ? 'green' : 'blue'">
          {{ itemData.isBatchManagementRequired ? '是' : '否' }}
        </Tag>
      </Descriptions.Item>
      <Descriptions.Item label="批号">
        {{ itemData.BatchNo }}
      </Descriptions.Item>
      <!-- <Descriptions.Item label="制造日期">
        {{
          itemData.ManufactureDate
            ? dayjs(itemData.ManufactureDate).format('YYYY-MM-DD')
            : ''
        }}
      </Descriptions.Item> -->
      <Descriptions.Item label="实收效期">
        {{ itemData.ActualExpiration }}
      </Descriptions.Item>
      <Descriptions.Item label="产品数量">
        {{ itemData.GoodsNum }}
      </Descriptions.Item>
      <Descriptions.Item label="箱规数量">
        {{ itemData.oPCB }}
      </Descriptions.Item>
      <Descriptions.Item label="剩余应留样">
        {{ itemData.SamplesRemainingQty }}
      </Descriptions.Item>
      <!-- <Descriptions.Item label="层规数量">
        {{ itemData.iPCL }}
      </Descriptions.Item>
      <Descriptions.Item label="托规数量">
        {{ itemData.iPCP }}
      </Descriptions.Item> -->

      <!-- <Descriptions.Item label="精细理货">
        <Tag :color="itemData.iIsCareful ? 'green' : 'blue'">
          {{ itemData.iIsCareful ? '是' : '否' }}
        </Tag>
      </Descriptions.Item> -->
    </Descriptions>
    <Form class="mt-4">
      <template #TallyBatch="slotProps">
        <a-input-search v-bind="slotProps" readonly @search="handleBatchSearch">
          <template #enterButton>
            <a-button :disabled="slotProps.disabled" class="!pl-1 !pr-1">
              <AntSearch class="size-5" />
            </a-button>
          </template>
        </a-input-search>
      </template>
    </Form>
    <BatchSelectDrawer @success="handleBatchSelected" />
  </BaseModal>
</template>
