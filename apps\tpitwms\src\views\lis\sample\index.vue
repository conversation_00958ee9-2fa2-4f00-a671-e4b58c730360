<script setup lang="ts">
import type { VxeGridProps } from '#/adapter';

import { onMounted, reactive } from 'vue';

import { Page } from '@vben/common-ui';
import { AntClear } from '@vben/icons';

import { useVbenVxeGrid } from '#/adapter';
import { getSampleCommandList } from '#/api/lis';

const searchField: any = reactive({
  HBL: undefined,
  /* MatCode: undefined,
  BatchNo: undefined, */
});
const gridOptions = reactive<VxeGridProps>({
  id: 'LISSample',
  columns: [
    {
      fixed: 'left',
      title: '#',
      type: 'seq',
      width: 60,
    },
    {
      field: 'MailDate',
      title: '邮件日期',
      width: 160,
      formatter: 'formatDateTime',
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'MailSubject',
      title: '邮件主题',
      minWidth: 200,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'HBL',
      title: '入库提单HBL',
      minWidth: 150,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'Invoice',
      title: '发票号',
      minWidth: 120,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'MatCode',
      title: '物料编号',
      minWidth: 88,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'BatchNo',
      title: '批号',
      minWidth: 80,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'CustomsCategory',
      title: '海关分类',
      minWidth: 100,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'SampleCategory',
      title: '样品类别',
      minWidth: 150,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'Qty',
      title: '指令数量',
      minWidth: 100,
      filters: [{ data: { type: '0', num1: undefined, num2: undefined } }],
      filterRender: { name: 'FilterNumberRange' },
    },
    {
      field: 'ReceivedQty',
      title: '理货数量',
      minWidth: 100,
      filters: [{ data: { type: '0', num1: undefined, num2: undefined } }],
      filterRender: { name: 'FilterNumberRange' },
    },
    {
      field: 'Recorded4Item',
      title: '备货记录',
      minWidth: 100,
      filters: [{ data: { type: '0', num1: undefined, num2: undefined } }],
      filterRender: { name: 'FilterNumberRange' },
    },
    {
      field: 'Remaining4Item',
      title: '剩余未备货',
      minWidth: 100,
      filters: [{ data: { type: '0', num1: undefined, num2: undefined } }],
      filterRender: { name: 'FilterNumberRange' },
    },
    {
      field: 'RecordedQty',
      title: '理货记录',
      minWidth: 100,
      filters: [{ data: { type: '0', num1: undefined, num2: undefined } }],
      filterRender: { name: 'FilterNumberRange' },
    },
    {
      field: 'SamplesRemainingQty',
      title: '剩余未理货',
      minWidth: 100,
      filters: [{ data: { type: '0', num1: undefined, num2: undefined } }],
      filterRender: { name: 'FilterNumberRange' },
    },
  ],
  filterConfig: {
    remote: true,
  },
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: ({ filters, page, sorts }) => {
        const queryParams: any = Object.assign({}, page);
        // 处理排序条件
        if (sorts.length === 0) {
          queryParams.sort = 'MailDate desc,HBL,Invoice,MatCode,BatchNo';
        } else {
          const sortItem = sorts.map((item) => {
            const newItem = `${item.field} ${item.order}`;
            return newItem;
          });
          queryParams.sort = sortItem.join(',');
        }
        // 处理筛选
        Object.getOwnPropertyNames(searchField).forEach((key) => {
          searchField[key] = undefined;
        });
        filters.forEach(({ field, values }) => {
          queryParams[field] = values.join(',');
          if (Object.prototype.hasOwnProperty.call(searchField, field)) {
            const jsonObject = JSON.parse(values.toString());
            searchField[field] = jsonObject.text;
          }
        });
        return getSampleCommandList(queryParams)
          .then((res) => {
            return res;
          })
          .catch(() => {})
          .finally(() => {});
      },
    },
    seq: true,
  },
  scrollX: { enabled: true, gt: 0 },
  scrollY: { enabled: true, gt: 0 },
  size: 'mini',
  sortConfig: {
    remote: true,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
});
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });
function searchEvent(field: string) {
  const column = gridApi.grid.getColumnByField(field);
  if (column) {
    // 修改第一个选项为勾选状态
    const option = column.filters[0];
    if (!option) {
      return;
    }

    if (searchField[field]) {
      option.data = { type: '1', text: searchField[field] };
      option.value = JSON.stringify(option.data);
      option.checked = true;
    } else {
      option.data = { type: '0', text: undefined };
      option.checked = false;
    }

    // 修改条件之后，需要手动调用 updateData 处理表格数据
    gridApi.grid.updateData();
    gridApi.grid.commitProxy('query');
  }
}
function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
  gridApi.grid.commitProxy('query');
}
async function fetch() {}
onMounted(() => {
  setTimeout(() => {
    fetch();
  }, 300);
});
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar_left>
        <a-button class="mr-1" @click="clearFilterAndSort()">
          <AntClear class="size-5" />
        </a-button>
        <div class="hidden pl-1 md:block">
          <a-input-search
            v-model:value="searchField.HBL"
            allow-clear
            class="mr-2 w-60"
            placeholder="入库提单HBL"
            @search="searchEvent('HBL')"
          />
        </div>
      </template>
      <template #toolbar-tools> </template>
    </Grid>
  </Page>
</template>
