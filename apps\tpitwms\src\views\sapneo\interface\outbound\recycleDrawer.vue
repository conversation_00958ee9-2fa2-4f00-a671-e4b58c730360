<script setup lang="ts">
import type { VxeGridProps } from '#/adapter';

import { reactive } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';
import { RotateCw } from '@vben/icons';

import { message, Modal } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import { getOutboundRecycle, outboundRestore } from '#/api/sapneo';

const emit = defineEmits(['success']);
const searchField: any = reactive({
  DeliveryDocument: undefined,
});
const recycleGridOptions = reactive<VxeGridProps>({
  columns: [
    { type: 'seq', width: 48 },
    {
      field: 'DeliveryDocument',
      title: 'Outbound',
      minWidth: 100,
    },
    {
      field: 'SO',
      title: '销售订单',
      minWidth: 88,
    },
    {
      field: 'CreateDate',
      title: 'W创建日期',
      minWidth: 132,
      formatter: 'formatDateTime',
    },
    {
      field: 'Restore',
      title: '操作',
      width: 80,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  pagerConfig: {
    enabled: true,
    layouts: ['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump'],
  },
  proxyConfig: {
    ajax: {
      query: ({ page, sorts }) => {
        const queryParams: any = Object.assign(
          { DeliveryDocument: searchField.DeliveryDocument },
          page,
        );
        // 处理排序条件
        if (sorts.length === 0) {
          queryParams.sort = 'CreateDate desc';
        } else {
          const sortItem = sorts.map((item) => {
            const newItem = `${item.field} ${item.order}`;
            return newItem;
          });
          queryParams.sort = sortItem.join(',');
        }
        return getOutboundRecycle(queryParams);
      },
    },
    seq: true,
  },
  size: 'mini',
  sortConfig: {
    remote: true,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
});

const [RecycleGrid, recycleGridApi] = useVbenVxeGrid({
  gridOptions: recycleGridOptions,
});

const [RecycleDrawer, recycleDrawerApi] = useVbenDrawer({
  /* class: 'w-1/2', */
  title: '回收站',
  placement: 'right',
  footer: false,
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      /* recycleGridApi.query(); */
    }
  },
});

function handleRestore(id: string) {
  outboundRestore(id)
    .then(() => {
      message.success('操作成功');
      recycleGridApi.query();
      emit('success');
    })
    .catch(() => {})
    .finally(() => {});
}

function showRestoreModal(id: string, name: string) {
  Modal.confirm({
    content: `请确认是否还原？`,
    onCancel() {},
    onOk() {
      handleRestore(id);
    },
    title: `Outbound: ${name}`,
  });
}

defineExpose({
  open: recycleDrawerApi.open,
});
</script>

<template>
  <RecycleDrawer>
    <RecycleGrid>
      <template #toolbar_left>
        <a-input-search
          v-model:value="searchField.DeliveryDocument"
          allow-clear
          class="mr-2 w-48"
          placeholder="Outbound"
          @search="recycleGridApi.grid.commitProxy('query')"
        />
      </template>
      <template #action="{ row }">
        <a-button
          type="link"
          @click="showRestoreModal(row.Guid, row.DeliveryDocument)"
        >
          <RotateCw class="size-5" />
        </a-button>
      </template>
    </RecycleGrid>
  </RecycleDrawer>
</template>
