<script setup lang="ts">
import type { VxeGridListeners, VxeGridProps } from '#/adapter';

import { reactive } from 'vue';
import { useRouter } from 'vue-router';

import { useAccess } from '@vben/access';
import { Page, useVbenModal } from '@vben/common-ui';
import { AntClear, AntDelete } from '@vben/icons';

import { message, Modal, Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import {
  getStockMovementList,
  stockMovementDelete,
} from '#/api/bondedwarehouse/stock';

import createModal from './createModal.vue';

const { hasAccessByCodes } = useAccess();

const router = useRouter();
const [CreateModal, createModalApi] = useVbenModal({
  connectedComponent: createModal,
});
const searchField: any = reactive({
  DocNo: undefined,
});
const gridOptions = reactive<VxeGridProps>({
  id: 'BWStockMovement',
  columns: [
    {
      fixed: 'left',
      title: '#',
      type: 'seq',
      width: 60,
    },
    {
      field: 'DocNo',
      title: '库存调整单号',
      minWidth: 150,
      fixed: 'left',
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'IsClosed',
      title: '执行状态',
      minWidth: 88,
      filters: [
        { label: '已执行', value: true },
        { label: '待执行', value: false },
      ],
      filterMultiple: false,
      slots: { default: 'isClosed' },
    },
    {
      field: 'MovementTypes',
      title: '调整类型',
      minWidth: 128,
      filters: [{ data: null }],
      filterRender: { name: 'FilterInput' },
    },
    {
      field: 'TotalMoveQty',
      title: '调整产品数量',
      minWidth: 128,
      filters: [{ data: { type: '0', num1: undefined, num2: undefined } }],
      filterRender: { name: 'FilterNumberRange' },
    },
    {
      field: 'CreateBy',
      title: '创建人',
      width: 88,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      visible: false,
    },
    {
      field: 'CreateDate',
      title: '创建日期',
      minWidth: 150,
      formatter: 'formatDateTime',
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'CloseBy',
      title: '执行人',
      width: 100,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      visible: false,
    },
    {
      field: 'CloseDate',
      title: '调整日期',
      width: 150,
      formatter: 'formatDateTime',
      sortable: true,
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'PostingDate',
      title: '记账日期',
      minWidth: 108,
      formatter: 'formatDate',
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'Remark',
      title: '调整备注',
      minWidth: 280,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'APIExecuteStatus',
      title: '回传状态',
      width: 108,
      filters: [
        { label: '无需回传', value: 0 },
        { label: '等待回传', value: 1 },
        { label: '回传成功', value: 2 },
      ],
      align: 'center',
      slots: { default: 'apiExecuteStatus' },
    },
    {
      field: 'SAPDocNo',
      title: 'SAP单据号',
      minWidth: 120,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'Delete',
      align: 'center',
      slots: { default: 'action' },
      title: '删除',
      width: 60,
    },
  ],
  filterConfig: {
    remote: true,
  },
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: ({ filters, page, sorts }) => {
        const queryParams: any = Object.assign({}, page);
        // 处理排序
        if (sorts.length === 0) {
          queryParams.sort = 'CreateDate desc';
        } else {
          const sortItem = sorts.map((item) => {
            const newItem = `${item.field} ${item.order}`;
            return newItem;
          });
          queryParams.sort = sortItem.join(',');
        }
        // 处理筛选
        Object.getOwnPropertyNames(searchField).forEach((key) => {
          searchField[key] = undefined;
        });
        filters.forEach(({ field, values }) => {
          queryParams[field] = values.join(',');
          if (Object.prototype.hasOwnProperty.call(searchField, field)) {
            const jsonObject = JSON.parse(values.toString());
            searchField[field] = jsonObject.text;
          }
        });
        return getStockMovementList(queryParams);
      },
    },
    seq: true,
  },
  size: 'mini',
  sortConfig: {
    remote: true,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
});

const gridEvents: VxeGridListeners = {
  cellDblclick({ row }) {
    if (hasAccessByCodes(['BWStockMovement:Detial'])) {
      router.push({
        name: 'BWStockMovementDetail',
        params: { id: row.Guid },
      });
    }
  },
};
const [Grid, gridApi] = useVbenVxeGrid({ gridEvents, gridOptions });

function searchEvent(field: string) {
  const column = gridApi.grid.getColumnByField(field);
  if (column) {
    // 修改第一个选项为勾选状态
    const option = column.filters[0];
    if (!option) {
      return;
    }

    if (searchField[field]) {
      option.data = { type: '1', text: searchField[field] };
      option.value = JSON.stringify(option.data);
      option.checked = true;
    } else {
      option.data = { type: '0', text: undefined };
      option.checked = false;
    }

    // 修改条件之后，需要手动调用 updateData 处理表格数据
    gridApi.grid.updateData();
    gridApi.grid.commitProxy('query');
  }
}
function openCreateModal() {
  createModalApi.open();
}
function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
  gridApi.grid.commitProxy('query');
}
function handleSuccess() {
  gridApi.grid.commitProxy('query');
}
function handleDelete(id: string) {
  stockMovementDelete(id)
    .then(() => {
      message.success('操作成功');
      handleSuccess();
    })
    .catch(() => {})
    .finally(() => {});
}

function showDeleteModal(id: string, name: string) {
  Modal.confirm({
    content: `请确认是否删除？`,
    /* icon: createVNode(ExclamationCircleOutlined), */
    onCancel() {},
    onOk() {
      handleDelete(id);
    },
    title: `调整单号: ${name}`,
    okButtonProps: { danger: true },
  });
}
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar_left>
        <a-button class="mr-1" @click="clearFilterAndSort()">
          <AntClear class="size-5" />
        </a-button>
        <div class="hidden pl-1 md:block">
          <!-- <a-select
            v-model:value="searchField.IsClosed"
            allow-clear
            class="mr-2 w-32"
            placeholder="执行状态"
            @change="searchEvent('IsClosed', 'Select')"
          >
            <a-select-option :value="true">已执行</a-select-option>
            <a-select-option :value="false">待执行</a-select-option>
          </a-select> -->
          <a-input-search
            v-model:value="searchField.DocNo"
            allow-clear
            class="mr-2 w-60"
            placeholder="调整单号"
            @search="searchEvent('DocNo')"
          />
        </div>
      </template>
      <template #isClosed="{ row }">
        <Tag :color="row.IsClosed ? 'green' : 'blue'">
          {{ row.IsClosed ? '已执行' : '待执行' }}
        </Tag>
      </template>
      <template #apiExecuteStatus="{ row }">
        <Tag
          :color="
            row.APIExecuteStatus === 0
              ? 'blue'
              : row.APIExecuteStatus === 1
                ? 'grey'
                : 'green'
          "
        >
          {{
            row.APIExecuteStatus === 0
              ? '无需回传'
              : row.APIExecuteStatus === 1
                ? '等待回传'
                : '回传成功'
          }}
        </Tag>
      </template>

      <template #toolbar-tools>
        <a-button
          v-access:code="'BWStockMovement:Create'"
          class="mr-3"
          type="primary"
          @click="openCreateModal()"
        >
          创建调整单
        </a-button>
      </template>
      <template #action="{ row }">
        <a-button
          :disabled="
            row.IsClosed || !hasAccessByCodes(['BWStockMovement:Delete'])
          "
          danger
          size="small"
          type="link"
          @click="showDeleteModal(row.Guid, row.DocNo)"
        >
          <AntDelete class="size-5" />
        </a-button>
      </template>
    </Grid>
    <CreateModal @success="handleSuccess" />
  </Page>
</template>
