<script setup lang="ts">
import type { VxeGridListeners, VxeGridProps } from '#/adapter';

import { onMounted, reactive } from 'vue';

import { Page, useVbenDrawer } from '@vben/common-ui';
import { AntClear } from '@vben/icons';

import { useVbenVxeGrid } from '#/adapter';
import {
  getBWInOrderList,
  getInOrderItem,
} from '#/api/bondedwarehouse/inbound';
import { getOptions } from '#/api/common';
import batchDrawer from '#/views/sapneo/master/batch/batchDrawer.vue';
import skuDrawer from '#/views/sapneo/master/product/skuDrawer.vue';

const [SkuDrawer, skuDrawerApi] = useVbenDrawer({
  connectedComponent: skuDrawer,
});
const [BatchDrawer, batchDrawerApi] = useVbenDrawer({
  connectedComponent: batchDrawer,
});
function openSkuDrawer(id: string) {
  skuDrawerApi.setData({ id });
  skuDrawerApi.open();
}
function openBatchDrawer(sku: string, batch: string) {
  batchDrawerApi.setData({ sku, batch });
  batchDrawerApi.open();
}
const searchField: any = reactive({
  HBL: undefined,
});
const itemGridOptions = reactive<VxeGridProps>({
  columns: [
    { type: 'seq', width: 60 },
    {
      field: 'MatCode',
      title: '物料编号',
      width: 88,
      /* fixed: 'left', */
      slots: { default: 'material' },
    },
    {
      field: 'Invoice',
      title: '发票号',
      width: 120,
    },
    {
      field: 'Inbound',
      title: 'Inbound',
      width: 88,
    },
    {
      field: 'InboundItem',
      title: 'Item',
      width: 80,
    },
    {
      field: 'InboundBatch',
      title: '产品批号',
      width: 88,
      slots: { default: 'ibdbatch' },
    },
    {
      field: 'ShelfLifeExpirationDate',
      title: '产品效期',
      width: 88,
      formatter: 'formatDate',
    },
    {
      field: 'EstimatedReceivedQty',
      title: '产品数量',
      width: 88,
    },
    {
      field: 'SSCC',
      title: 'SSCC',
      width: 160,
    },
    {
      field: 'iCName',
      title: '中文品名',
      minWidth: 240,
    },
    { field: 'iBarCode', title: '条形码', minWidth: 112 },
    { field: 'iMatType', title: '物料类型', width: 80 },
    { field: 'iDivision', title: 'Division', width: 80 },
    {
      field: 'iBrandName',
      title: '品牌',
      width: 88,
    },
    { field: 'iOriginName', title: '原产国', width: 88 },
    { field: 'iSpecifaction', title: '规格', width: 88 },
  ],
  customConfig: {
    storage: false,
  },
  menuConfig: {
    body: {
      options: [
        [
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuCopy',
            disabled: false,
            name: '复制单元格',
            prefixConfig: {
              icon: 'vxe-icon-copy',
            },
            visible: true,
          },
        ],
      ],
    },
    className: 'font-medium shadow rounded-md',
  },
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    enabled: false,
  },
  rowConfig: {
    useKey: true,
    keyField: 'Guid',
  },
  rowStyle({ row }) {
    if (row.Qty === 0) {
      return {
        backgroundColor: '#fff4e6',
        color: '#222',
      };
    }
  },
  size: 'mini',
  stripe: false,
  toolbarConfig: {
    enabled: false,
  },
  treeConfig: {
    transform: true,
    rowField: 'ItemNumber',
    parentField: 'ParentItem',
    showLine: true,
    /* expandAll: true, */
  },
});
const [ItemGrid, itemGridApi] = useVbenVxeGrid({
  gridOptions: itemGridOptions,
});
const gridOptions = reactive<VxeGridProps>({
  id: 'Record4BWInboundUnPositioned',
  columns: [
    {
      title: '#',
      type: 'seq',
      width: 60,
    },
    { type: 'expand', width: 48, slots: { content: 'expand_content' } },
    {
      field: 'HBL',
      title: '入库提单HBL',
      minWidth: 150,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    { field: 'OwnerShortName', title: '货主', minWidth: 100, filters: [] },
    { field: 'WarehouseName', title: '仓库', minWidth: 100, filters: [] },
    { field: 'GoodsNum', title: '产品总数', minWidth: 108 },
    { field: 'PalletCount', title: '托盘数', minWidth: 64 },
    { field: 'ContractNo', title: '合同号', minWidth: 120 },
    { field: 'JobNo', title: '业务编号', minWidth: 120 },
    {
      field: 'ETA',
      title: 'ETA',
      minWidth: 88,
      formatter: 'formatDate',
    },
    {
      field: 'DeclareDate',
      title: '申报日期',
      minWidth: 88,
      formatter: 'formatDate',
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'PreInDate',
      title: '预计进仓日',
      minWidth: 100,
      formatter: 'formatDate',
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'CreateDate',
      filterRender: { name: 'FilterDateRange' },
      filters: [{ data: { date: [], type: '0' } }],
      formatter: 'formatDateTime',
      sortable: true,
      title: '创建日期',
      width: 136,
    },
  ],
  expandConfig: {
    accordion: true,
  },
  filterConfig: {
    remote: true,
  },
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: ({ filters, page, sorts }) => {
        const queryParams: any = Object.assign({ InStatus: false }, page);
        // 处理排序条件
        if (sorts.length === 0) {
          queryParams.sort = '_Identify desc';
        } else {
          const sortItem = sorts.map((item) => {
            const newItem = `${item.field} ${item.order}`;
            return newItem;
          });
          queryParams.sort = sortItem.join(',');
        }
        // 处理筛选
        Object.getOwnPropertyNames(searchField).forEach((key) => {
          searchField[key] = undefined;
        });
        filters.forEach(({ field, values }) => {
          queryParams[field] = values.join(',');
          if (Object.prototype.hasOwnProperty.call(searchField, field)) {
            const jsonObject = JSON.parse(values.toString());
            searchField[field] = jsonObject.text;
          }
        });
        return getBWInOrderList(queryParams);
      },
    },
    seq: true,
  },
  /* scrollX: { enabled: false },
  scrollY: { enabled: false }, */
  size: 'mini',
  sortConfig: {
    remote: true,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
});
const gridEvents: VxeGridListeners = {
  async toggleRowExpand({ expanded, row }) {
    await handleItemData([]);
    if (expanded) {
      handleItemLoading(true);
      const itemData = await getInOrderItem(row.Guid);
      await handleHeadRow(row);
      await handleItemData(itemData.data);
      handleItemLoading(false);
      itemGridApi.grid?.setAllTreeExpand(true);
    }
  },
};
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions, gridEvents });

function handleHeadRow(row: any) {
  gridApi.grid.setCurrentRow(row);
}
function handleItemData(data: any) {
  itemGridApi.setGridOptions({
    data,
  });
}
function handleItemLoading(isLoading: boolean) {
  itemGridApi.setLoading(isLoading);
}
function searchEvent(field: string) {
  const column = gridApi.grid.getColumnByField(field);
  if (column) {
    // 修改第一个选项为勾选状态
    const option = column.filters[0];
    if (!option) {
      return;
    }

    if (searchField[field]) {
      option.data = { type: '1', text: searchField[field] };
      option.value = JSON.stringify(option.data);
      option.checked = true;
    } else {
      option.data = { type: '0', text: undefined };
      option.checked = false;
    }

    // 修改条件之后，需要手动调用 updateData 处理表格数据
    gridApi.grid.updateData();
    gridApi.grid.commitProxy('query');
  }
}
function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
  gridApi.grid.commitProxy('query');
}
async function fetch() {
  const options = await getOptions('Owner&Warehouse');
  const fieldList = ['OwnerShortName', 'WarehouseName'];
  fieldList.forEach((field) => {
    gridApi.grid.setFilter(
      field,
      options.filter((item: any) => {
        return item.type === field;
      }),
    );
  });
}
onMounted(() => {
  setTimeout(() => {
    fetch();
  }, 300);
});
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar_left>
        <a-button class="mr-1" @click="clearFilterAndSort()">
          <AntClear class="size-5" />
        </a-button>
        <div class="hidden pl-1 md:block">
          <a-input-search
            v-model:value="searchField.HBL"
            allow-clear
            class="mr-2 w-60"
            placeholder="入库提单HBL"
            @search="searchEvent('HBL')"
          />
        </div>
      </template>
      <template #toolbar-tools> </template>
      <template #expand_content>
        <div>
          <ItemGrid>
            <template #material="{ row }">
              <a-button
                size="small"
                type="link"
                @click="openSkuDrawer(row.MatCode)"
              >
                {{ row.MatCode }}
              </a-button>
            </template>
            <template #ibdbatch="{ row }">
              <a-button
                v-if="row.InboundBatch"
                size="small"
                type="link"
                @click="openBatchDrawer(row.MatCode, row.InboundBatch)"
              >
                {{ row.InboundBatch }}
              </a-button>
            </template>
          </ItemGrid>
        </div>
      </template>
    </Grid>
    <SkuDrawer />
    <BatchDrawer />
  </Page>
</template>
