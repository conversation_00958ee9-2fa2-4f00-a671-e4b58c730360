<script setup lang="ts">
import type { VxeGridListeners, VxeGridProps } from '#/adapter';

import { onMounted, reactive, ref } from 'vue';

import { Page, useVbenDrawer } from '@vben/common-ui';
import { AntClear } from '@vben/icons';
import { downloadFileFromBlob } from '@vben/utils';

import { Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import { getRecord4BWInReceived } from '#/api/bondedwarehouse/inbound';
import { addExportRecord, exportFile, getOptions } from '#/api/common';
import batchDrawer from '#/views/sapneo/master/batch/batchDrawer.vue';
import skuDrawer from '#/views/sapneo/master/product/skuDrawer.vue';

const gridQueryParams = ref({}) as any;
const loadingRef = ref(false);
const [SkuDrawer, skuDrawerApi] = useVbenDrawer({
  // 连接抽离的组件
  connectedComponent: skuDrawer,
});
const [BatchDrawer, batchDrawerApi] = useVbenDrawer({
  connectedComponent: batchDrawer,
});
function openSkuDrawer(id: string) {
  skuDrawerApi.setData({ id });
  skuDrawerApi.open();
}
function openBatchDrawer(sku: string, batch: string) {
  batchDrawerApi.setData({ sku, batch });
  batchDrawerApi.open();
}
const searchField: any = reactive({
  HBL: undefined,
  MatCode: undefined,
});
const received: any = reactive({
  ReceivedQty: 0,
  ReceivedBoxNum: 0,
  MissingQty: 0,
});
const gridOptions = reactive<VxeGridProps>({
  id: 'Record4BWInboundReceived',
  columns: [
    {
      title: '#',
      type: 'seq',
      width: 60,
      fixed: 'left',
    },
    {
      field: 'OwnerShortName',
      title: '货主',
      width: 88,
      filters: [],
    },
    {
      field: 'WarehouseName',
      title: '仓库',
      width: 72,
      filters: [],
    },
    {
      field: 'HBL',
      title: '入库提单HBL',
      width: 132,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'Invoice',
      title: '发票号',
      width: 88,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'Inbound',
      title: 'Inbound',
      width: 88,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      visible: false,
    },
    {
      field: 'PONo',
      title: 'PO',
      width: 88,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      visible: false,
    },
    {
      field: 'SSCC',
      title: 'SSCC',
      width: 144,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      visible: false,
    },
    {
      field: 'PalletNo',
      title: '托盘号',
      width: 88,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },

    {
      field: 'MatCode',
      title: '物料编号',
      width: 88,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      slots: { default: 'material' },
    },
    {
      field: 'iMatType',
      title: '物料类型',
      width: 88,
      filters: [],
    },
    {
      field: 'iIsCareful',
      title: '精细理货',
      width: 88,
      formatter({ cellValue }) {
        return cellValue ? '是' : '否';
      },
      filters: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      filterMultiple: false,
      slots: { default: 'cStatus' },
    },
    {
      field: 'isBatchManagementRequired',
      title: '批次管理',
      width: 88,
      align: 'center',
      slots: { default: 'batchmanage' },
      formatter({ cellValue }) {
        return cellValue ? '是' : '否';
      },
      filters: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      filterMultiple: false,
    },
    {
      field: 'InboundBatch',
      title: '产品批号',
      width: 88,
      slots: { default: 'ibdbatch' },
      visible: false,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'ManufactureDateInMaster',
      title: '制造日期',
      width: 88,
      visible: false,
      formatter: 'formatDate',
    },
    {
      field: 'ShelfLifeExpirationDateInMaster',
      title: '产品效期',
      width: 88,
      visible: false,
      formatter: 'formatDate',
    },
    {
      field: 'ReceivedBatch',
      title: '实收批号',
      width: 88,
      slots: { default: 'receivedbatch' },
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'ReceivedExpirationDate',
      title: '实收效期',
      width: 88,
    },
    {
      field: 'ReceivedBoxNum',
      title: '收货箱数',
      width: 72,
    },
    {
      field: 'ReceivedQty',
      title: '收货数量',
      width: 96,
      filters: [{ data: { type: '0', num1: undefined, num2: undefined } }],
      filterRender: { name: 'FilterNumberRange' },
    },
    {
      field: 'MissingQty',
      title: '少货',
      width: 72,
      filters: [{ data: { type: '0', num1: undefined, num2: undefined } }],
      filterRender: { name: 'FilterNumberRange' },
    },

    {
      field: 'ReceivedBy',
      title: '收货人',
      width: 76,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'ReceivedDate',
      title: '收货时间',
      width: 132,
      formatter: 'formatDateTime',
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'Remark',
      title: '备注',
      minWidth: 128,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'iCName',
      title: '中文品名',
      width: 240,
      visible: false,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'iBarCode',
      title: '条形码',
      width: 112,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      visible: false,
    },
    {
      field: 'iDivision',
      title: 'Division',
      width: 88,
      visible: false,
      filters: [],
    },
    {
      field: 'iBrandName',
      title: '品牌',
      width: 80,
      filters: [],
    },
    {
      field: 'iOriginName',
      title: '原产国',
      width: 80,
      visible: false,
      filters: [],
    },
    { field: 'iSpecifaction', title: '规格', width: 80, visible: false },
    {
      field: 'InboundGRDate',
      title: 'GR日期',
      width: 132,
      formatter: 'formatDateTime',
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
  ],
  filterConfig: {
    remote: true,
  },
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: ({ filters, page, sorts }) => {
        const queryParams: any = Object.assign({}, page);
        // 处理排序条件
        if (sorts.length === 0) {
          queryParams.sort = '_Identify desc';
        } else {
          const sortItem = sorts.map((item) => {
            const newItem = `${item.field} ${item.order}`;
            return newItem;
          });
          queryParams.sort = sortItem.join(',');
        }
        // 处理筛选
        Object.getOwnPropertyNames(searchField).forEach((key) => {
          searchField[key] = undefined;
        });
        filters.forEach(({ field, values }) => {
          queryParams[field] = values.join(',');
          if (Object.prototype.hasOwnProperty.call(searchField, field)) {
            const jsonObject = JSON.parse(values.toString());
            searchField[field] = jsonObject.text;
          }
        });
        return getRecord4BWInReceived(queryParams)
          .then((res) => {
            Object.assign(received, res.received);
            return res;
          })
          .catch(() => {})
          .finally(() => {});
      },
    },
    seq: true,
  },
  scrollX: { enabled: true, gt: 0 },
  scrollY: { enabled: true, gt: 0 },
  showFooter: true,
  size: 'mini',
  sortConfig: {
    remote: true,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
  menuConfig: {
    trigger: 'cell',
    body: {
      options: [
        [
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuCopy',
            disabled: false,
            name: '复制单元格',
            prefixConfig: {
              icon: 'vxe-icon-copy',
            },
            visible: true,
          },
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuRefresh',
            disabled: false,
            name: '刷新',
            prefixConfig: { icon: 'vxe-icon-refresh' },
            visible: true,
          },
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuExport',
            disabled: false,
            name: '导出明细',
            prefixConfig: { icon: 'vxe-icon-download' },
            visible: true,
          },
        ],
      ],
    },
    className: 'font-medium shadow rounded-md',
  },
  footerRowStyle: { 'font-weight': 'bold ', backgroundColor: '#ebfbee' },
  footerMethod({ columns }) {
    return [
      columns.map((column, columnIndex) => {
        if (columnIndex === 0) {
          return `合计`;
        }
        if (
          ['MissingQty', 'ReceivedBoxNum', 'ReceivedQty'].includes(column.field)
        ) {
          return received[column.field];
        }

        return null;
      }),
    ];
  },
});
const gridEvents: VxeGridListeners = {
  menuClick({ menu }) {
    switch (menu.code) {
      case 'menuExport': {
        handleExport();
        break;
      }
      default: {
        break;
      }
    }
  },
};
/* function handleExport() {
  gridApi.grid.exportData({
    filename: `入库理货明细`,
    original: false,
    sheetName: 'Sheet1',
    type: 'xlsx',
    isFooter: false,
    columnFilterMethod: ({ column }) => {
      return column.field !== undefined;
    },
  });
} */
function handleExport() {
  addExportRecord({
    typ: 'Record4BWInboundReceived',
    params: gridQueryParams.value,
    columns: gridApi.grid
      .getColumns()
      .filter((item) => item.field && item.title)
      .map((item) => ({
        field: item.field,
        title: item.title,
      })),
  })
    .then((res) => {
      loadingRef.value = true;
      exportFile(res)
        .then((res) => {
          downloadFileFromBlob({
            source: res.data,
            fileName: `入库理货明细_${Date.now()}.xlsx`,
          });
        })
        .catch(() => {})
        .finally(() => {
          loadingRef.value = false;
        });
    })
    .catch(() => {})
    .finally(() => {});
}
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions, gridEvents });
function searchEvent(field: string) {
  const column = gridApi.grid.getColumnByField(field);
  if (column) {
    // 修改第一个选项为勾选状态
    const option = column.filters[0];
    if (!option) {
      return;
    }

    if (searchField[field]) {
      option.data = { type: '1', text: searchField[field] };
      option.value = JSON.stringify(option.data);
      option.checked = true;
    } else {
      option.data = { type: '0', text: undefined };
      option.checked = false;
    }

    // 修改条件之后，需要手动调用 updateData 处理表格数据
    gridApi.grid.updateData();
    gridApi.grid.commitProxy('query');
  }
}
function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
  gridApi.grid.commitProxy('query');
}
async function fetch() {
  const options = await getOptions('Record4BWInboundReceived');
  const fieldList = [
    'iMatType',
    'iBrandName',
    'OwnerShortName',
    'WarehouseName',
    'iDivision',
    'iOriginName',
  ];
  fieldList.forEach((field) => {
    gridApi.grid.setFilter(
      field,
      options.filter((item: any) => {
        return item.type === field;
      }),
    );
  });
}
onMounted(() => {
  setTimeout(() => {
    fetch();
  }, 300);
});
</script>

<template>
  <Page auto-content-height v-loading="loadingRef">
    <Grid>
      <template #toolbar_left>
        <a-button class="mr-1" @click="clearFilterAndSort()">
          <AntClear class="size-5" />
        </a-button>
        <div class="hidden pl-1 md:block">
          <a-input-search
            v-model:value="searchField.HBL"
            allow-clear
            class="mr-2 w-60"
            placeholder="入库提单HBL"
            @search="searchEvent('HBL')"
          />
          <a-input-search
            v-model:value="searchField.MatCode"
            allow-clear
            class="mr-2 w-48"
            placeholder="物料编号"
            @search="searchEvent('MatCode')"
          />
        </div>
      </template>
      <template #toolbar-tools> </template>
      <template #material="{ row }">
        <a class="text-blue-500" @click="openSkuDrawer(row.MatCode)">
          {{ row.MatCode }}
        </a>
      </template>
      <template #batch="{ row }">
        <a
          class="text-blue-500"
          @click="openBatchDrawer(row.MatCode, row.BatchNo)"
        >
          {{ row.BatchNo }}
        </a>
      </template>
      <template #cStatus="{ row }">
        <Tag :color="row.iIsCareful ? 'green' : 'blue'" class="mr-0">
          {{ row.iIsCareful ? '是' : '否' }}
        </Tag>
      </template>
      <template #batchmanage="{ row }">
        <Tag :color="row.isBatchManagementRequired ? 'green' : 'red'">
          {{ row.isBatchManagementRequired ? '是' : '否' }}
        </Tag>
      </template>
      <template #ibdbatch="{ row }">
        <a
          v-if="row.InboundBatch"
          class="text-blue-500"
          @click="openBatchDrawer(row.MatCode, row.InboundBatch)"
        >
          {{ row.InboundBatch }}
        </a>
      </template>
      <template #receivedbatch="{ row }">
        <a
          v-if="row.ReceivedBatch"
          class="text-blue-500"
          @click="openBatchDrawer(row.MatCode, row.ReceivedBatch)"
        >
          {{ row.ReceivedBatch }}
        </a>
      </template>
    </Grid>
    <SkuDrawer />
    <BatchDrawer />
  </Page>
</template>
