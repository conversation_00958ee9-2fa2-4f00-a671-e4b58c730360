import { requestClient, sleep } from '#/api/request';

/**
 * 系统维护 WMS基础物料相关API
 */
export async function getMaterialList(obj: object) {
  await sleep(100);
  return requestClient.get('/maintenance/data/material/getList', {
    params: obj,
  });
}

export async function materialUpdate(params: object) {
  await sleep(100);
  return requestClient.post('/maintenance/data/material/update', params);
}

export async function getMaterialData(id: string) {
  await sleep(100);
  return requestClient.get('/maintenance/data/material/getData', {
    params: { id },
  });
}

export async function materialDelete(id: string) {
  await sleep(100);
  return requestClient.post('/maintenance/data/material/del', { id });
}
