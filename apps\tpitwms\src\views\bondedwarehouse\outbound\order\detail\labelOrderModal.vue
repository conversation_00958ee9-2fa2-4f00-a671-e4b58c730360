<script lang="ts" setup>
import type { VxeGridProps } from '#/adapter';

import { reactive, ref } from 'vue';

import { /* useVbenDrawer, */ useVbenModal } from '@vben/common-ui';

import { useVbenVxeGrid } from '#/adapter';
import { getUnusedStock } from '#/api/bondedwarehouse/outbound';

const emit = defineEmits(['success']);

const data = ref();

const [Modal, modalApi] = useVbenModal({
  class: 'w-full h-full',
  fullscreenButton: true,
  closeOnClickModal: false,
  destroyOnClose: true,
  zIndex: 900,
  /* showConfirmButton: false, */
  footer: false,
  /* onCancel() {
    modalApi.close();
  }, */
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      data.value = modalApi.getData<Record<string, any>>();
      fetch();
    } else {
      emit('success');
    }
  },
  title: '标签订单',
});
const gridOptions = reactive<VxeGridProps>({
  id: 'LabelOrder',
  checkboxConfig: {
    highlight: true,
    range: true,
  },
  columns: [
    { type: 'checkbox', width: 40 },
    {
      field: 'StorageLocation',
      title: 'SAP位置',
      width: 96,
    },
    {
      field: 'HBL',
      title: '入库提单',
      minWidth: 160,
      filters: [{ data: null }],
      filterRender: { name: 'FilterInput' },
    },
    { field: 'Invoice', title: '发票号', width: 108 },
    {
      field: 'PalletNo',
      title: '入库托盘',
      minWidth: 100,
      filters: [{ data: null }],
      filterRender: { name: 'FilterInput' },
    },
    {
      field: 'MatCode',
      title: '物料编号',
      minWidth: 88,
      filters: [{ data: null }],
      filterRender: { name: 'FilterInput' },
    },
    {
      field: 'MaterialType',
      title: '物料类型',
      width: 88,
      filters: [],
    },
    {
      field: 'isBatchManagementRequired',
      title: '批次管理',
      width: 88,
      align: 'center',
      slots: { default: 'batchmanage' },
      filters: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      filterMultiple: false,
    },
    {
      field: 'BatchNo',
      title: '批次',
      minWidth: 88,
      filters: [{ data: null }],
      filterRender: { name: 'FilterInput' },
    },
    {
      field: 'BatchStatus',
      title: '批次状态',
      width: 88,
      slots: { default: 'batchstatus' },
      filters: [
        { label: '正常', value: 0 },
        { label: '限制', value: 1 },
        { label: '不存在', value: -1 },
      ],
    },
    {
      field: 'ActualExpiration',
      title: '实物效期',
      width: 88,
      formatter: 'formatDate',
    },
    {
      field: 'ReceivedDate',
      title: '收货日期',
      width: 136,
      formatter: 'formatDateTime',
    },
    { field: 'StockQty', title: '库存数量', width: 100 },
    { field: 'UnrestrictedQty', title: '非限制库存', width: 100 },
    { field: 'BlockedQty', title: '冻结库存', width: 96 },
    {
      field: 'LockedByMovement',
      title: '调整锁',
      width: 80,
      filters: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      filterMultiple: false,
      slots: { default: 'lockbymovement' },
    },
    {
      field: 'LockedByTaking',
      title: '盘点锁',
      width: 80,
      filters: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      filterMultiple: false,
      slots: { default: 'lockbytaking' },
    },
    {
      field: 'LockedByAPI',
      title: 'API锁',
      width: 80,
      filters: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      filterMultiple: false,
      slots: { default: 'lockbyapi' },
    },
    {
      field: 'BrandName',
      title: '品牌',
      width: 100,
      filters: [],
    },
    { field: 'AreaName', title: '库区', width: 100 },
    { field: 'BIN', title: '货位', width: 100 },
    {
      field: 'CName',
      title: '中文品名',
      minWidth: 240,
      filters: [{ data: null }],
      filterRender: { name: 'FilterInput' },
    },
  ],
  filterConfig: {
    remote: false,
  },
  height: 'auto',
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    ajax: {
      query: async () => {
        const res = await getUnusedStock(data.value.id);
        return res.data;
      },
    },
    seq: true,
  },
  scrollX: { enabled: true, gt: 0 },
  scrollY: { enabled: true, mode: 'wheel', gt: 30, oSize: 0 },
  size: 'mini',
  sortConfig: {
    remote: false,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
});
const [Grid /* gridApi */] = useVbenVxeGrid({ gridOptions });

/* function handleAdd() {
  const checkedRecords = gridApi.grid
    .getCheckboxRecords()
    .map((item: any) => item.Guid);
  if (checkedRecords.length === 0) {
    message.info(`请勾选需要添加的库存明细`);
    return;
  }
  modalApi.setState({ confirmLoading: true });
  modalApi.setState({ loading: true });
  bwOutOrderItemAdd(data.value.id, checkedRecords)
    .then(() => {
      gridApi.grid.commitProxy('query');
      itemGridApi.grid.commitProxy('query');
    })
    .catch(() => {})
    .finally(() => {
      modalApi.setState({ loading: false });
      modalApi.setState({ confirmLoading: false });
    });
} */
async function fetch() {}
</script>
<template>
  <Modal>
    <Grid>
      <template #toolbar_left> </template>
      <template #toolbar-tools>
        <a-button type="primary" @click="1">生成标签订单</a-button>
      </template>
    </Grid>
  </Modal>
</template>
