<script lang="ts" setup>
import type { VxeGridProps } from '#/adapter';

import { reactive, ref } from 'vue';

import { Fallback, useVbenDrawer } from '@vben/common-ui';

import { Descriptions, RadioButton, RadioGroup, Tag } from 'ant-design-vue';
import dayjs from 'dayjs';

import { useVbenVxeGrid } from '#/adapter';
import { getBatchData } from '#/api/sapneo';

const data = ref();
const dataType = ref('info');
const batchData = ref({}) as any;
const isNotFound = ref(false);
const [Drawer, drawerApi] = useVbenDrawer({
  showConfirmButton: false,
  cancelText: '关闭',
  destroyOnClose: true,
  zIndex: 900,
  onCancel() {
    drawerApi.close();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      data.value = drawerApi.getData<Record<string, any>>();
      handleGetData(data.value.sku, data.value.batch);
    }
  },
});
const gridOptions = reactive<VxeGridProps>({
  columns: [
    { type: 'seq', width: 48 },
    {
      field: 'Material',
      title: '产品编号',
      width: 88,
    },
    {
      field: 'MasterBatch',
      title: '标准批次',
      width: 88,
    },
    {
      field: 'SubBatch',
      title: '子批次',
      minWidth: 80,
    },
    {
      field: 'MatlBatchIsInRstrcdUseStock',
      title: '批次状态',
      width: 88,
      slots: { default: 'rstrcd' },
    },
  ],
  customConfig: {
    storage: false,
  },
  menuConfig: {
    body: {
      options: [
        [
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuCopy',
            disabled: false,
            name: '复制单元格',
            prefixConfig: {
              icon: 'vxe-icon-copy',
            },
            visible: true,
          },
        ],
      ],
    },
    className: 'font-medium shadow rounded-md',
  },
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    enabled: false,
  },
  rowStyle({ row }) {
    if (row.SubBatch === row.MasterBatch) {
      return {
        backgroundColor: '#868e96',
        color: '#fff',
      };
    }
  },
  size: 'small',
  toolbarConfig: {
    enabled: false,
  },
});
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
});
function handleGetData(sku: string, batch: string) {
  drawerApi.setState({ loading: true });
  getBatchData(sku, batch)
    .then((res) => {
      batchData.value = res;
      gridApi.grid.loadData(res.mapping);
      isNotFound.value = false;
    })
    .catch(() => {
      isNotFound.value = true;
    })
    .finally(() => {
      drawerApi.setState({ loading: false });
    });
}
</script>
<template>
  <Drawer title="批次信息">
    <Fallback v-if="isNotFound" :show-back="false" status="404" />
    <div v-else>
      <RadioGroup
        v-model:value="dataType"
        button-style="solid"
        class="mb-2"
        @change="1"
      >
        <RadioButton value="info">批次主数据</RadioButton>
        <RadioButton value="mapping">关联批次明细</RadioButton>
      </RadioGroup>
      <Descriptions
        v-show="dataType === 'info'"
        :column="1"
        bordered
        size="small"
      >
        <Descriptions.Item label="产品编号">
          {{ batchData.Material }}
        </Descriptions.Item>
        <Descriptions.Item label="批次">
          {{ batchData.Batch }}
        </Descriptions.Item>
        <Descriptions.Item label="关联批次">
          {{ batchData.RelatedBatches }}
        </Descriptions.Item>
        <Descriptions.Item label="工厂">
          {{ batchData.Plants }}
        </Descriptions.Item>
        <Descriptions.Item label="删除标记">
          <Tag :color="batchData.BatchIsMarkedForDeletion ? 'red' : 'green'">
            {{ batchData.BatchIsMarkedForDeletion ? '是' : '否' }}
          </Tag>
        </Descriptions.Item>
        <Descriptions.Item label="限制使用标记">
          <Tag :color="batchData.MatlBatchIsInRstrcdUseStock ? 'red' : 'green'">
            {{ batchData.MatlBatchIsInRstrcdUseStock ? '受限制' : '非限制' }}
          </Tag>
        </Descriptions.Item>
        <Descriptions.Item label="制造日期">
          {{
            batchData.ManufactureDate
              ? dayjs(batchData.ManufactureDate).format('YYYY-MM-DD')
              : ''
          }}
        </Descriptions.Item>
        <Descriptions.Item label="批次效期">
          {{
            batchData.ShelfLifeExpirationDate
              ? dayjs(batchData.ShelfLifeExpirationDate).format('YYYY-MM-DD')
              : ''
          }}
        </Descriptions.Item>
        <Descriptions.Item label="生效日期">
          {{
            batchData.MatlBatchAvailabilityDate
              ? dayjs(batchData.MatlBatchAvailabilityDate).format('YYYY-MM-DD')
              : ''
          }}
        </Descriptions.Item>
        <Descriptions.Item label="下次检查日期">
          {{
            batchData.NextInspectionDate
              ? dayjs(batchData.NextInspectionDate).format('YYYY-MM-DD')
              : ''
          }}
        </Descriptions.Item>
        <Descriptions.Item label="供应商">
          {{ batchData.Supplier }}
        </Descriptions.Item>
        <Descriptions.Item label="供应商批次">
          {{ batchData.BatchBySupplier }}
        </Descriptions.Item>
        <Descriptions.Item label="原产国">
          {{ batchData.CountryOfOrigin }}
        </Descriptions.Item>
        <Descriptions.Item label="原产地">
          {{ batchData.RegionOfOrigin }}
        </Descriptions.Item>
        <Descriptions.Item label="创建日期">
          {{ batchData.CreateDate }}
        </Descriptions.Item>
        <Descriptions.Item label="更新日期">
          {{ batchData.UpdateDate }}
        </Descriptions.Item>
        <Descriptions.Item label="自定义日期1">
          {{ batchData.FreeDefinedDate1 }}
        </Descriptions.Item>
        <Descriptions.Item label="自定义日期2">
          {{ batchData.FreeDefinedDate2 }}
        </Descriptions.Item>
        <Descriptions.Item label="自定义日期3">
          {{ batchData.FreeDefinedDate3 }}
        </Descriptions.Item>
        <Descriptions.Item label="自定义日期4">
          {{ batchData.FreeDefinedDate4 }}
        </Descriptions.Item>
        <Descriptions.Item label="自定义日期5">
          {{ batchData.FreeDefinedDate5 }}
        </Descriptions.Item>
        <Descriptions.Item label="自定义日期6">
          {{ batchData.FreeDefinedDate6 }}
        </Descriptions.Item>
        <Descriptions.Item label="BEWMInternalId">
          {{ batchData.BatchExtWhseMgmtInternalId }}
        </Descriptions.Item>
        <Descriptions.Item label="ClassInternalID">
          {{ batchData.ClassInternalID }}
        </Descriptions.Item>
      </Descriptions>
      <Grid v-show="dataType === 'mapping'">
        <template #rstrcd="{ row }">
          <Tag :color="row.MatlBatchIsInRstrcdUseStock ? 'red' : 'green'">
            {{ row.MatlBatchIsInRstrcdUseStock ? '受限制' : '非限制' }}
          </Tag>
        </template>
      </Grid>
    </div>
  </Drawer>
</template>
