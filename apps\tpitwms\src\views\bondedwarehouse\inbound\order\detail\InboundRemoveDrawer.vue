<script lang="ts" setup>
import type { VxeGridProps } from '#/adapter';

import { reactive, ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import {
  bwInOrderInboundRemove,
  getInOrderInbound,
} from '#/api/bondedwarehouse/inbound';

const emit = defineEmits(['success']);
const data = ref();

const [Drawer, drawerApi] = useVbenDrawer({
  confirmText: '确认移除',
  zIndex: 900,
  onCancel() {
    drawerApi.close();
  },
  onConfirm: async () => {
    await handleRemove();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      data.value = drawerApi.getData<Record<string, any>>();
    }
  },
});
const gridOptions = reactive<VxeGridProps>({
  id: 'InboundDeliveryRemove',
  checkboxConfig: { highlight: true, range: true },
  columns: [
    { type: 'checkbox', width: 48, fixed: 'left' },
    { field: 'DeliveryNumber', title: 'Inbound', width: 88 },
    { field: 'AsnNumber', title: 'ASN编号', minWidth: 88 },
    { field: 'LorealDocument', title: '采购订单', width: 88 },
    { field: 'TotalQty', title: '总件数', width: 80 },
    { field: 'SSCCCount', title: 'SSCC数量', width: 80 },
  ],
  filterConfig: {
    remote: false,
  },
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    ajax: {
      query: () => {
        return getInOrderInbound(data.value.id);
      },
    },
    seq: true,
  },
  size: 'mini',
  sortConfig: {
    remote: false,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
});
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });
function handleRemove() {
  const checkedRecords = gridApi.grid
    .getCheckboxRecords()
    .map((item: any) => item.Guid);
  if (checkedRecords.length === 0) {
    message.info(`请勾选需要移除的Inbound`);
    return;
  }
  drawerApi.setState({ confirmLoading: true });
  drawerApi.setState({ loading: true });
  bwInOrderInboundRemove(data.value.id, checkedRecords)
    .then(() => {
      handleClose();
    })
    .catch(() => {})
    .finally(() => {
      drawerApi.setState({ loading: false });
      drawerApi.setState({ confirmLoading: false });
    });
}
function handleClose() {
  drawerApi.close();
  emit('success');
}
</script>
<template>
  <Drawer title="Inbound移除">
    <Grid />
  </Drawer>
</template>
