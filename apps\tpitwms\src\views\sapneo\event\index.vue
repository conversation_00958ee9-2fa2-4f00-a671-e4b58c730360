<script setup lang="ts">
import type { VxeGridProps } from '#/adapter';

import { onMounted, reactive } from 'vue';

import { Page, useVbenDrawer } from '@vben/common-ui';
import { AntClear, CarbonAPI, CarbonData } from '@vben/icons';

import { Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import { getOptions } from '#/api/common';
import { getEventList } from '#/api/sapneo';

import apiDrawer from './apiDrawer.vue';
import eventDrawer from './eventDrawer.vue';

const [ApiDrawer, apiDrawerApi] = useVbenDrawer({
  connectedComponent: apiDrawer,
});
const [EventDrawer, eventDrawerApi] = useVbenDrawer({
  connectedComponent: eventDrawer,
});
const searchField: any = reactive({
  /* APIParams: undefined, */
  EventID: undefined,
});
function openAPIDrawer(id: string) {
  apiDrawerApi.setData({ id });
  apiDrawerApi.setState({ title: `API Record：${id}` }).open();
  /* apiDrawerApi.open(); */
}
function openEventDrawer(id: string) {
  eventDrawerApi.setData({ id });
  eventDrawerApi.open();
}
const gridOptions = reactive<VxeGridProps>({
  id: 'NEOEventList',
  columns: [
    {
      title: '#',
      type: 'seq',
      width: 60,
    },
    {
      field: 'ReceiveDate',
      filterRender: { name: 'FilterDateRange' },
      filters: [{ data: { type: '0', date: [] } }],
      formatter: 'formatDateTime',
      sortable: true,
      title: 'ReceiveDate',
      width: 144,
    },
    {
      field: 'Topic',
      filterMultiple: true,
      filters: [],
      minWidth: 220,
      title: 'Topic',
    },
    {
      field: 'Partition',
      title: 'Partition',
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      width: 96,
    },
    {
      field: 'OffSet',
      title: 'OffSet',
      filters: [{ data: { type: '0', num1: undefined, num2: undefined } }],
      filterRender: { name: 'FilterNumberRange' },
      width: 96,
    },
    {
      field: 'EventID',
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      minWidth: 220,
      title: 'EventID',
    },
    {
      field: 'EventType',
      filterMultiple: true,
      filters: [
        { label: 'Create', value: 'Create' },
        { label: 'Update', value: 'Update' },
        { label: 'Delete', value: 'Delete' },
      ],
      title: 'Type',
      width: 72,
    },
    {
      field: 'EventDate',
      filterRender: { name: 'FilterDateRange' },
      filters: [{ data: { type: '0', date: [] } }],
      formatter: 'formatDateTime',
      sortable: true,
      title: 'EventDate',
      width: 144,
    },
    {
      field: 'Source',
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      title: 'Source',
      width: 120,
    },
    {
      field: 'DataKey',
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      title: 'DataKey',
      minWidth: 88,
    },
    {
      field: 'APIParams',
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
      minWidth: 100,
      title: 'APIParams',
    },
    {
      align: 'center',
      field: 'IsRequired',
      filterMultiple: false,
      filters: [
        { label: 'Yes', value: true },
        { label: 'No', value: false },
      ],
      slots: { default: 'isrequired' },
      title: 'Data/Required',
      width: 128,
    },
    {
      align: 'center',
      field: 'CallStatus',
      filterMultiple: true,
      filters: [],
      slots: { default: 'callstatus' },
      title: 'APICallStatus',
      width: 120,
    },
  ],
  filterConfig: {
    remote: true,
  },
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: ({ filters, page, sorts }) => {
        const queryParams: any = Object.assign({}, page);
        // 处理排序条件
        if (sorts.length === 0) {
          queryParams.sort = '_Identify desc';
        } else {
          const sortItem = sorts.map((item) => {
            const newItem = `${item.field} ${item.order}`;
            return newItem;
          });
          queryParams.sort = sortItem.join(',');
        }
        // 处理筛选
        Object.getOwnPropertyNames(searchField).forEach((key) => {
          searchField[key] = undefined;
        });
        filters.forEach(({ field, values }) => {
          queryParams[field] = values.join(',');
          if (Object.prototype.hasOwnProperty.call(searchField, field)) {
            const jsonObject = JSON.parse(values.toString());
            searchField[field] = jsonObject.text;
          }
        });
        return getEventList(queryParams);
      },
    },
    seq: true,
  },
  scrollX: { enabled: true, gt: 0 },
  scrollY: { enabled: true, gt: 0 },
  size: 'mini',
  sortConfig: {
    remote: true,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
});
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });
function searchEvent(field: string) {
  const column = gridApi.grid.getColumnByField(field);
  if (column) {
    // 修改第一个选项为勾选状态
    const option = column.filters[0];
    if (!option) {
      return;
    }

    if (searchField[field]) {
      option.data = { type: '1', text: searchField[field] };
      option.value = JSON.stringify(option.data);
      option.checked = true;
    } else {
      option.data = { type: '0', text: undefined };
      option.checked = false;
    }

    // 修改条件之后，需要手动调用 updateData 处理表格数据
    gridApi.grid.updateData();
    gridApi.grid.commitProxy('query');
  }
}
function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
  gridApi.grid.commitProxy('query');
}
async function fetch() {
  const options = await getOptions('EventListOptions');
  const fieldList = ['Topic', 'CallStatus'];
  fieldList.forEach((field) => {
    gridApi.grid.setFilter(
      field,
      options.filter((item: any) => {
        return item.type === field;
      }),
    );
  });
}
onMounted(() => {
  setTimeout(() => {
    fetch();
  }, 1000);
});
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar_left>
        <a-button class="mr-1" @click="clearFilterAndSort()">
          <AntClear class="size-5" />
        </a-button>
        <div class="hidden pl-1 md:block">
          <a-input-search
            v-model:value="searchField.EventID"
            allow-clear
            class="mr-2 w-80"
            placeholder="Search EventID"
            @search="searchEvent('EventID')"
          />
          <!-- <a-input-search
            v-model:value="searchField.APIParams"
            allow-clear
            class="mr-2 w-60"
            placeholder="Search APIParams"
            @search="searchEvent('APIParams')"
          /> -->
        </div>
      </template>
      <template #toolbar-tools> </template>
      <template #isrequired="{ row }">
        <a-button
          size="small"
          style="margin-right: 8px"
          type="link"
          @click="openEventDrawer(row.Guid)"
        >
          <CarbonData class="size-6" />
        </a-button>
        <Tag :color="row.IsRequired ? 'green' : 'red'">
          {{ row.IsRequired ? 'Yes' : 'No' }}
        </Tag>
      </template>
      <template #callstatus="{ row }">
        <a-button
          v-if="row.CallStatus > 0"
          size="small"
          style="margin-right: 8px"
          type="link"
          @click="openAPIDrawer(row.EventID)"
        >
          <CarbonAPI class="size-6" />
        </a-button>
        <Tag
          :color="
            row.CallStatus === 0
              ? 'grey'
              : row.CallStatus === 200
                ? 'green'
                : 'red'
          "
        >
          {{
            row.CallStatus === 0
              ? 'Pending'
              : row.CallStatus === -1
                ? 'Needless'
                : row.CallStatus
          }}
        </Tag>
      </template>
    </Grid>
    <ApiDrawer />
    <EventDrawer />
  </Page>
</template>
