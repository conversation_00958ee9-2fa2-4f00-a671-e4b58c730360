<script lang="ts" setup>
import type { VxeGridProps } from '#/adapter';

import { onActivated, onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';

import { Page, useVbenModal, VbenButton } from '@vben/common-ui';
import { useTabs } from '@vben/hooks';
import { useTabbarStore } from '@vben/stores';

import { Card, message, Modal } from 'ant-design-vue';

import { useVbenForm, useVbenVxeGrid } from '#/adapter';
import {
  apiHeadersDelete,
  apiHeadersUpdate,
  apiUpdate,
  getAPIData,
  getHeadersList,
} from '#/api/maintenance/interface';

import dataMappingModal from './dataMappingModal.vue';
import headersModal from './headersModal.vue';

interface RowType {
  Guid: string;
  HeaderOrder: number;
  HeaderKey: string;
  HeaderValue: string;
}
const apiData = ref({}) as any;
const mimeOptions = ref([]) as any;
const init = ref(false);
const submitLoading = ref(false);
const route = useRoute();
const { setTabTitle } = useTabs();
const id = route.params?.id?.toString() ?? '';
const [HeadersModal, headersModalApi] = useVbenModal({
  connectedComponent: headersModal,
});
const [DataMappingModal, dataMappingModalApi] = useVbenModal({
  connectedComponent: dataMappingModal,
});
const [Form, baseFormApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'pb-1',
    labelClass: 'w-28 md:w-32',
  },
  // 提交函数
  handleSubmit: onSubmit,
  layout: 'horizontal',
  schema: [
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'APIType',
      label: 'APIType',
      dependencies: {
        show: false,
        // 随意一个字段改变时，都会触发
        triggerFields: ['A'],
      },
    },
    {
      component: 'Input',
      componentProps: {},
      formItemClass: 'col-span-1 md:col-span-2',
      fieldName: 'APIName',
      label: 'APIName',
      disabled: true,
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'APIMethod',
      label: 'Method',
      disabled: true,
    },
    {
      component: 'Switch',
      componentProps: {
        class: 'w-auto',
      },
      fieldName: 'IsActived',
      label: 'IsActived',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 128,
      },
      formItemClass: 'col-span-1 md:col-span-2',
      fieldName: 'AccessTokenURL',
      label: 'AccessTokenURL',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 128,
      },
      formItemClass: 'col-span-1 md:col-span-2',
      fieldName: 'Scope',
      label: 'Scope',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 36,
      },
      formItemClass: 'col-span-1 md:col-span-2',
      fieldName: 'ClientID',
      label: 'ClientID',
      rules: 'required',
    },
    {
      component: 'InputPassword',
      componentProps: {
        autocomplete: 'off',
        maxlength: 64,
      },
      formItemClass: 'col-span-1 md:col-span-2',
      fieldName: 'ClientSecret',
      label: 'ClientSecret',
      rules: 'required',
    },
    {
      component: 'Textarea',
      componentProps: {
        maxlength: 999,
        autoSize: { minRows: 1, maxRows: 3 },
      },
      formItemClass: 'col-span-1 md:col-span-4',
      fieldName: 'APIURL',
      label: 'APIURL',
      rules: 'required',
    },
    /* {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 255,
      },
      fieldName: 'ParamsMapEvent',
      label: 'ParamsInEvent',
      rules: 'required',
      formItemClass: 'col-span-1 md:col-span-2',
      dependencies: {
        show(values) {
          return values.APIType === 'NEO2WMS';
        },
        triggerFields: ['APIType'],
      },
    }, */
    {
      component: 'Select',
      componentProps: {
        getPopupContainer: () => document.body,
      },
      fieldName: 'ContentType',
      label: 'ContentType',
      rules: 'required',
      formItemClass: 'col-span-1 md:col-span-2',
      dependencies: {
        show(values) {
          return values.APIMethod !== 'GET';
        },
        triggerFields: ['APIMethod'],
      },
    },
    {
      component: 'Select',
      componentProps: {
        getPopupContainer: () => document.body,
      },
      fieldName: 'Accept',
      label: 'Accept',
      rules: 'required',
      formItemClass: 'col-span-1 md:col-span-2',
    },
    {
      component: 'Select',
      componentProps: {
        getPopupContainer: () => document.body,
        mode: 'multiple',
        maxTagCount: 4,
        options: [
          { label: '400 Bad Request', value: 400 },
          { label: '401 Unauthorized', value: 401 },
          { label: '403 Forbidden', value: 403 },
          { label: '404 Not Found', value: 404 },
          { label: '500 Internal Server Error', value: 500 },
          { label: '502 Bad Gateway', value: 502 },
          { label: '503 Service Unavailable', value: 503 },
          { label: '504 Gateway Time-out', value: 504 },
        ],
        /* tokenSeparators: [','], */
      },
      fieldName: 'RetryCode',
      formItemClass: 'col-span-1 md:col-span-2',
      label: 'RetryCode',
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: -1,
      },
      fieldName: 'MaxRetryTimes',
      label: 'MaxRetryTimes',
    },
    {
      component: 'Switch',
      componentProps: {
        class: 'w-auto',
      },
      fieldName: 'FaultNotification',
      label: 'FaultNotification',
    },
  ],
  showDefaultActions: false,
  wrapperClass: 'grid-cols-1 md:grid-cols-4',
});
const gridOptions: VxeGridProps = {
  columns: [
    {
      editRender: { name: 'AInput', props: { type: 'number' } },
      field: 'HeaderOrder',
      title: 'Sort No.',
      width: 120,
    },
    {
      editRender: { name: 'AInput' },
      field: 'HeaderKey',
      title: 'Key',
      width: 240,
    },
    {
      editRender: { name: 'AInput' },
      field: 'HeaderValue',
      title: 'Value',
      minWidth: 320,
    },
    { slots: { default: 'action' }, title: 'Action', width: 160 },
  ],
  customConfig: {
    storage: false,
  },
  editConfig: {
    mode: 'row',
    trigger: 'manual',
    autoClear: false,
  },
  editRules: {
    HeaderOrder: [{ required: true }],
    HeaderKey: [{ required: true }],
    HeaderValue: [{ required: true }],
  },
  minHeight: 360,
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    autoLoad: false,
    ajax: {
      query: async () => {
        return await getHeadersList(id);
      },
    },
  },
  toolbarConfig: {
    enabled: false,
  },
};
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });
async function onSubmit(values: Record<string, any>) {
  const check = await baseFormApi.validate();
  if (check.valid) {
    submitLoading.value = true;
    apiUpdate(Object.assign({ id }, values))
      .then(() => {
        message.success('操作成功');
      })
      .catch(() => {})
      .finally(() => {
        setTimeout(() => {
          submitLoading.value = false;
        }, 500);
      });
  } else {
    message.info('必填项未填写完整，请检查！');
  }
}
function openHeadersModal() {
  headersModalApi.setData({ apiID: id });
  headersModalApi.open();
}
function openDataMappingModal() {
  dataMappingModalApi.setData({ apiID: id, apiName: apiData.value.Name });
  dataMappingModalApi.open();
}
function hasEditStatus(row: RowType) {
  return gridApi.grid?.isEditByRow(row);
}

function editRowEvent(row: RowType) {
  gridApi.grid?.setEditRow(row);
}

async function saveRowEvent(row: RowType) {
  await gridApi.grid?.clearEdit();

  gridApi.setLoading(true);

  apiHeadersUpdate(Object.assign({ id: row.Guid, APIID: id }, row))
    .then(() => {
      message.success('操作成功');
    })
    .catch(() => {})
    .finally(() => {
      gridApi.setLoading(false);
    });
}

const cancelRowEvent = (_row: RowType) => {
  gridApi.grid?.clearEdit();
};

function showDeleteModal(row: RowType) {
  Modal.confirm({
    content: `请确认是否删除参数：${row.HeaderKey}?`,
    onCancel() {},
    onOk() {
      handleDelete(row.Guid);
    },
    title: '操作确认',
  });
}

function handleDelete(id: string) {
  apiHeadersDelete(id)
    .then(() => {
      message.success('操作成功');
      handleSuccess();
    })
    .catch(() => {})
    .finally(() => {});
}

function handleSuccess() {
  gridApi.grid.commitProxy('query');
}

async function fetch() {
  await gridApi.grid.commitProxy('query');
  apiData.value = await getAPIData(id);
  mimeOptions.value = apiData.value.options;
  baseFormApi.updateSchema([
    {
      componentProps: {
        options: mimeOptions.value,
      },
      fieldName: 'Accept',
    },
  ]);
  baseFormApi.updateSchema([
    {
      componentProps: {
        options: mimeOptions.value,
      },
      fieldName: 'ContentType',
    },
  ]);
  /* setTabTitle(`${apiData.value.Type}-${apiData.value.Name}`); */
  setTabTitle(`Settings-${apiData.value.Name}`);
  baseFormApi.setValues(apiData.value.Setting);
  init.value = false;
}
onMounted(() => {
  init.value = true;
  fetch();
});
onActivated(() => {
  const tabbarStore = useTabbarStore();
  const currentTab = tabbarStore.getTabs.find(
    (item) => item.path === route.path,
  );
  if (!currentTab?.meta.newTabTitle && !init.value) {
    fetch();
  }
});
</script>

<template>
  <Page>
    <Card>
      <template #title>
        {{ `API Settings` }}
      </template>
      <template #extra>
        <VbenButton
          v-if="apiData.Type === 'NEO2WMS'"
          size="sm"
          variant="info"
          @click="openDataMappingModal()"
        >
          Data Mapping
        </VbenButton>
        <a-button
          :loading="submitLoading"
          type="primary"
          @click="baseFormApi.submitForm()"
        >
          Save
        </a-button>
      </template>

      <Form />
    </Card>
    <Card>
      <template #title>API Headers</template>
      <template #extra>
        <a-button type="primary" @click="openHeadersModal()"> Add </a-button>
      </template>
      <Grid>
        <template #action="{ row }">
          <template v-if="hasEditStatus(row)">
            <a-button type="link" @click="saveRowEvent(row)">Save</a-button>
            <a-button type="link" @click="cancelRowEvent(row)">Cancel</a-button>
          </template>
          <template v-else>
            <a-button type="link" @click="editRowEvent(row)">Edit</a-button>
            <a-button danger type="link" @click="showDeleteModal(row)">
              Delete
            </a-button>
          </template>
        </template>
      </Grid>
    </Card>
    <HeadersModal @success="handleSuccess" />
    <DataMappingModal />
  </Page>
</template>
