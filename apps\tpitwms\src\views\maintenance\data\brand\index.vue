<script setup lang="ts">
import type { VxeGridListeners, VxeGridProps } from '#/adapter';

import { reactive } from 'vue';

import { Page, useVbenDrawer } from '@vben/common-ui';
import { AntClear, AntDelete } from '@vben/icons';

import { message, Modal, Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import { brandDelete, getBrandList } from '#/api/maintenance/data';

import brandDrawer from './brandDrawer.vue';

const [BrandDrawer, brandDrawerApi] = useVbenDrawer({
  // 连接抽离的组件
  connectedComponent: brandDrawer,
});
const searchField: any = reactive({
  BrandName: undefined,
});
const gridOptions = reactive<VxeGridProps>({
  id: 'DataOwner',
  columns: [
    {
      title: '#',
      type: 'seq',
      width: 60,
    },
    {
      field: 'BrandName',
      title: '品牌名称',
      minWidth: 160,
      filters: [{ data: null }],
      filterRender: { name: 'FilterInput' },
    },
    {
      field: 'BrandEName',
      title: '英文名称',
      minWidth: 200,
      filters: [{ data: null }],
      filterRender: { name: 'FilterInput' },
    },
    {
      field: 'Division',
      title: 'Division',
      width: 128,
      filters: [{ data: null }],
      filterRender: { name: 'FilterInput' },
    },
    {
      field: 'SapS4Code',
      title: 'SapS4代码',
      width: 120,
    },
    {
      field: 'SapEccCode',
      title: 'SapECC代码',
      width: 120,
    },
    {
      field: 'IsActived',
      title: '启用状态',
      width: 120,
      slots: { default: 'isActived' },
      filters: [
        { label: '正常', value: 1 },
        { label: '停用', value: 0 },
      ],
      filterMultiple: false,
    },
    { field: 'Remark', title: '备注', minWidth: 160 },
    {
      field: 'CreateBy',
      title: '创建人',
      width: 120,
    },
    {
      field: 'CreateDate',
      filterRender: { name: 'FilterDateRange' },
      filters: [{ data: { date: [], type: '0' } }],
      formatter: 'formatDateTime',
      sortable: true,
      title: '创建日期',
      width: 136,
    },
    {
      field: 'UpdateDate',
      filterRender: { name: 'FilterDateRange' },
      filters: [{ data: { date: [], type: '0' } }],
      formatter: 'formatDateTime',
      sortable: true,
      title: '更新日期',
      width: 136,
    },
    {
      align: 'center',
      slots: { default: 'action' },
      title: '删除',
      width: 60,
    },
  ],
  filterConfig: {
    remote: true,
  },
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: ({ filters, page, sorts }) => {
        const queryParams: any = Object.assign({}, page);
        // 处理排序
        if (sorts.length === 0) {
          queryParams.sort = '_Identify desc';
        } else {
          const sortItem = sorts.map((item) => {
            const newItem = `${item.field} ${item.order}`;
            return newItem;
          });
          queryParams.sort = sortItem.join(',');
        }
        // 处理筛选
        Object.getOwnPropertyNames(searchField).forEach((key) => {
          searchField[key] = undefined;
        });
        filters.forEach(({ field, values }) => {
          queryParams[field] = values.join(',');
          if (Object.prototype.hasOwnProperty.call(searchField, field)) {
            searchField[field] = values.toString();
          }
        });
        return getBrandList(queryParams);
      },
    },
    seq: true,
  },
  scrollY: { enabled: true, gt: 0 },
  size: 'mini',
  sortConfig: {
    remote: true,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
});

const gridEvents: VxeGridListeners = {
  cellDblclick({ row }) {
    openBrandDrawer(row.Guid);
  },
};
const [Grid, gridApi] = useVbenVxeGrid({ gridEvents, gridOptions });

function searchEvent(field: string, type: string) {
  const column = gridApi.grid.getColumnByField(field);
  if (column) {
    // 修改第一个选项为勾选状态
    const option = column.filters[0];
    if (!option) {
      return;
    }
    if (searchField[field]) {
      option.data = searchField[field];
      option.value = option.data;
      option.checked = true;
    } else {
      if (type) {
        option.data = undefined;
        option.checked = false;
      }
    }
    // 修改条件之后，需要手动调用 updateData 处理表格数据
    gridApi.grid.updateData();
    gridApi.grid.commitProxy('query');
  }
}
function openBrandDrawer(id: string) {
  brandDrawerApi.setData({ id });
  brandDrawerApi.open();
}
function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
  gridApi.grid.commitProxy('query');
}
function handleSuccess() {
  gridApi.grid.commitProxy('query');
}
function handleDelete(id: string) {
  brandDelete(id)
    .then(() => {
      message.success('操作成功');
      handleSuccess();
    })
    .catch(() => {})
    .finally(() => {});
}

function showDeleteModal(id: string, name: string) {
  Modal.confirm({
    content: `请确认是否删除？`,
    /* icon: createVNode(ExclamationCircleOutlined), */
    onCancel() {},
    onOk() {
      handleDelete(id);
    },
    title: `品牌: ${name}`,
    okButtonProps: { danger: true },
  });
}
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar_left>
        <a-button class="mr-1" @click="clearFilterAndSort()">
          <AntClear class="size-5" />
        </a-button>
        <div class="hidden pl-1 md:block">
          <a-input-search
            v-model:value="searchField.BrandName"
            allow-clear
            class="mr-2 w-60"
            placeholder="品牌名称"
            @search="searchEvent('BrandName', 'Input')"
          />
        </div>
      </template>
      <template #isActived="{ row }">
        <Tag :color="row.IsActived ? 'green' : 'red'">
          {{ row.IsActived ? '启用' : '停用' }}
        </Tag>
      </template>
      <template #toolbar-tools>
        <a-button class="mr-3" type="primary" @click="openBrandDrawer('new')">
          新增品牌
        </a-button>
      </template>
      <template #action="{ row }">
        <a-button
          danger
          size="small"
          type="link"
          @click="showDeleteModal(row.Guid, row.BrandName)"
        >
          <AntDelete class="size-5" />
        </a-button>
      </template>
    </Grid>
    <BrandDrawer @success="handleSuccess" />
  </Page>
</template>
