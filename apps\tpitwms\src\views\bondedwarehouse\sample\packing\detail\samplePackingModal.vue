<script lang="ts" setup>
import type { VxeGridProps } from '#/adapter';

import { reactive, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { AntClear } from '@vben/icons';

import { InputNumber, message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import {
  bwOutSampleItemLoading,
  getOutSampleUnPacking,
} from '#/api/bondedwarehouse/outbound';

const emit = defineEmits(['success']);

const data = ref();
const packingData = ref([]) as any;
const searchField: any = reactive({
  BLNo: undefined,
});

const [PackModal, packModalApi] = useVbenModal({
  class: 'w-[840px]',
  fullscreenButton: false,
  title: '装箱确认',
  onConfirm: async () => {
    await handlePacking();
  },
});

const [Modal, modalApi] = useVbenModal({
  class: 'w-full h-full',
  destroyOnClose: true,
  fullscreenButton: true,
  closeOnClickModal: false,
  confirmText: '确认',
  zIndex: 900,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await handleLoading();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      data.value = modalApi.getData<Record<string, any>>();
      fetch();
    }
  },
  onBeforeClose() {
    emit('success');
    return true;
  },
  title: '留样产品装箱',
});
const gridOptions = reactive<VxeGridProps>({
  id: 'BWOutSampleUnPackingList',
  checkboxConfig: {
    highlight: true,
    range: true,
    checkMethod: ({ row }: any) => {
      return row.YUB;
    },
  },
  columns: [
    { type: 'checkbox', width: 40 },
    {
      fixed: 'left',
      title: '#',
      type: 'seq',
      width: 60,
    },
    {
      field: 'BLNo',
      title: '出库提单BL',
      width: 150,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'YUB',
      title: 'SAP销售订单',
      minWidth: 160,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'HBL',
      title: '入库提单HBL',
      minWidth: 100,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'Invoice',
      title: '发票号',
      minWidth: 100,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'MatCode',
      title: '物料编号',
      minWidth: 100,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'BatchNo',
      title: '实物批号',
      minWidth: 100,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    { field: 'ActualExpiration', title: '限用日期', minWidth: 100 },
    {
      field: 'UnrestrictedQty',
      title: '留样数量',
      minWidth: 100,
      filters: [{ data: { type: '0', num1: undefined, num2: undefined } }],
      filterRender: { name: 'FilterNumberRange' },
    },
    {
      field: 'TallyDate',
      title: '理货日期',
      minWidth: 88,
      formatter: 'formatDateTime',
      sortable: true,
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
  ],
  filterConfig: {
    remote: true,
  },
  height: 'auto',
  pagerConfig: {
    pageSize: 50,
  },
  proxyConfig: {
    ajax: {
      query: ({ filters, page, sorts }) => {
        const queryParams: any = Object.assign({}, page);
        // 处理排序条件
        if (sorts.length === 0) {
          queryParams.sort = '_Identify';
        } else {
          const sortItem = sorts.map((item) => {
            const newItem = `${item.field} ${item.order}`;
            return newItem;
          });
          queryParams.sort = sortItem.join(',');
        }
        // 处理筛选
        Object.getOwnPropertyNames(searchField).forEach((key) => {
          searchField[key] = undefined;
        });
        filters.forEach(({ field, values }) => {
          queryParams[field] = values.join(',');
          if (Object.prototype.hasOwnProperty.call(searchField, field)) {
            const jsonObject = JSON.parse(values.toString());
            searchField[field] = jsonObject.text;
          }
        });
        return getOutSampleUnPacking(queryParams);
      },
    },
    seq: true,
  },
  /* scrollY: { enabled: true, gt: 0 }, */
  size: 'mini',
  sortConfig: {
    remote: true,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
});

const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });

const packGridOptions = reactive<VxeGridProps>({
  id: 'OutPickingModalPack',
  columns: [
    {
      fixed: 'left',
      title: '#',
      type: 'seq',
      width: 60,
    },
    {
      field: 'BLNo',
      title: '出库提单BL',
      width: 180,
    },
    {
      field: 'YUB',
      title: 'SAP销售订单',
      width: 120,
    },
    {
      field: 'MatCode',
      title: '物料编号',
      width: 88,
    },
    {
      field: 'BatchNo',
      title: '实物批号',
      width: 88,
    },
    { field: 'UnrestrictedQty', title: '留样库存', width: 80 },
    {
      field: 'PackingQty',
      title: '装箱数量(可调整)',
      minWidth: 100,
      editRender: { name: 'AInput', autofocus: '' },
      slots: { edit: 'packingqty_edit' },
    },
  ],
  editConfig: {
    trigger: 'dblclick',
    mode: 'cell',
    showIcon: true,
    showStatus: true,
  },
  keepSource: true,
  size: 'mini',
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    enabled: false,
  },
  toolbarConfig: {
    custom: false,
    refresh: false,
    slots: {
      buttons: 'toolbar_left',
    },
  },
});
const [PackGrid, packGridApi] = useVbenVxeGrid({
  gridOptions: packGridOptions,
});

/* function handleClose() {
  modalApi.close();
  emit('success');
} */
async function handleLoading() {
  const checkedRecords = gridApi.grid
    .getCheckboxRecords()
    .map((item: any) => item.Guid);
  if (checkedRecords.length === 0) {
    message.info(`请勾选需要装箱的留样产品`);
    return;
  }
  gridApi.grid.getCheckboxRecords().forEach((item) => {
    item.PackingQty = item.UnrestrictedQty;
  });
  packingData.value = gridApi.grid.getCheckboxRecords();
  packGridOptions.data = packingData.value;
  packGridApi.setGridOptions(packGridOptions);
  packModalApi.open();
  /* modalApi.setState({ confirmLoading: true });
  modalApi.setState({ loading: true });
  bwOutSampleItemLoading(data.value.id, checkedRecords)
    .then(() => {
      handleClose();
    })
    .catch(() => {})
    .finally(() => {
      modalApi.setState({ loading: false });
      modalApi.setState({ confirmLoading: false });
    }); */
}
async function handlePacking() {
  let matCheck = true;
  const dt: any = packGridApi.grid.getData();
  const values = [] as any;
  const ids = [] as any;
  dt.forEach((item: any) => {
    if (!item.PackingQty || item.PackingQty === 0) {
      message.error(`请填入装箱数量！`);
      matCheck = false;
      return;
    }
    if (item.PackingQty < 0) {
      message.error(`装箱数量需大于0！`);
      matCheck = false;
      return;
    }
    ids.push(item.Guid);
    values.push({ Guid: item.Guid, PackingQty: item.PackingQty });
  });
  if (!matCheck) {
    return;
  }
  packModalApi.setState({ confirmLoading: true });
  packModalApi.setState({ loading: true });
  bwOutSampleItemLoading(data.value.id, ids, values)
    .then(() => {
      packModalApi.close();
      gridApi.grid.commitProxy('query');
    })
    .catch(() => {})
    .finally(() => {
      packModalApi.setState({ loading: false });
      packModalApi.setState({ confirmLoading: false });
    });
}
function checkPickingQty(row: any) {
  if (row.PackingQty > row.UnrestrictedQty) {
    row.PackingQty = row.UnrestrictedQty;
    message.info('装箱数量不能大于留样库存');
  }
}
function searchEvent(field: string) {
  const column = gridApi.grid.getColumnByField(field);
  if (column) {
    // 修改第一个选项为勾选状态
    const option = column.filters[0];
    if (!option) {
      return;
    }

    if (searchField[field]) {
      option.data = { type: '1', text: searchField[field] };
      option.value = JSON.stringify(option.data);
      option.checked = true;
    } else {
      option.data = { type: '0', text: undefined };
      option.checked = false;
    }

    // 修改条件之后，需要手动调用 updateData 处理表格数据
    gridApi.grid.updateData();
    gridApi.grid.commitProxy('query');
  }
}
/* function handleSuccess() {
  gridApi.grid.commitProxy('query');
} */
function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
  gridApi.grid.commitProxy('query');
}
async function fetch() {}
</script>
<template>
  <Modal>
    <Grid>
      <template #toolbar_left>
        <a-button class="mr-1" @click="clearFilterAndSort()">
          <AntClear class="size-5" />
        </a-button>
        <div class="hidden pl-1 md:block">
          <a-input-search
            v-model:value="searchField.BLNo"
            allow-clear
            class="mr-2 w-60"
            placeholder="出库提单BL"
            @search="searchEvent('BLNo')"
          />
        </div>
      </template>
      <template #toolbar-tools> </template>
    </Grid>
    <PackModal>
      <PackGrid>
        <template #packingqty_edit="{ row }">
          <InputNumber
            v-model:value="row.PackingQty"
            :min="1"
            @change="checkPickingQty(row)"
          />
        </template>
      </PackGrid>
    </PackModal>
  </Modal>
</template>
