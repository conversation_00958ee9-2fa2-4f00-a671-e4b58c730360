<script lang="ts" setup>
import { ref } from 'vue';

import { Fallback, useVbenDrawer } from '@vben/common-ui';

import { message, Tree } from 'ant-design-vue';

import { useVbenForm } from '#/adapter';
import { getRoleData, roleUpdate } from '#/api/maintenance/user';

const emit = defineEmits(['success']);

const data = ref();
const treeDate = ref();
const treeCheckedKeys = ref();
const isNotFound = ref(false);
const [Form, baseFormApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  // 提交函数
  handleSubmit: onSubmit,
  layout: 'horizontal',
  schema: [
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 16,
        placeholder: '请输入',
      },
      fieldName: 'RoleName',
      label: '角色名称',
      rules: 'required',
    },
    {
      component: 'RadioGroup',
      componentProps: {
        options: [
          {
            label: '正常',
            value: true,
          },
          {
            label: '停用',
            value: false,
          },
        ],
      },
      defaultValue: true,
      fieldName: 'IsActived',
      label: '使用状态',
      rules: 'required',
    },
    {
      component: 'Textarea',
      componentProps: {
        autocomplete: 'off',
        maxlength: 128,
        placeholder: '请输入',
      },
      fieldName: 'Description',
      label: '角色描述',
    },
    {
      component: 'Input',
      fieldName: 'field1',
      label: '权限列表',
    },
  ],
  showDefaultActions: false,
  wrapperClass: 'grid-cols-1',
});
const [Drawer, drawerApi] = useVbenDrawer({
  onCancel() {
    drawerApi.close();
  },
  onConfirm() {
    /* console.log(baseFormApi.validate()); */
    baseFormApi.submitForm();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      isNotFound.value = false;
      data.value = drawerApi.getData<Record<string, any>>();
      baseFormApi.updateSchema([
        {
          componentProps: {
            disabled: !(data.value.id === 'new'),
          },
          fieldName: 'RoleName',
        },
      ]);

      handleGetData(data.value.id);
    }
  },
});
function handleGetData(id: string) {
  drawerApi.setState({ loading: true });
  getRoleData(id)
    .then((res) => {
      treeDate.value = res.roleMenu;
      if (data.value.id === 'new') {
        drawerApi.setState({ title: '新增角色' });
        baseFormApi.resetForm();
        treeCheckedKeys.value = [];
      } else {
        drawerApi.setState({ title: '编辑角色' });
        treeCheckedKeys.value = res.info.Permission;
        baseFormApi.setValues(res.info);
      }
    })
    .catch(() => {
      isNotFound.value = true;
    })
    .finally(() => {
      drawerApi.setState({ loading: false });
    });
}
function handleTreeClick(checkedKeys: any, e: any) {
  const { checked, node } = e;
  if (
    node.type === 'func' &&
    checked &&
    !checkedKeys.includes(`${node.eventKey.split(':')[0]}:View`)
  ) {
    message.info('请先勾选“模块可见”权限');
    treeCheckedKeys.value = checkedKeys.filter(
      (k: string) => k !== node.eventKey,
    );
  } else if (node.type === 'view' && !checked) {
    treeCheckedKeys.value = checkedKeys.filter(
      (item: string) => item.split(':')[0] !== node.group,
    );
  } else {
    treeCheckedKeys.value = checkedKeys;
  }
}
async function onSubmit(values: Record<string, any>) {
  const check = await baseFormApi.validate();
  if (check.valid) {
    const routeData: string[] = [];
    if (treeCheckedKeys.value.length === 0) {
      message.info('请选择至少一项权限');
      return;
    }
    const permissionData = treeCheckedKeys.value.filter((item: string) => {
      const itemData: string[] = item.split(':');
      if (itemData.length === 2) {
        routeData.push(itemData[0] || '');
        return true;
      } else {
        return false;
      }
    });
    drawerApi.setState({ confirmLoading: true });
    drawerApi.setState({ loading: true });
    roleUpdate(
      Object.assign(
        {
          id: data.value.id,
          permission: permissionData,
          route: [...new Set(routeData)],
        },
        values,
      ),
    )
      .then(() => {
        message.success('操作成功');
        drawerApi.close();
        emit('success');
      })
      .catch(() => {})
      .finally(() => {
        drawerApi.setState({ loading: false });
        drawerApi.setState({ confirmLoading: false });
      });
  }
}
</script>
<template>
  <Drawer>
    <Fallback v-if="isNotFound" :show-back="false" status="404" />
    <Form v-else>
      <template #field1="slotProps">
        <Tree
          v-bind="slotProps"
          :checked-keys="treeCheckedKeys"
          :tree-data="treeDate"
          checkable
          @check="handleTreeClick"
        />
      </template>
    </Form>
  </Drawer>
</template>

<style scoped lang="less">
::v-deep(.ant-input[disabled]) {
  color: rgb(0 0 0 / 70%);
}
::v-deep(input[disabled]) {
  color: rgb(0 0 0 / 70%) !important;
}
</style>
