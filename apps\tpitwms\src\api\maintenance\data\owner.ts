import { requestClient, sleep } from '#/api/request';

/**
 * 系统维护 货主信息相关API
 */
export async function getOwnerList(obj: object) {
  await sleep(100);
  return requestClient.get('/maintenance/data/owner/getList', {
    params: obj,
  });
}

export async function ownerUpdate(params: object) {
  await sleep(100);
  return requestClient.post('/maintenance/data/owner/update', params);
}

export async function getOwnerData(id: string) {
  await sleep(100);
  return requestClient.get('/maintenance/data/owner/getData', {
    params: { id },
  });
}

export async function ownerDelete(id: string) {
  await sleep(100);
  return requestClient.post('/maintenance/data/owner/del', { id });
}
