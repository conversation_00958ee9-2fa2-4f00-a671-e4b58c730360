<script lang="ts" setup>
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Descriptions, Modal } from 'ant-design-vue';

import { useVbenForm, z } from '#/adapter';
import {
  bwInReturnItemGetData,
  bwInReturnItemReceiving,
} from '#/api/bondedwarehouse/inbound';

const emit = defineEmits(['success']);

const data = ref();
const itemData = ref({}) as any;
const submitData = ref({}) as any;

const [Form, formApi] = useVbenForm({
  handleSubmit: onSubmit,
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
    labelClass: 'w-24',
  },
  schema: [
    {
      component: 'InputNumber',
      componentProps: {
        autocomplete: 'off',
        min: 0,
        onChange: (e: number) => {
          formApi.setFieldValue('ReceivedQty', e * itemData.value.iPCB);
        },
      },
      fieldName: 'ReceivedBoxNum',
      label: '实收箱数',
      rules: 'required',
      dependencies: {
        trigger(values, form) {
          if (itemData.value.iPCB > 0) {
            form.setFieldValue(
              'ReceivedBoxNum',
              Math.ceil(values.ReceivedQty / itemData.value.iPCB),
              true,
            );
          }
        },
        // 只有指定的字段改变时，才会触发
        triggerFields: ['ReceivedQty'],
      },
    },
    {
      component: 'InputNumber',
      componentProps: {
        autocomplete: 'off',
        min: 0,
      },
      fieldName: 'ReceivedQty',
      label: '实收数量',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 128,
      },
      fieldName: 'Remark',
      formItemClass: 'col-span-1 md:col-span-2',
      label: '备注',
    },
  ],
  showDefaultActions: false,
  wrapperClass: 'grid-cols-1 md:grid-cols-2',
});

const [BaseModal, modalApi] = useVbenModal({
  class: 'w-[640px]',
  draggable: true,
  fullscreenButton: false,
  closeOnClickModal: false,
  destroyOnClose: true,
  zIndex: 900,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await formApi.submitForm();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      modalApi.setState({ showConfirmButton: false });
      itemData.value = {};
      submitData.value = {};
      data.value = modalApi.getData<Record<string, any>>();
      formApi.resetForm();
      fetch();
    }
  },
  title: '退货收货',
});

async function onSubmit(values: Record<string, any>) {
  const check = await formApi.validate();
  if (check.valid) {
    submitData.value = values;
    checkQty();
  }
}
function handleSubmit() {
  Modal.destroyAll();
  modalApi.setState({ confirmLoading: true });
  modalApi.setState({ loading: true });
  bwInReturnItemReceiving(
    Object.assign({ id: data.value.id }, submitData.value),
  )
    .then(() => {
      handleClose();
    })
    .catch(() => {})
    .finally(() => {
      modalApi.setState({ loading: false });
      modalApi.setState({ confirmLoading: false });
    });
}

function checkQty() {
  if (itemData.value.EstimatedReceivedQty > submitData.value.ReceivedQty) {
    Modal.confirm({
      closable: true,
      content: `实收数量小于产品数量，确认少货？`,
      onCancel() {},
      onOk() {
        handleSubmit();
      },
      title: `数量差异提醒`,
    });
  } else {
    handleSubmit();
  }
}

function handleClose() {
  modalApi.close();
  emit('success');
}
async function fetch() {
  await bwInReturnItemGetData(data.value.id)
    .then((res) => {
      modalApi.setState({ showConfirmButton: true });
      itemData.value = res;
      formApi.setValues(itemData.value);
      formApi.updateSchema([
        {
          rules: z
            .number()
            .max(res.EstimatedReceivedQty, '实收数量不能大于产品数量')
            .min(0, { message: '请输入实收数量' })
            .nullable()
            .refine((value) => value !== null && value !== undefined, {
              message: '请输入实收数量',
            }),
          fieldName: 'ReceivedQty',
        },
      ]);
    })
    .catch(() => {})
    .finally(() => {});
}
</script>
<template>
  <BaseModal>
    <Descriptions :column="{ xs: 1, sm: 2, md: 3 }" bordered size="small">
      <Descriptions.Item :span="2" label="SSCC">
        {{ itemData.SSCC }}
      </Descriptions.Item>
      <Descriptions.Item label="入库托盘">
        {{ itemData.PalletNo }}
      </Descriptions.Item>
      <Descriptions.Item label="产品编号">
        {{ itemData.MatCode }}
      </Descriptions.Item>
      <Descriptions.Item label="产品条码">
        {{ itemData.iBarCode }}
      </Descriptions.Item>
      <Descriptions.Item label="产品类型">
        {{ itemData.iMatType }}
      </Descriptions.Item>
      <Descriptions.Item label="箱规数量">
        {{ itemData.iPCB }}
      </Descriptions.Item>
      <Descriptions.Item label="层规数量">
        {{ itemData.iPCL }}
      </Descriptions.Item>
      <Descriptions.Item label="托规数量">
        {{ itemData.iPCP }}
      </Descriptions.Item>
      <Descriptions.Item label="产品数量">
        {{ itemData.EstimatedReceivedQty }}
      </Descriptions.Item>
    </Descriptions>
    <Form class="mt-4" />
  </BaseModal>
</template>
