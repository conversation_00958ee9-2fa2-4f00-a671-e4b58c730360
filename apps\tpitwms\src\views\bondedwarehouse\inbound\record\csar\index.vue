<script setup lang="ts">
import type { VxeGridProps } from '#/adapter';

import { onMounted, reactive } from 'vue';

import { Page } from '@vben/common-ui';
import { AntClear } from '@vben/icons';

import { Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import { getRecord4BWInCSAR } from '#/api/bondedwarehouse/inbound';

const searchField: any = reactive({
  HBL: undefined,
  /* MatCode: undefined,
  BatchNo: undefined, */
});
const gridOptions = reactive<VxeGridProps>({
  id: 'Record4BWInboundCSAR',
  columns: [
    {
      fixed: 'left',
      title: '#',
      type: 'seq',
      width: 60,
    },
    {
      field: 'HBL',
      title: '入库提单HBL',
      minWidth: 150,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'MatCode',
      title: '物料编号',
      minWidth: 88,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'BatchNo',
      title: '实收批号',
      minWidth: 88,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'Status',
      width: 88,
      title: '检查状态',
      align: 'center',
      slots: { default: 'status' },
      formatter({ cellValue }) {
        return cellValue ? '已检查' : '待检查';
      },
      filters: [
        { label: '已检查', value: true },
        { label: '待检查', value: false },
      ],
      filterMultiple: false,
    },
    {
      field: 'CName',
      title: '中文品名',
      minWidth: 88,
      filters: [
        { label: 'Y', value: 'Y' },
        { label: 'N', value: 'N' },
        { label: 'NA', value: 'NA' },
      ],
    },
    {
      field: 'GuidingWords',
      title: '引导语',
      minWidth: 88,
      filters: [
        { label: 'Y', value: 'Y' },
        { label: 'N', value: 'N' },
        { label: 'NA', value: 'NA' },
      ],
    },
    {
      field: 'ExpireDate',
      title: '限制使用日期',
      minWidth: 112,
      filters: [
        { label: 'Y', value: 'Y' },
        { label: 'N', value: 'N' },
        { label: 'NA', value: 'NA' },
      ],
    },
    {
      field: 'HP',
      title: 'HP No.',
      minWidth: 88,
      filters: [
        { label: 'Y', value: 'Y' },
        { label: 'N', value: 'N' },
        { label: 'NA', value: 'NA' },
      ],
    },
    {
      field: 'CName2',
      title: '备案信息',
      minWidth: 88,
      filters: [
        { label: 'Y', value: 'Y' },
        { label: 'N', value: 'N' },
        { label: 'NA', value: 'NA' },
      ],
    },
    {
      field: 'SPF',
      title: '防晒值',
      minWidth: 88,
      filters: [
        { label: 'Y', value: 'Y' },
        { label: 'N', value: 'N' },
        { label: 'NA', value: 'NA' },
      ],
    },
    {
      field: 'MadeIn',
      title: '原产国',
      minWidth: 88,
      filters: [
        { label: 'Y', value: 'Y' },
        { label: 'N', value: 'N' },
        { label: 'NA', value: 'NA' },
      ],
    },
    {
      field: 'Formula',
      title: '配方号',
      minWidth: 88,
      filters: [
        { label: 'Y', value: 'Y' },
        { label: 'N', value: 'N' },
        { label: 'NA', value: 'NA' },
      ],
    },
    {
      field: 'BarCode',
      title: '条形码',
      minWidth: 88,
      filters: [
        { label: 'Y', value: 'Y' },
        { label: 'N', value: 'N' },
        { label: 'NA', value: 'NA' },
      ],
    },
    {
      field: 'Others',
      title: '其他特殊情况',
      minWidth: 112,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'UpdateBy',
      title: '检查人',
      minWidth: 88,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'UpdateDate',
      title: '更新日期',
      minWidth: 136,
      formatter: 'formatDateTime',
      filterRender: { name: 'FilterDateRange' },
      filters: [{ data: { date: [], type: '0' } }],
      sortable: true,
    },
  ],
  filterConfig: {
    remote: true,
  },
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: ({ filters, page, sorts }) => {
        const queryParams: any = Object.assign({}, page);
        // 处理排序条件
        if (sorts.length === 0) {
          queryParams.sort = 'Status,_Identify desc';
        } else {
          const sortItem = sorts.map((item) => {
            const newItem = `${item.field} ${item.order}`;
            return newItem;
          });
          queryParams.sort = sortItem.join(',');
        }
        // 处理筛选
        Object.getOwnPropertyNames(searchField).forEach((key) => {
          searchField[key] = undefined;
        });
        filters.forEach(({ field, values }) => {
          queryParams[field] = values.join(',');
          if (Object.prototype.hasOwnProperty.call(searchField, field)) {
            const jsonObject = JSON.parse(values.toString());
            searchField[field] = jsonObject.text;
          }
        });
        return getRecord4BWInCSAR(queryParams)
          .then((res) => {
            return res;
          })
          .catch(() => {})
          .finally(() => {});
      },
    },
    seq: true,
  },
  scrollX: { enabled: true, gt: 0 },
  scrollY: { enabled: true, gt: 0 },
  size: 'mini',
  sortConfig: {
    remote: true,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
});
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });
function searchEvent(field: string) {
  const column = gridApi.grid.getColumnByField(field);
  if (column) {
    // 修改第一个选项为勾选状态
    const option = column.filters[0];
    if (!option) {
      return;
    }

    if (searchField[field]) {
      option.data = { type: '1', text: searchField[field] };
      option.value = JSON.stringify(option.data);
      option.checked = true;
    } else {
      option.data = { type: '0', text: undefined };
      option.checked = false;
    }

    // 修改条件之后，需要手动调用 updateData 处理表格数据
    gridApi.grid.updateData();
    gridApi.grid.commitProxy('query');
  }
}
function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
  gridApi.grid.commitProxy('query');
}
async function fetch() {}
onMounted(() => {
  setTimeout(() => {
    fetch();
  }, 300);
});
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar_left>
        <a-button class="mr-1" @click="clearFilterAndSort()">
          <AntClear class="size-5" />
        </a-button>
        <div class="hidden pl-1 md:block">
          <a-input-search
            v-model:value="searchField.HBL"
            allow-clear
            class="mr-2 w-60"
            placeholder="入库提单HBL"
            @search="searchEvent('HBL')"
          />
          <!-- <a-input-search
            v-model:value="searchField.MatCode"
            allow-clear
            class="mr-2 w-48"
            placeholder="物料编号"
            @search="searchEvent('MatCode', 'Input')"
          />
          <a-input-search
            v-model:value="searchField.BatchNo"
            allow-clear
            class="mr-2 w-48"
            placeholder="实收批号"
            @search="searchEvent('BatchNo', 'Input')"
          /> -->
        </div>
      </template>
      <template #status="{ row }">
        <Tag :color="row.Status ? 'green' : 'blue'">
          {{ row.Status ? '已检查' : '待检查' }}
        </Tag>
      </template>
      <template #toolbar-tools> </template>
    </Grid>
    <SkuDrawer />
    <BatchDrawer />
  </Page>
</template>
