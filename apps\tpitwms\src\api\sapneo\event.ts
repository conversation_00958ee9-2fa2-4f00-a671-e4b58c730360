import { requestClient, sleep } from '#/api/request';

/**
 * SAP NEO EVENT相关API
 */
export async function getEventList(obj: object) {
  await sleep(100);
  return requestClient.get('/sapneo/event/getList', { params: obj });
}

export async function getEventData(id: string) {
  await sleep(100);
  return requestClient.get('/sapneo/event/getData', { params: { id } });
}

export async function getEventAPIRecord(id: string) {
  await sleep(100);
  return requestClient.get('/sapneo/event/api/getRecord', { params: { id } });
}
