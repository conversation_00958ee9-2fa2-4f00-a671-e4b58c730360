import { requestClient, sleep } from '#/api/request';

/**
 * 库存管理 库存调整相关API
 */
export async function getStockMovementList(obj: object) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/stock/movement/getList', {
    params: obj,
  });
}

export async function stockMovementGetDoc() {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/stock/movement/getDoc');
}

export async function stockMovementCreate(params: object) {
  await sleep(100);
  return requestClient.post('/bondedwarehouse/stock/movement/create', params);
}

export async function stockMovementDelete(id: string) {
  await sleep(100);
  return requestClient.post('/bondedwarehouse/stock/movement/del', { id });
}

export async function getStockMovementHead(id: string) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/stock/movement/head/getData', {
    params: { id },
  });
}

export async function getStockMovementItem(id: string) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/stock/movement/item/getList', {
    params: { id },
  });
}

export async function stockMovementGetStockData(id: string) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/stock/movement/stock/getData', {
    params: { id },
  });
}

export async function stockMovementItemAdd(params: object) {
  await sleep(100);
  return requestClient.post('/bondedwarehouse/stock/movement/item/add', params);
}

export async function stockMovementItemDelete(id: string, ids: Array<string>) {
  await sleep(100);
  return requestClient.post('/bondedwarehouse/stock/movement/item/del', {
    id,
    ids,
  });
}

export async function stockMovementExecuteCheck(id: string) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/stock/movement/execute/check', {
    params: { id },
  });
}
export async function stockMovementExecute(params: object) {
  await sleep(100);
  return requestClient.post('/bondedwarehouse/stock/movement/execute', params);
}

export async function stockMovementBlockItemAdd(
  id: string,
  ids: Array<string>,
) {
  await sleep(100);
  return requestClient.post('/bondedwarehouse/stock/movement/blockitem/add', {
    id,
    ids,
  });
}
