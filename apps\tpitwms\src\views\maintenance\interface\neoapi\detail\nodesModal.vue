<script lang="ts" setup>
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter';
import { apiMappingNodesUpdate } from '#/api/maintenance/interface';

defineOptions({
  name: 'NodesModal',
});

const emit = defineEmits(['success']);
const data = ref();

const [Form, formApi] = useVbenForm({
  handleSubmit: onSubmit,
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
    labelClass: 'w-28',
  },
  schema: [
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 32,
      },
      fieldName: 'NodeName',
      label: 'Node Name',
      rules: 'required',
    },

    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 255,
      },
      fieldName: 'NodeXPath',
      label: 'Node XPath',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 48,
      },
      fieldName: 'MappingTable',
      label: 'Mapping Table',
      rules: 'required',
    },
    {
      component: 'Select',
      componentProps: {
        getPopupContainer: () => document.body,
      },
      fieldName: 'ParentID',
      label: 'Parent Node',
      /* dependencies: {
        rules(values) {
          if (!['Main', 'main'].includes(values.NodeName)) {
            return 'required';
          }
          return null;
        },
        triggerFields: ['NodeName'],
      }, */
    },
  ],
  showDefaultActions: false,
});

const [Modal, modalApi] = useVbenModal({
  fullscreenButton: false,
  closeOnClickModal: false,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await formApi.submitForm();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      data.value = modalApi.getData<Record<string, any>>();
      formApi.updateSchema([
        {
          componentProps: {
            options: data.value.nodesOptions,
          },
          fieldName: 'ParentID',
        },
      ]);
    }
  },
  title: 'Add Node',
});

async function onSubmit(values: Record<string, any>) {
  const check = await formApi.validate();
  if (check.valid) {
    modalApi.setState({ confirmLoading: true });
    modalApi.setState({ loading: true });
    apiMappingNodesUpdate(
      Object.assign({ id: data.value.id, APIID: data.value.apiID }, values),
    )
      .then(() => {
        message.success('操作成功');
        modalApi.close();
        emit('success');
      })
      .catch(() => {})
      .finally(() => {
        modalApi.setState({ confirmLoading: false });
        modalApi.setState({ loading: false });
      });
  }
}
</script>
<template>
  <Modal>
    <Form />
  </Modal>
</template>
