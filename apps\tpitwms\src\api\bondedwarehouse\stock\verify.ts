import { requestClient, sleep } from '#/api/request';

/**
 * 库存管理 差异核销相关API
 */
export async function getStockPLList(obj: object) {
  await sleep(100);
  return requestClient.get('/bondedwarehouse/stock/verify/pl/getList', {
    params: obj,
  });
}

export async function getStockMovementCommandList(obj: object) {
  await sleep(100);
  return requestClient.get(
    '/bondedwarehouse/stock/verify/movement/command/getList',
    {
      params: obj,
    },
  );
}
