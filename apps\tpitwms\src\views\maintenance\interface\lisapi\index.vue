<script lang="ts" setup>
import type { VxeGridProps } from '#/adapter';

import { onMounted, ref } from 'vue';

import { Page } from '@vben/common-ui';

import { Card, message } from 'ant-design-vue';

import { useVbenForm, useVbenVxeGrid } from '#/adapter';
import {
  getLisApiList,
  getLisApiPubData,
  lisApiPubUpdate,
  lisApiUpdate,
} from '#/api/maintenance/interface';

const submitLoading = ref(false);
/* const apiData = ref({}) as any; */
const env = ref('');
const mimeOptions = ref([]) as any;

const [Form, baseFormApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'pb-1',
    labelClass: 'w-32',
  },
  // 提交函数
  handleSubmit: onSubmit,
  layout: 'horizontal',
  schema: [
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 255,
      },
      fieldName: 'APIURL',
      label: 'API URL',
      rules: 'required',
      formItemClass: 'col-span-1 md:col-span-3',
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'APIMethod',
      label: 'API Method',
      disabled: true,
    },
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 60,
      },
      fieldName: 'UserID',
      label: 'PubParams_userid',
      rules: 'required',
      /* formItemClass: 'col-span-1 md:col-span-2', */
    },
    {
      component: 'InputPassword',
      componentProps: {
        autocomplete: 'off',
        maxlength: 32,
      },
      fieldName: 'Token',
      label: 'PubParams_token',
      rules: 'required',
      /* formItemClass: 'col-span-1 md:col-span-2', */
    },
    {
      component: 'Select',
      componentProps: {
        getPopupContainer: () => document.body,
      },
      fieldName: 'ContentType',
      label: 'ContentType',
      rules: 'required',
    },
    {
      component: 'Select',
      componentProps: {
        getPopupContainer: () => document.body,
      },
      fieldName: 'Accept',
      label: 'Accept',
      rules: 'required',
    },
    {
      component: 'Select',
      componentProps: {
        getPopupContainer: () => document.body,
        mode: 'multiple',
        maxTagCount: 4,
        options: [
          { label: '400 Bad Request', value: 400 },
          { label: '401 Unauthorized', value: 401 },
          { label: '403 Forbidden', value: 403 },
          { label: '404 Not Found', value: 404 },
          { label: '500 Internal Server Error', value: 500 },
          { label: '502 Bad Gateway', value: 502 },
          { label: '503 Service Unavailable', value: 503 },
          { label: '504 Gateway Time-out', value: 504 },
        ],
        /* tokenSeparators: [','], */
      },
      fieldName: 'RetryCode',
      formItemClass: 'col-span-1 md:col-span-2',
      label: 'RetryCode',
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: -1,
      },
      fieldName: 'MaxRetryTimes',
      label: 'MaxRetryTimes',
    },
    {
      component: 'Switch',
      componentProps: {
        class: 'w-auto',
      },
      fieldName: 'FaultNotification',
      label: 'FaultNotification',
    },
    {
      component: 'Input',
      componentProps: {
        autocomplete: 'off',
        maxlength: 255,
      },
      fieldName: 'ProxyAddress',
      label: 'ProxyAddress',
      dependencies: {
        triggerFields: ['EnableProxy'],
        required(values) {
          return values.EnableProxy;
        },
      },
      formItemClass: 'col-span-1 md:col-span-3',
    },
    {
      component: 'Switch',
      componentProps: {
        class: 'w-auto',
      },
      defaultValue: true,
      fieldName: 'EnableProxy',
      label: 'EnableProxy',
    },
  ],
  showDefaultActions: false,
  wrapperClass: 'grid-cols-1 md:grid-cols-4',
});
const gridOptions: VxeGridProps = {
  columns: [
    { title: '#', type: 'seq', width: 48 },
    {
      field: 'APIName',
      title: 'API Name',
      width: 160,
    },
    {
      editRender: {
        name: 'AInput',
        props: { autocomplete: 'off', maxlength: 20 },
      },
      field: 'APIDescription',
      title: 'Description',
      width: 160,
    },
    {
      editRender: {
        name: 'ASelect',
        props: {
          options: [
            { label: 'True', value: true },
            { label: 'False', value: false },
          ],
        },
      },
      field: 'IsActived',
      title: 'IsActived',
      width: 160,
    },
    {
      editRender: {
        name: 'AInput',
        props: { autocomplete: 'off', maxlength: 60 },
      },
      field: 'ParamsIF',
      title: 'SubParams_if',
      minWidth: 200,
    },
    {
      editRender: {
        name: 'AInput',
        props: { autocomplete: 'off', maxlength: 32 },
      },
      field: 'ParamsCallMethod',
      title: 'SubParams_callmethod',
      minWidth: 200,
    },
    { slots: { default: 'action' }, title: 'Action', width: 160 },
  ],
  customConfig: {
    storage: false,
  },
  editConfig: {
    mode: 'row',
    trigger: 'manual',
    autoClear: false,
    showStatus: true,
  },
  editRules: {
    APIDescription: [{ required: true, message: 'Description is required' }],
    ParamsIF: [{ required: true, message: 'SubParams_if is required' }],
    ParamsCallMethod: [
      { required: true, message: 'SubParams_callmethod is required' },
    ],
  },
  keepSource: true,
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    ajax: {
      query: async () => {
        const res = await getLisApiList();
        return res;
      },
    },
  },
  rowConfig: {
    useKey: true,
    keyField: 'Guid',
  },
  toolbarConfig: {
    enabled: false,
  },
};
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });
async function onSubmit(values: Record<string, any>) {
  const check = await baseFormApi.validate();
  if (check.valid) {
    submitLoading.value = true;
    lisApiPubUpdate(Object.assign({ env: env.value }, values))
      .then(() => {
        message.success('操作成功');
      })
      .catch(() => {})
      .finally(() => {
        setTimeout(() => {
          submitLoading.value = false;
        }, 500);
      });
  } else {
    message.info('必填项未填写完整，请检查！');
  }
}

function hasEditStatus(row: any) {
  return gridApi.grid?.isEditByRow(row);
}

function editRowEvent(row: any) {
  gridApi.grid?.setEditRow(row);
}

async function saveRowEvent(row: any) {
  const errMap = await gridApi.grid?.validate();
  if (errMap) {
    return;
  }
  gridApi.setLoading(true);
  lisApiUpdate(Object.assign({ id: row.Guid }, row))
    .then(() => {
      gridApi.grid?.clearEdit();
      handleSuccess();
      message.success('操作成功');
    })
    .catch(() => {})
    .finally(() => {
      gridApi.setLoading(false);
    });
}

const cancelRowEvent = (row: any) => {
  if (row.Guid === 'New') {
    gridApi.grid?.remove(row);
  } else {
    gridApi.grid?.clearEdit();
    gridApi.grid?.revertData(row);
  }
};
function handleSuccess() {
  gridApi.grid.commitProxy('query');
}

async function fetch() {
  const res = await getLisApiPubData();
  baseFormApi.setValues(res.pub);
  env.value = res.pub?.ENV;
  mimeOptions.value = res.mime;
  baseFormApi.updateSchema([
    {
      componentProps: {
        options: mimeOptions.value,
      },
      fieldName: 'Accept',
    },
  ]);
  baseFormApi.updateSchema([
    {
      componentProps: {
        options: mimeOptions.value,
      },
      fieldName: 'ContentType',
    },
  ]);
}
onMounted(() => {
  fetch();
});
</script>

<template>
  <Page>
    <Card>
      <template #title> LIS API Configurations </template>
      <template #extra>
        <a-button
          :loading="submitLoading"
          type="primary"
          @click="baseFormApi.submitForm()"
        >
          Save
        </a-button>
      </template>

      <Form />
    </Card>
    <Card>
      <template #title>API List </template>
      <template #extra> </template>
      <Grid>
        <template #action="{ row }">
          <template v-if="hasEditStatus(row)">
            <a-button type="link" @click="saveRowEvent(row)">Save</a-button>
            <a-button type="link" @click="cancelRowEvent(row)">Cancel</a-button>
          </template>
          <template v-else>
            <a-button type="link" @click="editRowEvent(row)">Edit</a-button>
          </template>
        </template>
      </Grid>
    </Card>
  </Page>
</template>
