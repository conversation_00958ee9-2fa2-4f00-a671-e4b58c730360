<script setup lang="ts">
import type { VxeGridListeners, VxeGridProps } from '#/adapter';

import { defineProps, reactive } from 'vue';
import { useRouter } from 'vue-router';

import { useVbenDrawer, useVbenModal } from '@vben/common-ui';
import { AntClear } from '@vben/icons';

import { Popconfirm, Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import { getOutDispatchItem } from '#/api/bondedwarehouse/outbound';
import batchDrawer from '#/views/sapneo/master/batch/batchDrawer.vue';
import skuDrawer from '#/views/sapneo/master/product/skuDrawer.vue';

import palletLoadingModal from './palletLoadingModal.vue';
import palletRemoveDrawer from './palletRemoveDrawer.vue';

/* import csarDrawer from './CSARDrawer.vue'; */

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
  doc: {
    type: String,
    default: '',
  },
  isClosed: {
    type: Boolean,
    default: false,
  },
});
const router = useRouter();
const [PalletLoadingModal, PalletLoadingModalApi] = useVbenModal({
  connectedComponent: palletLoadingModal,
});
const [PalletRemoveDrawer, palletRemoveDrawerApi] = useVbenDrawer({
  connectedComponent: palletRemoveDrawer,
});
const [SkuDrawer, skuDrawerApi] = useVbenDrawer({
  connectedComponent: skuDrawer,
});
const [BatchDrawer, batchDrawerApi] = useVbenDrawer({
  connectedComponent: batchDrawer,
});
function openSkuDrawer(id: string) {
  skuDrawerApi.setData({ id });
  skuDrawerApi.open();
}
function openBatchDrawer(sku: string, batch: string) {
  batchDrawerApi.setData({ sku, batch });
  batchDrawerApi.open();
}
function openPalletRemoveDrawer() {
  palletRemoveDrawerApi.setData({ id: props.id });
  palletRemoveDrawerApi.open();
}
function openPalletLoadingModa() {
  PalletLoadingModalApi.setData({ id: props.id });
  PalletLoadingModalApi.open();
}
/* const [CSARDrawer, CSARDrawerApi] = useVbenDrawer({
  connectedComponent: csarDrawer,
});
function openCSARDrawer(id: string) {
  CSARDrawerApi.setData({ id });
  CSARDrawerApi.open();
} */

const gridOptions = reactive<VxeGridProps>({
  id: 'BWOutDispatchItem',
  /* checkboxConfig: { highlight: true, range: true }, */
  columns: [
    /* { type: 'checkbox', width: 48, fixed: 'left' }, */
    {
      fixed: 'left',
      title: '#',
      type: 'seq',
      width: 60,
    },

    { field: 'NewPalletNo', title: '出库托盘', width: 100 },
    { field: 'NewSSCC', title: '出库SSCC', width: 150 },
    { field: 'PalletType', title: '托盘类型', width: 84 },
    {
      field: 'YUB',
      title: 'SAP销售订单',
      width: 96,
      filters: [{ data: { vals: [], sVal: '' } }],
      filterRender: {
        name: 'FilterContent',
      },
    },
    {
      field: 'Outbound',
      title: 'Outbound',
      width: 88,
      filters: [{ data: { vals: [], sVal: '' } }],
      filterRender: {
        name: 'FilterContent',
      },
    },
    {
      field: 'MatCode',
      title: '物料编号',
      width: 80,
      filters: [{ data: { vals: [], sVal: '' } }],
      filterRender: {
        name: 'FilterContent',
      },
      slots: { default: 'material' },
    },
    {
      field: 'isBatchManagementRequired',
      title: '批次管理',
      width: 72,
      align: 'center',
      slots: { default: 'batchmanage' },
    },
    {
      field: 'BatchNo',
      title: '批号',
      width: 80,
      filters: [{ data: { vals: [], sVal: '' } }],
      filterRender: {
        name: 'FilterContent',
      },
      slots: { default: 'batch' },
    },
    {
      field: 'BatchStatus',
      title: '批次状态',
      width: 88,
      slots: { default: 'batchStatus' },
    },
    { field: 'ActualExpiration', title: '限用日期', width: 88 },
    { field: 'GoodsNum', title: '产品数量', width: 80 },
    {
      field: 'DeliveryGoodsType',
      title: '产品分类',
      width: 88,
      filters: [
        { label: '正常', value: '正常' },
        { label: '留样', value: '留样' },
        { label: 'CIQ', value: 'CIQ' },
        { label: '搁置', value: '搁置' },
      ],
    },
    { field: 'SSCC', title: '分类SSCC', width: 150 },
    {
      field: 'CreateDate',
      title: '装载日期',
      width: 136,
      formatter: 'formatDateTime',
    },
    /* { field: 'BLNo', title: '出库订单', width: 160 }, */
    {
      field: 'PickingDocNo',
      title: '拣货单',
      width: 180,
      slots: { default: 'pickingDoc' },
    },
    {
      field: 'OutboundPGIStatus',
      title: 'PGI状态',
      width: 76,
      slots: { default: 'pgistatus' },
    },
    { field: 'oBrandName', title: '品牌', width: 100 },
    { field: 'oCName', title: '中文品名', minWidth: 240 },
  ],
  filterConfig: {
    remote: false,
  },
  footerRowStyle: { 'font-weight': 'bold ' },
  footerMethod({ columns, data }) {
    return [
      columns.map((column, columnIndex) => {
        if (columnIndex === 0) {
          return `合计`;
        }
        if (columnIndex === 1) {
          return `${data.length} 项`;
        }
        if (['GoodsNum'].includes(column.field)) {
          return sumNum(data, column.field);
        }
        if (['NewSSCC'].includes(column.field)) {
          const palltes = [
            ...new Set(
              data.map((item) => item.NewSSCC).filter((item) => item !== ''),
            ),
          ];
          return `托盘数: ${palltes.length}`;
        }
        return null;
      }),
    ];
  },
  height: 'auto',
  keepSource: true,
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    ajax: {
      query: async () => {
        return await getOutDispatchItem(props.id);
      },
    },
    seq: true,
  },
  showFooter: true,
  size: 'mini',
  sortConfig: {
    remote: false,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
  scrollX: { enabled: true, gt: 0 },
  scrollY: { enabled: true, mode: 'wheel', gt: 30, oSize: 0 },
  menuConfig: {
    trigger: 'cell',
    body: {
      options: [
        [
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuCopy',
            disabled: false,
            name: '复制单元格',
            prefixConfig: {
              icon: 'vxe-icon-copy',
            },
            visible: true,
          },
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuRefresh',
            disabled: false,
            name: '刷新',
            prefixConfig: { icon: 'vxe-icon-refresh' },
            visible: true,
          },
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuExport',
            disabled: false,
            name: '导出明细',
            prefixConfig: { icon: 'vxe-icon-download' },
            visible: true,
          },
        ],
      ],
    },
    className: 'font-medium shadow rounded-md',
  },
});
const gridEvents: VxeGridListeners = {
  /* cellDblclick({ row }) {
    openCSARDrawer(row.Guid);
  }, */
  menuClick({ menu }) {
    switch (menu.code) {
      case 'menuExport': {
        handleExport();
        break;
      }
      default: {
        break;
      }
    }
  },
};
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions, gridEvents });
function sumNum(list: any[], field: string) {
  let count = 0;
  list.forEach((item) => {
    count += Number(item[field]);
  });
  return count;
}
function handleExport() {
  gridApi.grid.exportData({
    filename: `装车明细_${props.doc}`,
    sheetName: 'Sheet1',
    type: 'xlsx',
    isFooter: false,
    columnFilterMethod: ({ column }) => {
      return column.field !== undefined;
    },
  });
}
function handleSuccess() {
  gridApi.grid.commitProxy('query');
}
/* function handleRemoveCheck() {
  if (props.isClosed) {
    message.error(`调整单据已执行，无法移除明细！`);
    return;
  }
  const checkedRecords = gridApi.grid
    .getCheckboxRecords()
    .map((item: any) => item.Guid);
  if (checkedRecords.length === 0) {
    message.info(`请勾选需要移除的调整明细！`);
    return;
  }
  Modal.confirm({
    content: `请确认是否移除勾选的调整明细？`,
    onCancel() {},
    onOk() {
      handleDelete(checkedRecords);
    },
    title: `移除调整明细`,
    okButtonProps: { danger: true },
  });
} */
/* function handleDelete(ids: Array<string>) {
  stockMovementItemDelete(props.id, ids)
    .then(() => {
      handleSuccess();
    })
    .catch(() => {})
    .finally(() => {});
} */

function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
}
</script>

<template>
  <div class="flex h-full flex-col">
    <Grid>
      <template #toolbar_left>
        <div class="hidden pl-1 text-base font-medium md:block">
          <span class="mr-2">装车明细</span>
        </div>
        <a-button class="mr-1" size="small" @click="clearFilterAndSort()">
          <AntClear class="size-5" />
        </a-button>
        <a-button
          :disabled="props.isClosed"
          class="ml-2"
          size="small"
          type="primary"
          @click="openPalletLoadingModa()"
        >
          装载托盘
        </a-button>
        <a-button
          :disabled="props.isClosed"
          class="ml-2"
          size="small"
          @click="openPalletRemoveDrawer()"
        >
          卸载托盘
        </a-button>
      </template>
      <template #toolbar-tools> </template>
      <template #pickingDoc="{ row }">
        <Popconfirm
          cancel-text="取消"
          ok-text="查看拣货单"
          title="跳转提示"
          @confirm="
            router.push({
              name: 'BWOutPickingDetail',
              params: { id: row.PickingHeadID },
            })
          "
        >
          <a class="text-blue-500" @click.prevent>
            {{ row.PickingDocNo }}
          </a>
        </Popconfirm>
      </template>
      <template #pgistatus="{ row }">
        <Tag :color="row.OutboundPGIStatus ? 'green' : 'red'">
          {{ row.OutboundPGIStatus ? '是' : '否' }}
        </Tag>
      </template>
      <template #material="{ row }">
        <a
          v-if="row.MatCode"
          class="text-blue-500"
          @click="openSkuDrawer(row.MatCode)"
        >
          {{ row.MatCode }}
        </a>
      </template>
      <template #batchmanage="{ row }">
        <Tag :color="row.isBatchManagementRequired ? 'green' : 'red'">
          {{ row.isBatchManagementRequired ? '是' : '否' }}
        </Tag>
      </template>
      <template #batch="{ row }">
        <a
          v-if="row.BatchNo"
          class="text-blue-500"
          @click="openBatchDrawer(row.MatCode, row.BatchNo)"
        >
          {{ row.BatchNo }}
        </a>
      </template>
      <template #batchStatus="{ row }">
        <Tag
          :color="
            row.BatchStatus === 0
              ? 'green'
              : row.BatchStatus === 1
                ? 'orange'
                : 'red'
          "
        >
          {{
            row.BatchStatus === 0
              ? '正常'
              : row.BatchStatus === 1
                ? '限制'
                : '不存在'
          }}
        </Tag>
      </template>
      <!-- <template #batchmanage="{ row }">
        <Tag :color="row.isBatchManagementRequired ? 'green' : 'blue'">
          {{ row.isBatchManagementRequired ? '是' : '否' }}
        </Tag>
      </template> -->
    </Grid>
    <PalletLoadingModal @success="handleSuccess()" />
    <PalletRemoveDrawer @success="handleSuccess()" />
    <SkuDrawer />
    <BatchDrawer />
  </div>
</template>
