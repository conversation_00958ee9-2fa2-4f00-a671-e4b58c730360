<script lang="ts" setup>
import type { VxeGridProps } from '#/adapter';

import { reactive, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { AntClear } from '@vben/icons';

import { Modal as AModal, message, Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import {
  getBWStockDetailList,
  stockTakingMissingItemImport,
} from '#/api/bondedwarehouse/stock';
import { getOptions } from '#/api/common';

const emit = defineEmits(['success']);

const data = ref();

const [Modal, modalApi] = useVbenModal({
  class: 'w-full h-full',
  destroyOnClose: true,
  fullscreenButton: true,
  closeOnClickModal: false,
  /* footer: false, */
  zIndex: 900,
  confirmText: '批量添加',
  onCancel() {
    handleClose();
  },
  onConfirm: async () => {
    await handleImport();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      data.value = modalApi.getData<Record<string, any>>();
      fetch();
    }
  },
  onBeforeClose() {
    emit('success');
    return true;
  },
  title: '库存盘点 - 贴标少',
});
const gridOptions = reactive<VxeGridProps>({
  id: 'StockTakingItemAdd',
  checkboxConfig: {
    highlight: true,
    range: true,
    checkMethod: ({ row }) => {
      return !(row.LockedByTaking || row.LockedByMovement);
    },
  },
  columns: [
    { type: 'checkbox', width: 40 },
    /* {
      field: 'AreaName',
      title: '库区',
      width: 100,
    }, */
    {
      field: 'StockStatus',
      title: '库存状态',
      width: 88,
      filters: [],
    },
    {
      field: 'StorageLocation',
      title: 'SAP位置',
      width: 96,
    },
    {
      field: 'MatCode',
      title: '物料编号',
      width: 96,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'MaterialType',
      title: '物料类型',
      width: 88,
      filters: [],
    },
    /* {
      field: 'isBatchManagementRequired',
      title: '批次管理',
      width: 88,
      align: 'center',
      slots: { default: 'batchmanage' },
      filters: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      filterMultiple: false,
    }, */
    {
      field: 'BatchNo',
      title: '批号',
      width: 80,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    /* {
      field: 'BatchStatus',
      title: '批次状态',
      width: 88,
      slots: { default: 'batchstatus' },
      filters: [
        { label: '正常', value: 0 },
        { label: '限制', value: 1 },
        { label: '不存在', value: -1 },
      ],
    }, */
    /* {
      field: 'ShelfLifeExpirationDate',
      title: '批次效期',
      width: 108,
      formatter: 'formatDate',
      sortable: true,
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'ActualExpiration',
      title: '实物效期',
      width: 88,
      formatter: 'formatDate',
    }, */
    { field: 'StockQty', title: '库存数量', width: 108 },
    { field: 'UnrestrictedQty', title: '非限制库存', width: 108 },
    { field: 'BlockedQty', title: '冻结库存', width: 100 },
    /* {
      field: 'HBL',
      title: 'HBL',
      minWidth: 136,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    }, */
    {
      field: 'BLNo',
      title: 'BLNo',
      minWidth: 136,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'PalletNo',
      title: '入库托盘',
      width: 100,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },

    {
      field: 'BIN',
      title: '货位',
      width: 88,
    },
    {
      field: 'TallyDate',
      title: '理货日期',
      width: 108,
      formatter: 'formatDate',
      sortable: true,
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'CreateDate',
      title: '调整日期',
      width: 108,
      formatter: 'formatDate',
      sortable: true,
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    /* {
      field: 'IsWorking',
      title: '正操作',
      width: 80,
      align: 'center',
      filters: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      filterMultiple: false,
      slots: { default: 'isworking' },
    }, */
    {
      field: 'LockedByMovement',
      title: '调整锁定',
      width: 88,
      filters: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      filterMultiple: false,
      formatter({ cellValue }) {
        return cellValue ? '是' : '否';
      },
    },
    {
      field: 'LockedByTaking',
      title: '盘点锁定',
      width: 88,
      filters: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      filterMultiple: false,
      formatter({ cellValue }) {
        return cellValue ? '是' : '否';
      },
    },
    {
      field: 'LockedByAPI',
      title: 'API锁定',
      width: 88,
      filters: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      filterMultiple: false,
      formatter({ cellValue }) {
        return cellValue ? '是' : '否';
      },
    },
    /*  {
      title: '操作',
      slots: { default: 'action' },
      width: 72,
      align: 'center',
      fixed: 'right',
    }, */
  ],
  expandConfig: {
    accordion: true,
  },
  filterConfig: {
    remote: true,
  },
  height: 'auto',
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    ajax: {
      query: async ({ filters }) => {
        const queryParams: any = Object.assign({
          WarehouseCode: data.value.warehouse,
          OwnerCode: data.value.owner,
          StorageLocation: 'SLB4',
          LockedByTaking: false,
          currentPage: 1,
          pageSize: 9999,
          sort: '_Identify',
        });
        filters.forEach(({ field, values }) => {
          queryParams[field] = values.join(',');
        });
        const res = await getBWStockDetailList(queryParams);
        return res.items;
      },
    },
    seq: true,
  },
  /* scrollY: { enabled: true, gt: 0 }, */
  size: 'mini',
  sortConfig: {
    remote: true,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
});

const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });

function handleImport() {
  const checkedRecords = gridApi.grid
    .getCheckboxRecords()
    .map((item: any) => item.Guid);
  if (checkedRecords.length === 0) {
    message.info(`请勾选需要添加的库存明细`);
    return;
  }

  AModal.confirm({
    title: '批量操作确认',
    content: `系统将直接完成贴标少库存复盘作业`,
    okText: '确定',
    cancelText: '取消',
    okButtonProps: { danger: true },
    async onOk() {
      modalApi.setState({ confirmLoading: true });
      modalApi.setState({ loading: true });
      try {
        await stockTakingMissingItemImport(data.value.id, checkedRecords);
        handleClose();
      } catch {
        // 错误处理
      } finally {
        modalApi.setState({ loading: false });
        modalApi.setState({ confirmLoading: false });
      }
    },
  });
}
function handleClose() {
  modalApi.close();
  emit('success');
}

async function fetch() {
  const options = await getOptions(`StockTaking_${data.value.warehouse}`);
  const fieldList = [
    'AreaName',
    'StockStatus',
    'StorageLocation',
    'MaterialType',
    'BrandName',
    'Division',
  ];
  fieldList.forEach((field) => {
    gridApi.grid.setFilter(
      field,
      options.filter((item: any) => {
        return item.type === field;
      }),
    );
  });
}
function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
  gridApi.grid.commitProxy('query');
}
</script>
<template>
  <Modal>
    <Grid>
      <template #toolbar_left>
        <a-button class="mr-1" @click="clearFilterAndSort()">
          <AntClear class="size-5" />
        </a-button>
      </template>
      <template #toolbar-tools> </template>
      <template #batchmanage="{ row }">
        <Tag :color="row.isBatchManagementRequired ? 'green' : 'red'">
          {{ row.isBatchManagementRequired ? '是' : '否' }}
        </Tag>
      </template>

      <!-- <template #isworking="{ row }">
        <Tag :color="row.IsWorking ? 'blue' : 'green'">
          {{ row.IsWorking ? '是' : '否' }}
        </Tag>
      </template> -->
    </Grid>
  </Modal>
</template>
