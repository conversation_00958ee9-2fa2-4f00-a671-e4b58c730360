<script setup lang="ts">
import type { VxeGridListeners, VxeGridProps } from '#/adapter';

import { onMounted, reactive } from 'vue';
import { useRouter } from 'vue-router';

import { Page, useVbenModal } from '@vben/common-ui';
import { AntClear, AntDelete } from '@vben/icons';

import { message, Modal, Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import {
  bwInOrderDelete,
  getBWInOrderList,
} from '#/api/bondedwarehouse/inbound';
import { getOptions } from '#/api/common';

import createModal from './createModal.vue';

const router = useRouter();
const [CreateModal, createModalApi] = useVbenModal({
  connectedComponent: createModal,
});
const searchField: any = reactive({
  HBL: undefined,
});

const gridOptions = reactive<VxeGridProps>({
  id: 'BWInOrder',
  columns: [
    {
      fixed: 'left',
      title: '#',
      type: 'seq',
      width: 60,
    },
    {
      field: 'HBL',
      title: '入库提单HBL',
      minWidth: 136,
      fixed: 'left',
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'IsClosed',
      title: '单据状态',
      minWidth: 88,
      formatter({ cellValue }) {
        return cellValue ? '已关闭' : '未关闭';
      },
      filters: [
        { label: '已关闭', value: true },
        { label: '未关闭', value: false },
      ],
      filterMultiple: false,
      slots: { default: 'docStatus' },
    },
    {
      field: 'InStatus',
      title: '是否进仓',
      minWidth: 88,
      formatter({ cellValue }) {
        return cellValue ? '已进仓' : '未进仓';
      },
      filters: [
        { label: '已进仓', value: true },
        { label: '未进仓', value: false },
      ],
      filterMultiple: false,
      slots: { default: 'inStatus' },
    },
    {
      field: 'ReceivingStatus',
      title: '收货状态',
      minWidth: 88,
      filters: [
        { label: '待收货', value: '待收货' },
        { label: '部分收', value: '部分收' },
        { label: '已收货', value: '已收货' },
      ],
      /* filterMultiple: false, */
      /* slots: { default: 'storestatus' }, */
    },
    { field: 'OwnerShortName', title: '货主', minWidth: 100, filters: [] },
    { field: 'WarehouseName', title: '仓库', minWidth: 100, filters: [] },
    {
      field: 'GoodsNum',
      title: '产品总数',
      minWidth: 108,
      filters: [{ data: { type: '0', num1: undefined, num2: undefined } }],
      filterRender: { name: 'FilterNumberRange' },
    },
    { field: 'PalletCount', title: '托盘数', minWidth: 64, visible: false },
    {
      field: 'PreInDate',
      title: '预计进仓日',
      minWidth: 100,
      formatter: 'formatDate',
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'InDate',
      title: '进仓日期',
      minWidth: 88,
      formatter: 'formatDate',
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'ETA',
      title: 'ETA',
      minWidth: 88,
      formatter: 'formatDate',
    },
    {
      field: 'ATA',
      title: 'ATA',
      minWidth: 88,
      formatter: 'formatDate',
      visible: false,
    },
    {
      field: 'DeclareDate',
      title: '申报日期',
      minWidth: 88,
      formatter: 'formatDate',
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'EntryID',
      title: '入库报关单号',
      minWidth: 150,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'ContractNo',
      title: '合同号',
      minWidth: 120,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'JobNo',
      title: '业务编号',
      minWidth: 120,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    { field: 'TotalBoxNum', title: '箱数', minWidth: 100, visible: false },
    {
      field: 'TotalGrossWeightInKg',
      title: '毛重',
      minWidth: 100,
      visible: false,
    },
    {
      field: 'TotalNetWeightInKg',
      title: '净重',
      minWidth: 100,
      visible: false,
    },
    { field: 'TotalVolumeInM3', title: '体积', minWidth: 100, visible: false },
    { field: 'CargoType', title: '货物类型', minWidth: 100, visible: false },
    { field: 'WrapType', title: '包装类型', minWidth: 100, visible: false },
    { field: 'TrafMode', title: '运输方式', minWidth: 100, visible: false },
    { field: 'EntyPort', title: '入境口岸', minWidth: 100, visible: false },
    {
      field: 'Remark',
      title: '备注',
      minWidth: 150,
      visible: false,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'CreateBy',
      title: '创建人',
      width: 100,
      visible: false,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'CreateDate',
      filterRender: { name: 'FilterDateRange' },
      filters: [{ data: { date: [], type: '0' } }],
      formatter: 'formatDateTime',
      title: '创建日期',
      width: 136,
    },
    {
      field: 'UpdateBy',
      title: '更新人',
      width: 100,
      visible: false,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'UpdateDate',
      filterRender: { name: 'FilterDateRange' },
      filters: [{ data: { date: [], type: '0' } }],
      formatter: 'formatDateTime',
      title: '更新日期',
      width: 136,
      visible: false,
    },
    {
      field: 'CloseBy',
      title: '关单人',
      width: 100,
      visible: false,
      filters: [{ data: { type: '0', text: undefined } }],
      filterRender: { name: 'FilterInputText' },
    },
    {
      field: 'CloseDate',
      title: '关闭日期',
      width: 136,
      formatter: 'formatDateTime',
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
      visible: false,
    },
    {
      field: 'Delete',
      align: 'center',
      slots: { default: 'action' },
      title: '删除',
      width: 60,
    },
  ],
  filterConfig: {
    remote: true,
  },
  height: 'auto',
  proxyConfig: {
    autoLoad: true,
    ajax: {
      query: ({ filters, page, sorts }) => {
        const queryParams: any = Object.assign({}, page);
        // 处理排序
        if (sorts.length === 0) {
          queryParams.sort = '_Identify desc';
        } else {
          const sortItem = sorts.map((item) => {
            const newItem = `${item.field} ${item.order}`;
            return newItem;
          });
          queryParams.sort = sortItem.join(',');
        }
        // 处理筛选
        Object.getOwnPropertyNames(searchField).forEach((key) => {
          searchField[key] = undefined;
        });
        filters.forEach(({ field, values }) => {
          queryParams[field] = values.join(',');
          if (Object.prototype.hasOwnProperty.call(searchField, field)) {
            const jsonObject = JSON.parse(values.toString());
            searchField[field] = jsonObject.text;
          }
        });
        return getBWInOrderList(queryParams);
      },
    },
    seq: true,
  },
  size: 'mini',
  sortConfig: {
    remote: true,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
});

const gridEvents: VxeGridListeners = {
  cellDblclick({ row }) {
    router.push({
      name: 'BWInOrderDetail',
      params: { id: row.Guid },
    });
  },
};
const [Grid, gridApi] = useVbenVxeGrid({ gridEvents, gridOptions });

function searchEvent(field: string) {
  const column = gridApi.grid.getColumnByField(field);
  if (column) {
    // 修改第一个选项为勾选状态
    const option = column.filters[0];
    if (!option) {
      return;
    }

    if (searchField[field]) {
      option.data = { type: '1', text: searchField[field] };
      option.value = JSON.stringify(option.data);
      option.checked = true;
    } else {
      option.data = { type: '0', text: undefined };
      option.checked = false;
    }

    // 修改条件之后，需要手动调用 updateData 处理表格数据
    gridApi.grid.updateData();
    gridApi.grid.commitProxy('query');
  }
}
function openCreateModal() {
  createModalApi.open();
}
function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
  gridApi.grid.commitProxy('query');
}
function handleSuccess() {
  gridApi.grid.commitProxy('query');
}
function handleDelete(id: string) {
  bwInOrderDelete(id)
    .then(() => {
      message.success('操作成功');
      handleSuccess();
    })
    .catch(() => {})
    .finally(() => {});
}

function showDeleteModal(id: string, name: string) {
  Modal.confirm({
    content: `请确认是否删除？`,
    /* icon: createVNode(ExclamationCircleOutlined), */
    onCancel() {},
    onOk() {
      handleDelete(id);
    },
    title: `HBL: ${name}`,
    okButtonProps: { danger: true },
  });
}
async function fetch() {
  const options = await getOptions('Owner&Warehouse');
  const fieldList = ['OwnerShortName', 'WarehouseName'];
  fieldList.forEach((field) => {
    gridApi.grid.setFilter(
      field,
      options.filter((item: any) => {
        return item.type === field;
      }),
    );
  });
}
onMounted(() => {
  setTimeout(() => {
    fetch();
  }, 300);
});
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar_left>
        <a-button class="mr-1" @click="clearFilterAndSort()">
          <AntClear class="size-5" />
        </a-button>
        <div class="hidden pl-1 md:block">
          <!-- <a-select
            v-model:value="selectField.IsClosed"
            allow-clear
            class="mr-2 w-32"
            placeholder="单据状态"
            @change="searchEvent('IsClosed', 'Select')"
          >
            <a-select-option :value="true">已关闭</a-select-option>
            <a-select-option :value="false">未关闭</a-select-option>
          </a-select>
          <a-select
            v-model:value="selectField.InStatus"
            allow-clear
            class="mr-2 w-32"
            placeholder="进仓状态"
            @change="searchEvent('InStatus', 'Select')"
          >
            <a-select-option :value="true">已进仓</a-select-option>
            <a-select-option :value="false">未进仓</a-select-option>
          </a-select> -->
          <a-input-search
            v-model:value="searchField.HBL"
            allow-clear
            class="mr-2 w-60"
            placeholder="HBL"
            @search="searchEvent('HBL')"
          />
        </div>
      </template>
      <template #toolbar-tools>
        <a-button class="mr-3" type="primary" @click="openCreateModal()">
          创建入库订单
        </a-button>
      </template>
      <template #action="{ row }">
        <a-button
          danger
          size="small"
          type="link"
          @click="showDeleteModal(row.Guid, row.HBL)"
        >
          <AntDelete class="size-5" />
        </a-button>
      </template>
      <template #docStatus="{ row }">
        <Tag :color="row.IsClosed ? 'green' : 'blue'">
          {{ row.IsClosed ? '已关闭' : '未关闭' }}
        </Tag>
      </template>
      <template #inStatus="{ row }">
        <Tag :color="row.InStatus ? 'green' : 'blue'">
          {{ row.InStatus ? '已进仓' : '未进仓' }}
        </Tag>
      </template>
    </Grid>
    <CreateModal @success="handleSuccess" />
  </Page>
</template>
