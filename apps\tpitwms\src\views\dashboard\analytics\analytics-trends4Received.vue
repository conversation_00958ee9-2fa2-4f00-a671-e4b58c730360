<script lang="ts" setup>
import type { EchartsUIType } from '@vben/plugins/echarts';

import { onMounted, ref } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

import { getTrends4Received } from '#/api/common/web';

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);

onMounted(() => {
  getTrends4Received()
    .then((res) => {
      renderEcharts({
        grid: {
          bottom: 0,
          containLabel: true,
          left: '1%',
          right: '1%',
          top: '24',
        },
        legend: {
          data: ['收货总数', 'IBD批次差异', '正装', '促销品', '工具'],
          selected: {
            收货总数: true,
            IBD批次差异: true,
            正装: false,
            促销品: false,
            工具: false,
          },
        },
        series: [
          {
            areaStyle: {},
            data: res.seriesTotal,
            itemStyle: {
              color: '#5ab1ef',
            },
            name: '收货总数',
            smooth: true,
            type: 'line',
          },
          {
            areaStyle: {},
            data: res.seriesBatchDiff,
            itemStyle: {},
            name: 'IBD批次差异',
            smooth: true,
            type: 'line',
          },
          {
            areaStyle: {},
            data: res.seriesYFG,
            itemStyle: {},
            name: '正装',
            smooth: true,
            type: 'line',
          },
          {
            areaStyle: {},
            data: res.seriesYSM2,
            itemStyle: {},
            name: '促销品',
            smooth: true,
            type: 'line',
          },
          {
            areaStyle: {},
            data: res.seriesYPL2,
            itemStyle: {},
            name: '工具',
            smooth: true,
            type: 'line',
          },
        ],
        tooltip: {
          axisPointer: {
            lineStyle: {
              color: '#019680',
              width: 1,
            },
          },
          trigger: 'axis',
        },
        // xAxis: {
        //   axisTick: {
        //     show: false,
        //   },
        //   boundaryGap: false,
        //   data: Array.from({ length: 18 }).map((_item, index) => `${index + 6}:00`),
        //   type: 'category',
        // },
        xAxis: {
          axisTick: {
            show: false,
          },
          boundaryGap: false,
          data: res.xAxis /* Array.from({ length: 18 }).map(
            (_item, index) => `${index + 6}:00`,
          ), */,
          splitLine: {
            lineStyle: {
              type: 'solid',
              width: 1,
            },
            show: true,
          },
          type: 'category',
        },
        yAxis: [
          {
            axisTick: {
              show: false,
            },
            /* max: 80_000, */
            splitArea: {
              show: true,
            },
            splitNumber: 4,
            type: 'value',
          },
        ],
        // 添加响应式配置，在小屏幕下隐藏图例
        media: [
          {
            query: {
              maxWidth: 768, // 小屏幕断点，可根据需要调整
            },
            option: {
              legend: {
                show: false, // 小屏幕下隐藏图例
              },
            },
          },
        ],
      });
    })
    .catch(() => {})
    .finally(() => {});
});
</script>

<template>
  <EchartsUI ref="chartRef" />
</template>
