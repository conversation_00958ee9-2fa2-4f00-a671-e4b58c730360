<script lang="ts" setup>
import { onActivated, onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';

import { Page, useVbenModal, VbenButton } from '@vben/common-ui';
import { useTabs } from '@vben/hooks';
import { AntCaretDown, AntCaretUp } from '@vben/icons';
import { useTabbarStore } from '@vben/stores';

import { Card, message, Modal, TabPane, Tabs } from 'ant-design-vue';

import { useVbenForm } from '#/adapter';
import {
  bwInReturnClosing,
  bwInReturnHeadUpdate,
  getInReturnHead,
} from '#/api/bondedwarehouse/inbound';

import inConfirmModal from './InConfirmModal.vue';
import TabInspection from './tabInspection.vue';
import TabReceiving from './tabReceiving.vue';

const headData = ref({}) as any;
const init = ref(false);
const showForm = ref(true);
const submitLoading = ref(false);
const loadingRef = ref(false);
const activeKey = ref('Receiving');
const route = useRoute();
const { setTabTitle } = useTabs();
const id = route.params?.id?.toString() ?? '';

const [InConfirmModal, InConfirmModalApi] = useVbenModal({
  connectedComponent: inConfirmModal,
});

function openInConfirmModal() {
  InConfirmModalApi.setData({ id, doc: headData.value.ReturnDoc });
  InConfirmModalApi.open();
}

const [Form, baseFormApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
    formItemClass: 'pb-1',
    labelClass: 'w-28',
  },
  compact: true,
  // 提交函数
  handleSubmit: onSubmit,
  layout: 'horizontal',
  schema: [
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'OwnerShortName',
      label: '货主简称',
      disabled: true,
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'WarehouseName',
      label: '仓库',
      disabled: true,
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'InAreaName',
      label: '进仓区域',
      disabled: true,
    },
    {
      component: 'DatePicker',
      componentProps: {
        style: { width: '100%' },
        valueFormat: 'YYYY-MM-DD',
      },
      fieldName: 'InDate',
      label: '进仓日期',
      disabled: true,
    },
    {
      component: 'Input',
      componentProps: {},
      fieldName: 'ReturnDoc',
      label: '退货单号',
      disabled: true,
    },
    /* {
      component: 'Input',
      componentProps: {
        maxlength: 10,
        autocomplete: 'off',
      },
      fieldName: 'ReturnOBD',
      label: 'SAP销售订单',
    }, */
    {
      component: 'Input',
      componentProps: {
        placeholder: '请绑定',
      },
      disabled: true,
      fieldName: 'ReturnOBD',
      label: 'Return OBD',
    },
    {
      component: 'Input',
      componentProps: {
        maxlength: 128,
        autocomplete: 'off',
      },
      fieldName: 'Remark',
      label: '备注',
      formItemClass: 'col-span-1 md:col-span-2',
    },
  ],
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2 md:grid-cols-4',
});

async function onSubmit(values: Record<string, any>) {
  const check = await baseFormApi.validate();
  if (check.valid) {
    submitLoading.value = true;
    bwInReturnHeadUpdate(Object.assign({ id }, values))
      .then(() => {
        message.success('操作成功');
      })
      .catch(() => {})
      .finally(() => {
        setTimeout(() => {
          submitLoading.value = false;
        }, 500);
      });
  } else {
    message.info('必填项未填写完整，请检查！');
  }
}
function handleClose(id: string) {
  loadingRef.value = true;
  bwInReturnClosing(id)
    .then(() => {
      message.success('操作成功');
    })
    .catch(() => {})
    .finally(() => {
      fetch();
      loadingRef.value = false;
    });
}
function showCloseModal(id: string, name: string) {
  Modal.confirm({
    content: `请确认是否GR回传并关闭退货订单？`,
    onCancel() {},
    onOk() {
      handleClose(id);
    },
    title: `退货单号: ${name}`,
    okButtonProps: { danger: true },
  });
}
async function fetch() {
  const res = await getInReturnHead(id);
  headData.value = res.head;
  const tabbarStore = useTabbarStore();
  const currentTab = tabbarStore.getTabs.find(
    (item) => item.path === route.path,
  );
  if (currentTab?.meta.title === '退货操作') {
    setTabTitle(`退货操作-${res.doc}`);
  }
  baseFormApi.resetForm();
  baseFormApi.setValues(headData.value);
  init.value = false;
}
onMounted(() => {
  init.value = true;
  showForm.value = true;
  fetch();
});
onActivated(() => {
  const tabbarStore = useTabbarStore();
  const currentTab = tabbarStore.getTabs.find(
    (item) => item.path === route.path,
  );
  if (!currentTab?.meta.newTabTitle && !init.value) {
    fetch();
  }
});
</script>

<template>
  <Page auto-content-height v-loading="loadingRef">
    <div class="flex h-full flex-col">
      <Card :body-style="{ padding: '4px' }" :bordered="false" size="small">
        <template #title>
          <div class="hidden md:block">
            {{ `退货订单表头` }}
          </div>
        </template>
        <template #extra>
          <div class="flex">
            <a-button
              :danger="!headData.InStatus"
              :disabled="headData.InStatus"
              :type="headData.InStatus ? 'primary' : 'primary'"
              class="mr-2"
              @click="openInConfirmModal()"
            >
              进仓确认
            </a-button>
            <a-button
              :loading="submitLoading"
              class="mr-2"
              type="primary"
              @click="baseFormApi.submitForm()"
            >
              保存
            </a-button>
            <VbenButton
              :disabled="headData.IsClosed"
              class="mr-2"
              size="sm"
              variant="info"
              @click="showCloseModal(id, headData.ReturnDoc)"
            >
              关单
            </VbenButton>
            <AntCaretDown
              v-if="showForm"
              class="size-6"
              @click="showForm = !showForm"
            />
            <AntCaretUp v-else class="size-6" @click="showForm = !showForm" />
          </div>

          <!-- <a-button type="link" @click="showForm = !showForm"> -->
          <!-- </a-button> -->
        </template>

        <Form v-show="showForm">
          <!-- <template #ReturnOBD="slotProps">
            <a-input-search
              v-bind="slotProps"
              readonly="readonly"
              @search="openReturnOBDImportModal()"
            >
              <template #enterButton>
                <a-button class="!pl-1 !pr-1">
                  <AntSearch class="size-5" />
                </a-button>
              </template>
            </a-input-search>
          </template> -->
        </Form>
      </Card>
      <div class="inorder-tab min-h-0 flex-1">
        <Tabs v-model:active-key="activeKey" class="h-full" tab-position="left">
          <TabPane key="Receiving" tab="收货">
            <div v-if="activeKey === 'Receiving'" class="h-full">
              <TabReceiving
                :id="id"
                :doc="headData.ReturnDoc"
                :in-status="headData.InStatus"
                :is-closed="headData.IsClosed"
                :owner="headData.OwnerCode"
                @success="fetch()"
              />
            </div>
          </TabPane>
          <TabPane key="Inspection" tab="质检">
            <div v-if="activeKey === 'Inspection'" class="h-full">
              <TabInspection
                :id="id"
                :doc="headData.ReturnDoc"
                :in-status="headData.InStatus"
                :is-closed="headData.IsClosed"
                :owner="headData.OwnerCode"
              />
            </div>
          </TabPane>
        </Tabs>
      </div>
      <InConfirmModal @success="fetch()" />
    </div>
  </Page>
</template>

<style scoped lang="less">
::v-deep(.inorder-tab) {
  background-color: #fff;

  .ant-tabs-tabpane .ant-tabs-tabpane-active {
    padding-left: 0 !important;
  }

  .ant-tabs-left > .ant-tabs-nav .ant-tabs-tab {
    padding: 8px 12px;
  }

  .ant-tabs-tab-active {
    background-color: #e7f5ff;
  }

  .ant-tabs-content-holder > .ant-tabs-content > .ant-tabs-tabpane {
    padding-left: 0;
  }
  .ant-tabs-content-left {
    height: 100%;
  }
}
</style>
