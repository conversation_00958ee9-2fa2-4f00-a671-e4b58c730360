<script lang="ts" setup>
import type { VxeGridListeners, VxeGridProps } from '#/adapter';

import { reactive, ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';
import { AntClear } from '@vben/icons';

import { Modal, Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import { getBatchMappingList } from '#/api/sapneo';

const emit = defineEmits(['success']);
const data = ref();
const searchField: any = reactive({
  Batch: undefined,
});
const [Drawer, drawerApi] = useVbenDrawer({
  showCancelButton: false,
  showConfirmButton: false,
  zIndex: 900,
  onCancel() {
    drawerApi.close();
  },
  /* onConfirm: async () => {
    await handleRemove();
  }, */
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      data.value = drawerApi.getData<Record<string, any>>();
    }
  },
});
const gridOptions = reactive<VxeGridProps>({
  id: 'iBatchSelect',
  checkboxConfig: { highlight: true, range: true },
  columns: [
    /* { type: 'checkbox', width: 48, fixed: 'left' }, */
    {
      field: 'SubBatch',
      title: '子批次',
      minWidth: 88,
      filters: [{ data: null }],
      filterRender: { name: 'FilterInput' },
    },
    {
      field: 'MasterBatch',
      title: '标准批次',
      minWidth: 100,
      filters: [{ data: null }],
      filterRender: { name: 'FilterInput' },
    },
    {
      field: 'ManufactureDate',
      title: '制造日期',
      minWidth: 88,
      formatter: 'formatDate',
    },
    {
      field: 'ShelfLifeExpirationDate',
      title: '批次效期',
      minWidth: 88,
      formatter: 'formatDate',
    },
    {
      field: 'MatlBatchIsInRstrcdUseStock',
      title: '限制标记',
      minWidth: 88,
      slots: { default: 'rstrcd' },
      filters: [
        { label: '受限制', value: true },
        { label: '非限制', value: false },
      ],
      filterMultiple: false,
    },
  ],
  pagerConfig: {
    enabled: true,
    layouts: ['PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump'],
  },
  proxyConfig: {
    /* autoLoad: false, */
    ajax: {
      query: ({ filters, page, sorts }) => {
        const queryParams: any = Object.assign(
          { Material: data.value.material },
          page,
        );
        // 处理排序条件
        if (sorts.length === 0) {
          queryParams.sort = 'MasterBatch,SubBatch';
        } else {
          const sortItem = sorts.map((item) => {
            const newItem = `${item.field} ${item.order}`;
            return newItem;
          });
          queryParams.sort = sortItem.join(',');
        }
        // 处理筛选
        Object.getOwnPropertyNames(searchField).forEach((key) => {
          searchField[key] = undefined;
        });
        filters.forEach(({ field, values }) => {
          queryParams[field] = values.join(',');
          if (Object.prototype.hasOwnProperty.call(searchField, field)) {
            searchField[field] = values.toString();
          }
        });
        return getBatchMappingList(queryParams);
      },
    },
    seq: true,
  },
  filterConfig: {
    remote: true,
  },
  height: 'auto',
  size: 'mini',
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
    custom: false,
  },
  rowStyle({ row }) {
    if (row.SubBatch !== row.MasterBatch) {
      return {
        backgroundColor: '#fff4e6',
        color: '#222',
      };
    }
  },
});
const gridEvents: VxeGridListeners = {
  cellDblclick({ row }) {
    if (row.MatlBatchIsInRstrcdUseStock) {
      Modal.confirm({
        content: `该批次为限制状态，请确认是否使用？`,
        onCancel() {},
        onOk() {
          emit('success', row);
          drawerApi.close();
        },
        title: `受限提醒`,
        okButtonProps: { danger: true },
      });
    } else {
      emit('success', row);
      drawerApi.close();
    }
  },
};
const [Grid, gridApi] = useVbenVxeGrid({ gridEvents, gridOptions });

function searchEvent(field: string, type: string) {
  const column = gridApi.grid.getColumnByField(field);
  if (column) {
    // 修改第一个选项为勾选状态
    const option = column.filters[0];
    if (!option) {
      return;
    }
    if (searchField[field]) {
      option.data = searchField[field];
      option.value = option.data;
      option.checked = true;
    } else {
      if (type) {
        option.data = undefined;
        option.checked = false;
      }
    }
    // 修改条件之后，需要手动调用 updateData 处理表格数据
    gridApi.grid.updateData();
    gridApi.grid.commitProxy('query');
  }
}
function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
  gridApi.grid.commitProxy('query');
}
</script>
<template>
  <Drawer title="双击选择批次">
    <Grid>
      <template #toolbar_left>
        <a-button class="mr-1" @click="clearFilterAndSort()">
          <AntClear class="size-5" />
        </a-button>
        <div class="hidden pl-1 md:block">
          <a-input-search
            v-model:value="searchField.Batch"
            allow-clear
            class="mr-2 w-40"
            placeholder="批次"
            @search="searchEvent('Batch', 'Input')"
          />
        </div>
      </template>
      <!-- <template #toolbar-tools> </template> -->
      <template #rstrcd="{ row }">
        <Tag :color="row.MatlBatchIsInRstrcdUseStock ? 'red' : 'green'">
          {{ row.MatlBatchIsInRstrcdUseStock ? '受限制' : '非限制' }}
        </Tag>
      </template>
    </Grid>
  </Drawer>
</template>
