<script lang="ts" setup>
import type { VxeGridListeners, VxeGridProps } from '#/adapter';

import { reactive, ref } from 'vue';
/* import { AntClear } from '@vben/icons'; */

import { useVbenDrawer, useVbenModal } from '@vben/common-ui';

import { message, Tag } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter';
import { bwOutPickingOutboundBinding } from '#/api/bondedwarehouse/outbound';
import { getOutboundItem, getOutboundList } from '#/api/sapneo';
import customerDrawer from '#/views/sapneo/master/customer/customerDrawer.vue';
import skuDrawer from '#/views/sapneo/master/product/skuDrawer.vue';

const emit = defineEmits(['success']);
const [CustomerDrawer, customerDrawerApi] = useVbenDrawer({
  connectedComponent: customerDrawer,
});
const [SkuDrawer, skuDrawerApi] = useVbenDrawer({
  connectedComponent: skuDrawer,
});
function openCustomerDrawer(id: string) {
  customerDrawerApi.setData({ id });
  customerDrawerApi.open();
}
function openSkuDrawer(id: string) {
  skuDrawerApi.setData({ id });
  skuDrawerApi.open();
}
const data = ref();
const [Modal, modalApi] = useVbenModal({
  class: 'w-full h-full',
  fullscreenButton: true,
  closeOnClickModal: false,
  confirmText: '确认绑定',
  destroyOnClose: true,
  zIndex: 800,
  onCancel() {
    modalApi.close();
  },
  onConfirm: async () => {
    await handleImport();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      data.value = modalApi.getData<Record<string, any>>();
      fetch();
      /* fetch(); */
    }
  },
  title: '选择绑定Outbound',
});
const gridOptions = reactive<VxeGridProps>({
  id: 'OutboundImport',
  /* checkboxConfig: { highlight: true, range: true }, */
  radioConfig: {
    checkMethod: ({ row }) => {
      return row.WMSID === null || row.WMSID === '';
    },
  },
  columns: [
    { type: 'radio', width: 40 },
    { type: 'expand', width: 48, slots: { content: 'expand_content' } },
    {
      field: 'DeliveryDocument',
      title: 'Outbound',
      minWidth: 100,
    },
    {
      field: 'DeliveryDocumentType',
      title: '单据类型',
      minWidth: 96,
    },
    {
      field: 'SO',
      title: '销售订单',
      minWidth: 88,
    },
    {
      field: 'SOType',
      title: '销售类型',
      minWidth: 84,
    },
    {
      field: 'SoldToParty',
      title: '售往',
      minWidth: 88,
      slots: { default: 'soldto' },
    },
    {
      field: 'ShipToParty',
      title: '运往',
      minWidth: 88,
      slots: { default: 'shipto' },
    },
    {
      field: 'ShippingPoint',
      title: '装运点',
      minWidth: 64,
      visible: false,
    },
    {
      field: 'Urgent',
      title: '紧急程度',
      minWidth: 72,
      slots: { default: 'urgent' },
    },
    {
      field: 'BusinessPartnerName1',
      title: '收货人',
      minWidth: 136,
    },
    {
      field: 'DeliveryDate',
      title: '计划发货',
      minWidth: 88,
      formatter: 'formatDate',
    },
    {
      field: 'TotalQty',
      title: '总件数',
      minWidth: 88,
    },
    {
      field: 'ItemsCount',
      title: 'Item项',
      minWidth: 60,
    },
    {
      field: 'CreateDate',
      title: '创建日期',
      minWidth: 136,
      formatter: 'formatDateTime',
      sortable: true,
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
    {
      field: 'UpdateDate',
      title: '更新日期',
      minWidth: 136,
      formatter: 'formatDateTime',
      sortable: true,
      filters: [{ data: { type: '0', date: [] } }],
      filterRender: { name: 'FilterDateRange' },
    },
  ],
  expandConfig: {
    accordion: true,
  },
  filterConfig: {
    remote: true,
  },
  height: 'auto',
  proxyConfig: {
    autoLoad: false,
    ajax: {
      query: ({ page, sorts }) => {
        const queryParams: any = Object.assign(
          { inUsed: 0, YUB: data.value.YUB },
          page,
        );
        // 处理排序条件
        if (sorts.length === 0) {
          queryParams.sort = '_Identify desc';
        } else {
          const sortItem = sorts.map((item) => {
            const newItem = `${item.field} ${item.order}`;
            return newItem;
          });
          queryParams.sort = sortItem.join(',');
        }
        return getOutboundList(queryParams);
      },
    },
    seq: true,
  },
  /* scrollY: { enabled: true, gt: 0 }, */
  size: 'mini',
  sortConfig: {
    remote: true,
  },
  toolbarConfig: {
    slots: {
      buttons: 'toolbar_left',
    },
  },
});
const itemGridOptions = reactive<VxeGridProps>({
  columns: [
    { type: 'seq', width: 48 },
    {
      field: 'DeliveryDocumentItem',
      title: '项号',
      width: 88,
    },
    { field: 'Plant', title: '工厂编码', width: 72 },
    {
      field: 'StorageLocation',
      title: '库存地点',
      width: 72,
    },

    {
      field: 'Material',
      title: '产品编号',
      width: 100,
      slots: { default: 'skucode' },
    },
    {
      field: 'MaterialIsBatchManaged',
      title: '批次管理',
      width: 72,
      align: 'center',
      slots: { default: 'batchmanage' },
    },
    { field: 'Batch', title: '批次', width: 64 },
    { field: 'DeliveryQuantity', title: '指令数量', minWidth: 72 },
    { field: 'PickUpQty', title: '拣货数量', minWidth: 72 },
    { field: 'DeliveryQuantityUnit', title: '单位', width: 48 },
    { field: 'ItemGrossWeight', title: '毛重', width: 80 },
    { field: 'ItemNetWeight', title: '净重', width: 80 },
    { field: 'ItemWeightUnit', title: '重量单位', width: 72 },
    { field: 'ItemVolume', title: '体积', width: 80 },
    { field: 'ItemVolumeUnit', title: '体积单位', width: 72 },
    { field: 'DistributionChannel', title: '分销渠道', width: 72 },
    { field: 'Division', title: 'Division', width: 72 },
    { field: 'GoodsMovementType', title: '移动类型', width: 72 },
    { field: 'ReferenceSDDocument', title: '销售订单', width: 88 },
    { field: 'ReferenceSDDocumentItem', title: '销售项号', width: 72 },
    {
      field: 'ZZ1_Customer_referenc1_DLI',
      title: '采购订单',
      minWidth: 108,
    },
    {
      field: 'ZZ1_Customer_PO_Type_DLI',
      title: '直流订单',
      width: 72,
    },
  ],
  customConfig: {
    storage: false,
  },
  menuConfig: {
    body: {
      options: [
        [
          {
            className: 'rounded-md text-black text-sm pt-1 pb-1',
            code: 'menuCopy',
            disabled: false,
            name: '复制单元格',
            prefixConfig: {
              icon: 'vxe-icon-copy',
            },
            visible: true,
          },
        ],
      ],
    },
    className: 'font-medium shadow rounded-md',
  },
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    enabled: false,
  },
  rowConfig: {
    useKey: true,
    keyField: 'Guid',
  },
  rowStyle({ row }) {
    if (row.Qty === 0) {
      return {
        backgroundColor: '#fff4e6',
        color: '#222',
      };
    }
  },
  size: 'mini',
  toolbarConfig: {
    enabled: false,
  },
  treeConfig: {
    transform: true,
    rowField: 'ItemNumber',
    parentField: 'ParentItem',
    showLine: true,
  },
});
const [ItemGrid, itemGridApi] = useVbenVxeGrid({
  gridOptions: itemGridOptions,
});
const gridEvents: VxeGridListeners = {
  async toggleRowExpand({ expanded, row }) {
    await handleItemData([]);
    if (expanded) {
      handleItemLoading(true);
      const itemData = await getOutboundItem(row.Guid);
      await handleHeadRow(row);
      await handleItemData(itemData);
      handleItemLoading(false);
      itemGridApi.grid?.setAllTreeExpand(true);
    }
  },
};
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions, gridEvents });
function handleHeadRow(row: any) {
  gridApi.grid.setCurrentRow(row);
}
function handleItemData(data: any) {
  itemGridApi.setGridOptions({
    data,
  });
}
function handleItemLoading(isLoading: boolean) {
  itemGridApi.setLoading(isLoading);
}
function handleImport() {
  const currRow = gridApi.grid.getRadioRecord();
  if (!currRow) {
    message.info(`请勾选需要绑定的Outbound`);
    return;
  }
  modalApi.setState({ confirmLoading: true });
  modalApi.setState({ loading: true });
  bwOutPickingOutboundBinding(data.value.Guid, currRow.Guid)
    .then(() => {
      handleClose();
    })
    .catch(() => {})
    .finally(() => {
      modalApi.setState({ loading: false });
      modalApi.setState({ confirmLoading: false });
    });
}
function handleClose() {
  modalApi.close();
  emit('success');
}
/* function searchEvent(field: string, type: string) {
  const column = gridApi.grid.getColumnByField(field);
  if (column) {
    // 修改第一个选项为勾选状态
    const option = column.filters[0];
    if (!option) {
      return;
    }
    if (searchField[field]) {
      option.data = searchField[field];
      option.value = option.data;
      option.checked = true;
    } else {
      if (type) {
        option.data = undefined;
        option.checked = false;
      }
    }
    // 修改条件之后，需要手动调用 updateData 处理表格数据
    gridApi.grid.updateData();
    gridApi.grid.commitProxy('query');
  }
}
function clearFilterAndSort() {
  gridApi.grid.clearFilter();
  gridApi.grid.clearSort();
  gridApi.grid.commitProxy('query');
} */
function fetch() {
  setTimeout(() => {
    gridApi.grid.commitProxy('query');
  }, 200);
}
</script>
<template>
  <Modal>
    <Grid>
      <template #toolbar_left>
        <div class="hidden pl-1 text-base font-medium md:block">
          <span class="mr-2">{{ data.YUB }}</span>
        </div>
        <!-- <a-button class="mr-1" @click="clearFilterAndSort()">
          <AntClear class="size-5" />
        </a-button>
        <div class="hidden pl-1 md:block">
          <a-input-search
            v-model:value="searchField.DeliveryNumber"
            allow-clear
            class="mr-2 w-44"
            placeholder="Inbound"
            @search="searchEvent('DeliveryNumber', 'Input')"
          />
          <a-input-search
            v-model:value="searchField.LorealDocument"
            allow-clear
            class="mr-2 w-44"
            placeholder="采购订单"
            @search="searchEvent('LorealDocument', 'Input')"
          />
        </div> -->
      </template>
      <template #toolbar-tools> </template>
      <template #expand_content>
        <div>
          <ItemGrid>
            <template #skucode="{ row }">
              <a
                v-if="row.Material"
                class="text-blue-500"
                @click="openSkuDrawer(row.Material)"
              >
                {{ row.Material }}
              </a>
            </template>
            <template #batchmanage="{ row }">
              <Tag :color="row.MaterialIsBatchManaged ? 'green' : 'red'">
                {{ row.MaterialIsBatchManaged ? '是' : '否' }}
              </Tag>
            </template>
          </ItemGrid>
        </div>
      </template>
      <template #urgent="{ row }">
        <Tag :color="row.Urgent ? 'orange' : 'blue'">
          {{ row.Urgent ? '紧急' : '普通' }}
        </Tag>
      </template>
      <template #soldto="{ row }">
        <a-button
          size="small"
          type="link"
          @click="openCustomerDrawer(row.SoldToParty)"
        >
          {{ row.SoldToParty }}
        </a-button>
      </template>
      <template #shipto="{ row }">
        <a-button
          size="small"
          type="link"
          @click="openCustomerDrawer(row.ShipToParty)"
        >
          {{ row.ShipToParty }}
        </a-button>
      </template>
    </Grid>
    <CustomerDrawer />
    <SkuDrawer />
  </Modal>
</template>
